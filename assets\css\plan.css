.pricingTable {
  margin: 40px auto;
}
.pricingTable > .pricingTable-title {
  text-align: center;
  color: #6e768d;
  font-size: 3em;
  font-size: 300%;
  margin-bottom: 20px;
  letter-spacing: 0.04em;
}
.pricingTable > .pricingTable-subtitle {
  text-align: center;
  color: #b4bdc6;
  font-size: 1.8em;
  letter-spacing: 0.04em;
  margin-bottom: 60px;
}
@media screen and (max-width: 480px) {
  .pricingTable > .pricingTable-subtitle {
    margin-bottom: 30px;
  }
}
.pricingTable-firstTable {
  list-style: none;
  padding-left: 2em;
  padding-right: 2em;
  text-align: center;
}
.pricingTable-firstTable_table {
  vertical-align: middle;
  width: 31%;
  background-color: #ffffff;
  display: inline-block;
  padding: 0px 30px 40px;
  text-align: center;
  max-width: 320px;
  transition: all 0.3s ease;
  border-radius: 5px;
  border: 1px solid;
}
@media screen and (max-width: 767px) {
  .pricingTable-firstTable_table {
    display: block;
    width: 90%;
    margin: 0 auto;
    max-width: 90%;
    margin-bottom: 20px;
    padding: 10px;
    padding-left: 20px;
    /* border: 220px; */
    border: 1px solid;
  }
}
@media screen and (max-width: 767px) {
  .pricingTable-firstTable_table > * {
    display: inline-block;
    vertical-align: middle;
  }
}
@media screen and (max-width: 480px) {
  .pricingTable-firstTable_table > * {
    display: block;
    float: none;
  }
}
@media screen and (max-width: 767px) {
  .pricingTable-firstTable_table:after {
    display: table;
    content: "";
    clear: both;
  }
}
.pricingTable-firstTable_table:hover {
  transform: scale(1.08);
}
@media screen and (max-width: 767px) {
  .pricingTable-firstTable_table:hover {
    transform: none;
    border: 1px solid;
  }
}
.pricingTable-firstTable_table:not(:last-of-type) {
  margin-right: 3.5%;
  border: 1px solid;
}
@media screen and (max-width: 767px) {
  .pricingTable-firstTable_table:not(:last-of-type) {
    margin-right: auto;
  }
}
.pricingTable-firstTable_table:nth-of-type(2) {
  position: relative;
}
@media screen and (max-width: 767px) {
  .pricingTable-firstTable_table:nth-of-type(2) h1 {
    padding-top: 8%;
  }
}
.pricingTable-firstTable_table:nth-of-type(2):before {
  content: "Most Popular";
  position: absolute;
  color: white;
  display: block;
  background-color: #3bbdee;
  text-align: center;
  right: 15px;
  top: -25px;
  height: 65px;
  width: 65px;
  border-radius: 50%;
  box-sizing: border-box;
  font-size: 0.5em;
  padding-top: 22px;
  text-transform: uppercase;
  letter-spacing: 0.13em;
  transition: all 0.5s ease;
}
@media screen and (max-width: 988px) {
  .pricingTable-firstTable_table:nth-of-type(2):before {
    font-size: 0.6em;
  }
}
@media screen and (max-width: 767px) {
  .pricingTable-firstTable_table:nth-of-type(2):before {
    left: 10px;
    width: 45px;
    height: 45px;
    top: 2px;
    padding-top: 13px;
  }
}
@media screen and (max-width: 480px) {
  .pricingTable-firstTable_table:nth-of-type(2):before {
    font-size: 0.4em;
  }
}
.pricingTable-firstTable_table:nth-of-type(2):hover:before {
  transform: rotate(360deg);
}
.pricingTable-firstTable_table__header {
  font-size: 1.6em;
  padding: 40px 0px;
  border-bottom: 2px solid #ebedec;
  letter-spacing: 0.03em;
}
@media screen and (max-width: 1068px) {
  .pricingTable-firstTable_table__header {
    font-size: 1.45em;
  }
}
@media screen and (max-width: 767px) {
  .pricingTable-firstTable_table__header {
    padding: 0px;
    border-bottom: none;
    float: left;
    width: 33%;
    padding-top: 3%;
    padding-bottom: 2%;
  }
}
@media screen and (max-width: 610px) {
  .pricingTable-firstTable_table__header {
    font-size: 1.3em;
  }
}
@media screen and (max-width: 480px) {
  .pricingTable-firstTable_table__header {
    float: none;
    width: 100%;
    font-size: 1.8em;
    margin-bottom: 5px;
  }
}
.pricingTable-firstTable_table__pricing {
  font-size: 3em;
  padding: 30px 0px;
  border-bottom: 2px solid #ebedec;
  line-height: 0.7;
}
@media screen and (max-width: 1068px) {
  .pricingTable-firstTable_table__pricing {
    font-size: 2.8em;
  }
}
@media screen and (max-width: 767px) {
  .pricingTable-firstTable_table__pricing {
    border-bottom: none;
    padding: 0;
    float: left;
    clear: left;
    width: 33%;
  }
}
@media screen and (max-width: 610px) {
  .pricingTable-firstTable_table__pricing {
    font-size: 2.4em;
  }
}
@media screen and (max-width: 480px) {
  .pricingTable-firstTable_table__pricing {
    float: none;
    width: 100%;
    font-size: 3em;
    margin-bottom: 10px;
  }
}
.pricingTable-firstTable_table__pricing span:first-of-type {
  font-size: 0.35em;
  vertical-align: top;
  letter-spacing: 0.15em;
}
@media screen and (max-width: 1068px) {
  .pricingTable-firstTable_table__pricing span:first-of-type {
    font-size: 0.3em;
  }
}
.pricingTable-firstTable_table__pricing span:last-of-type {
  vertical-align: bottom;
  font-size: 0.3em;
  letter-spacing: 0.04em;
  padding-left: 0.2em;
}
@media screen and (max-width: 1068px) {
  .pricingTable-firstTable_table__pricing span:last-of-type {
    font-size: 0.25em;
  }
}
.pricingTable-firstTable_table__options {
  list-style: none;
  padding: 15px;
  text-align: left;
  font-size: 0.9em;
  border-bottom: 2px solid #ebedec;
}
@media screen and (max-width: 1068px) {
  .pricingTable-firstTable_table__options {
    font-size: 0.85em;
  }
}
@media screen and (max-width: 767px) {
  .pricingTable-firstTable_table__options {
    border-bottom: none;
    padding: 0;
    margin-right: 10%;
  }
}
@media screen and (max-width: 610px) {
  .pricingTable-firstTable_table__options {
    font-size: 0.7em;
    margin-right: 8%;
  }
}
@media screen and (max-width: 480px) {
  .pricingTable-firstTable_table__options {
    font-size: 1.3em;
    margin-right: none;
    margin-bottom: 10px;
  }
}
.pricingTable-firstTable_table__options > li {
  padding: 8px 0px;
}
@media screen and (max-width: 767px) {
  .pricingTable-firstTable_table__options > li {
    text-align: left;
  }
}
@media screen and (max-width: 610px) {
  .pricingTable-firstTable_table__options > li {
    padding: 5px 0;
  }
}
@media screen and (max-width: 480px) {
  .pricingTable-firstTable_table__options > li {
    text-align: center;
  }
}
.pricingTable-firstTable_table__options > li:before {
  content: "✓";
  display: inline-flex;
  margin-right: 15px;
  color: white;
  background-color: #ff565b;
  border-radius: 50%;
  width: 15px;
  height: 15px;
  font-size: 0.8em;
  padding: 2px;
  align-items: center;
  justify-content: center;
}
@media screen and (max-width: 1068px) {
  .pricingTable-firstTable_table__options > li:before {
    width: 14px;
    height: 14px;
    padding: 1.5px;
  }
}
@media screen and (max-width: 767px) {
  .pricingTable-firstTable_table__options > li:before {
    width: 12px;
    height: 12px;
  }
}
.pricingTable-firstTable_table__getstart {
  color: white;
  border: 0;
  background-color: #ff565b;
  margin-top: 30px;
  border-radius: 5px;
  cursor: pointer;
  padding: 15px;
  box-shadow: 0px 3px 0px 0px #ff565b;
  letter-spacing: 0.07em;
  transition: all 0.4s ease;
}
@media screen and (max-width: 1068px) {
  .pricingTable-firstTable_table__getstart {
    font-size: 0.95em;
  }
}
@media screen and (max-width: 767px) {
  .pricingTable-firstTable_table__getstart {
    margin-top: 0;
  }
}
@media screen and (max-width: 610px) {
  .pricingTable-firstTable_table__getstart {
    font-size: 0.9em;
    padding: 10px;
  }
}
@media screen and (max-width: 480px) {
  .pricingTable-firstTable_table__getstart {
    font-size: 1em;
    /* width: 50%; */
    margin: 10px auto;
  }
}
.pricingTable-firstTable_table__getstart:hover {
  transform: translateY(-10px);
  box-shadow: 0px 40px 29px -19px rgba(245, 79, 57, 0.9);
}
@media screen and (max-width: 767px) {
  .pricingTable-firstTable_table__getstart:hover {
    transform: none;
    box-shadow: none;
  }
}
.pricingTable-firstTable_table__getstart:active {
  box-shadow: inset 0 0 10px 1px #66a564,
    0px 40px 29px -19px rgba(102, 172, 100, 0.95);
  transform: scale(0.95) translateY(-9px);
}
@media screen and (max-width: 767px) {
  .pricingTable-firstTable_table__getstart:active {
    transform: scale(0.95) translateY(0);
    box-shadow: none;
  }
}
/* body {
    font-family: 'Montserrat', sans-serif;
    font-size: 100%;
    background-color: #f0f4f7;
    color: #717787;
  } 
  @media screen and (max-width: 960px) {
    body {
      font-size: 80%;
    }
  }
  @media screen and (max-width: 776px) {
    body {
      font-size: 70%;
    }
  }
  @media screen and (max-width: 496px) {
    body {
      font-size: 50%;
    }
  }
  @media screen and (max-width: 320px) {
    body {
      font-size: 40%;
    }
  }
  * {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
  }*/
/* Subscription plan styles */
.pricingTable-firstTable_table {
  position: relative;
  transition: all 0.3s ease;
}

.pricingTable-firstTable_table.active-plan {
  border: 2px solid #28a745;
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.2);
  transform: scale(1.02);
}

.current-plan-badge {
  position: absolute;
  top: -10px;
  right: -10px;
  background-color: #28a745;
  color: white;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  z-index: 1;
}

/* Subscription plan button styles */
.pricingTable-firstTable_table__getstart {
  position: relative;
  display: block;
  margin: 0 auto;
  padding: 10px;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 5px;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pricingTable-firstTable_table__getstart:not(.disabled-btn):hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}

.pricingTable-firstTable_table__getstart.disabled-btn {
  background-color: #6c757d !important;
  border-color: #6c757d !important;
  cursor: not-allowed;
  opacity: 0.65;
}

/* Add a checkmark icon to subscribed plans */
.pricingTable-firstTable_table__getstart.disabled-btn::before {
  content: "✓ ";
  font-weight: bold;
}

/* Style for "Subscribe Again" button */
.plan-btn:not(.disabled-btn) {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.plan-btn:not(.disabled-btn):hover {
  background-color: #0069d9;
  border-color: #0062cc;
}

/* Payment success modal */
#paymentSuccessModal .modal-header {
  background-color: #28a745;
  color: white;
}

#paymentSuccessModal .modal-body {
  padding: 20px;
  text-align: center;
}

#paymentSuccessModal .modal-footer {
  justify-content: center;
}

/* Subscription confirmation modal */
#subscriptionConfirmModal .modal-header {
  background-color: #ffc107;
  color: #212529;
}

#subscriptionConfirmModal .modal-body {
  padding: 20px;
}

#subscriptionConfirmModal .btn-danger {
  background-color: #dc3545;
  border-color: #dc3545;
}

#subscriptionConfirmModal .btn-success {
  background-color: #28a745;
  border-color: #28a745;
}
