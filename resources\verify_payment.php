<?php

include 'db_config/db_connection.php';
include "../scripts/config/encryption-handlers.php";
session_start();

$curl = curl_init();

$ref = $_GET["reference"];
$save = $_GET["save"];
$email = $_GET["email"];
$payment = $_GET["payment"];

curl_setopt_array($curl, array(
    CURLOPT_URL => "https://api.paystack.co/transaction/verify/$ref",
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => "",
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => "GET",
    CURLOPT_HTTPHEADER => array(
        "Authorization: Bearer sk_test_da3e0e887b69f7342464f89a6656150a09d676a0",
        "Cache-Control: no-cache",
    ),
));

$response = curl_exec($curl);
$err = curl_error($curl);

curl_close($curl);

if ($err) {
    echo "cURL Error #:" . $err;
} else {
    // echo $response;
    $response = json_decode($response, true);
    if ($response["status"] == true) {
        $amount = $response["data"]["amount"];

        $query = "SELECT * FROM users WHERE email = '$email'";
        $result = mysqli_query($db, $query);
        $ans = mysqli_fetch_assoc($result);
        $user_id = $ans['id'];
        if ($save == 1) {
            $auth = $response["data"]["authorization"];
            // var_dump($auth);
            $last_four = $auth["last4"];
            $exp_month =  $auth["exp_month"];
            $exp_year =  $auth["exp_year"];
            $card_type =  $auth["card_type"];
            $authorization_code =  $auth["authorization_code"];

            $query = "INSERT INTO payment_card(user_id, card_no, exp_month, exp_year, card_type, auth_code, add_reference) VALUES ($user_id, $last_four, $exp_month, $exp_year, '$card_type', '$authorization_code', '$ref')";
            $result = mysqli_query($db, $query);

            if ($payment == 0) {
                // ! reverse the nigga's money ---------------------------
                $url = "https://api.paystack.co/refund";
                $fields = [
                    'transaction' => $ref,
                ];
                $fields_string = http_build_query($fields);

                //open connection
                $ch = curl_init();

                //set the url, number of POST vars, POST data
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $fields_string);
                curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                    "Authorization: Bearer sk_test_da3e0e887b69f7342464f89a6656150a09d676a0",
                    "Cache-Control: no-cache",
                ));
                //So that curl_exec returns the contents of the cURL; rather than echoing it
                // curl_setopt($ch,CURLOPT_RETURNREFUND, true);   

                //execute post
                $response = curl_exec($ch);
                // $response = json_decode($response, true);
                // do thangs with response

                $err = curl_error($ch);
                curl_close($ch);

                if ($err) {
                    echo "cURL Error #:" . $err;
                } else {
                    // echo "successful";
                }
                // ! reversal end ---------------------------
            }
        }

        if ($payment == 1) {
            $cart = $_SESSION["cart"];
            // var_dump($cart);

            foreach ($cart as $cart_item) {
                $product_id = decrypt_data($cart_item["id"]);

                $query = "INSERT INTO puchase_history(img_id, user_id, amount, payment_method, payment_status, comment, payment_ref) VALUES($product_id, $user_id, $amount,  'Card' , 1, 'Payment made successfully' ,'$ref')";
                $result = mysqli_query($db, $query);
            }
            //get valus in cart
            unset($_SESSION["cart"]);
        }
    }

    echo "successful";


    // var_dump($response["authorization"]);
}
