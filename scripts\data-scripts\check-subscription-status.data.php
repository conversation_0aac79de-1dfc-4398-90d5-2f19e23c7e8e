<?php
header("Content-Type: application/json");
session_start();

include '../class-scripts/db-connection.class.php';
include '../class-scripts/user-auth.class.php';
include '../class-scripts/checkSubscription.class.php';

if (isset($_POST['user_id'])) {
    $user_id = $_POST['user_id'];

    $UserLoginObj = new UserAuth();
    $result = $UserLoginObj->checkSubscription($user_id);
    echo $result;
} else {
    $responseBody = array(
        "status" => 0,
        "message" => "Invalid Request"
    );
    echo (json_encode($responseBody));
}
