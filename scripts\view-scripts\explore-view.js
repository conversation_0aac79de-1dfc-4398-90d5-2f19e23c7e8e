$(document).ready(function () {
  /* let inputLabel = document.getElementById("getValue");
  inputLabel.style.visibility = 'hidden'; */
  //Run Codes When Document Loads Completely

  // ! show collection loader Icon
  // enableLoader();

  // get catagory id if available
  const queryString = window.location.search;
  const urlParams = new URLSearchParams(queryString);
  const categoryId = urlParams.get("category");
  // console.log(categoryId);

  var path = window.location.pathname;
  console.log(path);
  var split = path.split("/");
  console.log(split[2]);

  function exploreWithcategoryId(offset) {
    $.get(
      "scripts/data-scripts/explore.data.php",
      { offset: offset, categoryId: categoryId },
      function (return_data) {
        // assign Callback Function Return Data to Variable for Usability
        var return_data_value = return_data;

        // console.log(return_data_value["id"]);

        if (return_data_value["message"] != "error") {
          if (return_data_value["message"] != "no image to explore") {
            // ! hide collections view
            // disableLoader();

            createExploreItemsViewByLoop(return_data_value);
          } else {
            // ! no collections available
            // disableLoader();
          }
        } else {
          // ! server error
          // disableLoader();
        }
      }
    );
  }

  if (split[2] == "explore.php") {
    //This lazy load for the specific explorer page.

    console.log(window.location.href);

    var isLoading = false;
    var offset = 0;

    function loadExplore() {
      if (isLoading) return;

      isLoading = true;

      if (categoryId == null || categoryId == "") {
        $.get(
          "scripts/data-scripts/explore.data.php",
          { offset: offset },
          function (return_data) {
            // assign Callback Function Return Data to Variable for Usability
            var return_data_value = return_data;

            // console.log(return_data_value["id"]);

            if (return_data_value["message"] != "error") {
              createExploreItemsViewByLoop(return_data_value);
              if (return_data_value["message"] != "no image to explore") {
                // ! hide collections view
                // disableLoader();
              } else {
                // ! no collections available
                // disableLoader();
              }
            } else {
              // ! server error
              // disableLoader();
            }

            offset += response.length;
            isLoading = false;
          }
        );
      } else {
        exploreWithcategoryId(offset);
      }
    }
    // Implement lazy loading as the user scrolls

    loadExplore();

    $(window).scroll(function () {
      // var windowHeight = $(window).height();
      // var documentHeight = $(document).height();
      // var windowWidth = $(window).width();
      // var scrollPosition = $(window).scrollTop() + windowHeight;

      if (
        $(window).scrollTop() + $(window).height() >=
        $(document).height() - 700
      ) {
        // $(".members_card_gen").slideDown(700);
        loadExplore().slideDown(1000);
      }

      // if (scrollPosition >= documentHeight && windowWidth >= 768) {
      //   loadUsers();
      // }
    });
  } else {
    $.get("scripts/data-scripts/explore.data.php", function (return_data) {
      // assign Callback Function Return Data to Variable for Usability
      var return_data_value = return_data;

      // console.log(return_data_value["id"]);

      if (return_data_value["message"] != "error") {
        if (return_data_value["message"] != "no image to explore") {
          // ! hide collections view
          // disableLoader();

          createExploreItemsViewByLoop(return_data_value);
        } else {
          // ! no collections available
          // disableLoader();
        }
      } else {
        // ! server error
        // disableLoader();
      }
    });
  }

  function createExploreItemsViewByLoop(data) {
    // var exploreItem = '';

    data.forEach((element) => {
      //console.log(element);

      //imgCompress(`peco_image_store/explore/${element['thumbnail']}`).then((dataURL) => {

      //removed as imgCompress isn't to be used in code
      //<img src=${dataURL} alt="" onerror="this.style.display='none'" />

      //Check if session is logged in or not
      $.post(
        "scripts/data-scripts/explore.data.php",
        { photoId: element["id"] },
        function (return_data) {
          var result = return_data;
          // console.log(result);

          if (result["status"] == "inactive") {
            // Display default Explore photos if user not logged in

            $("#explore-items").append(`
                <div class="grid">
                  <img src="${`peco_image_store/thumbnail/${element["thumbnail"]}`}" alt="" onerror="this.style.display='none'" />
                  <div class="grid__body">
                    <div class="relative">
                      <a class="grid__link details-link" href="" data-link="${
                        element["id"]
                      }" ></a>
                      <a href="" class="details-link grid__tag" data-link="${
                        element["id"]
                      }">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#ff565b" viewBox="-100 0 700 512">
                  <path d="M562.8 267.7c56.5-56.5 56.5-148 0-204.5c-50-50-128.8-56.5-186.3-15.4l-1.6 1.1c-14.4 10.3-17.7 30.3-7.4 44.6s30.3 17.7 44.6 7.4l1.6-1.1c32.1-22.9 76-19.3 103.8 8.6c31.5 31.5 31.5 82.5 0 114L405.3 334.8c-31.5 31.5-82.5 31.5-114 0c-27.9-27.9-31.5-71.8-8.6-103.8l1.1-1.6c10.3-14.4 6.9-34.4-7.4-44.6s-34.4-6.9-44.6 7.4l-1.1 1.6C189.5 251.2 196 330 246 380c56.5 56.5 148 56.5 204.5 0L562.8 267.7zM43.2 244.3c-56.5 56.5-56.5 148 0 204.5c50 50 128.8 56.5 186.3 15.4l1.6-1.1c14.4-10.3 17.7-30.3 7.4-44.6s-30.3-17.7-44.6-7.4l-1.6 1.1c-32.1 22.9-76 19.3-103.8-8.6C57 372 57 321 88.5 289.5L200.7 177.2c31.5-31.5 82.5-31.5 114 0c27.9 27.9 31.5 71.8 8.6 103.9l-1.1 1.6c-10.3 14.4-6.9 34.4 7.4 44.6s34.4 6.9 44.6-7.4l1.1-1.6C416.5 260.8 410 182 360 132c-56.5-56.5-148-56.5-204.5 0L43.2 244.3z"/></svg>
                  </a>
                    <a href="" class="bookmark like-button grid__tag like_btn__tag_p" data-like="${
                      element["id"]
                    }">
                  <svg class="heart-icon stroke" viewBox="0 0 24 24">
                    <defs>
                    <clipPath id="mask">
                    <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z"/>
                    </clipPath>
                    </defs>
                    <circle id="first-stroke" r="0" cx="12" cy="12" clip-path="url(#mask)"></circle>
                    <circle id="second-stroke" r="0" cx="12" cy="12" clip-path="url(#mask)"></circle>
                  </svg> 
                  <svg class="heart-icon red"  viewBox="0 0 24 24">
                    <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z"/>
                  </svg>
                  <svg class="heart-icon" viewBox="0 0 24 24">
                    <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z"/>
                  </svg>
                  </a></p>
                    </div>
                    <div class="mt-auto" >
                      <span class="grid__tag">${element["title"]}</span>
                    </div>
                  </div>
                </div>
                <!-- End Gallery Item -->`);
          } else if (result["status"] == "active") {
            // Check if user have liked specific photos to be displayed on the explore page so as to change the heart icon to liked and vise-versa.

            if (result["liked"] == "yes") {
              $("#explore-items").append(`
                  <div class="grid">
                    <img src="${`peco_image_store/thumbnail/${element["thumbnail"]}`}" alt="" onerror="this.style.display='none'" />
                    <div class="grid__body">
                      <div class="relative">
                        <a class="grid__link details-link" href="" data-link="${
                          element["id"]
                        }" ></a>
                        <a href="" class="details-link" data-link="${
                          element["id"]
                        }">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#ff565b" viewBox="0 0 640 512">
                    <path d="M562.8 267.7c56.5-56.5 56.5-148 0-204.5c-50-50-128.8-56.5-186.3-15.4l-1.6 1.1c-14.4 10.3-17.7 30.3-7.4 44.6s30.3 17.7 44.6 7.4l1.6-1.1c32.1-22.9 76-19.3 103.8 8.6c31.5 31.5 31.5 82.5 0 114L405.3 334.8c-31.5 31.5-82.5 31.5-114 0c-27.9-27.9-31.5-71.8-8.6-103.8l1.1-1.6c10.3-14.4 6.9-34.4-7.4-44.6s-34.4-6.9-44.6 7.4l-1.1 1.6C189.5 251.2 196 330 246 380c56.5 56.5 148 56.5 204.5 0L562.8 267.7zM43.2 244.3c-56.5 56.5-56.5 148 0 204.5c50 50 128.8 56.5 186.3 15.4l1.6-1.1c14.4-10.3 17.7-30.3 7.4-44.6s-30.3-17.7-44.6-7.4l-1.6 1.1c-32.1 22.9-76 19.3-103.8-8.6C57 372 57 321 88.5 289.5L200.7 177.2c31.5-31.5 82.5-31.5 114 0c27.9 27.9 31.5 71.8 8.6 103.9l-1.1 1.6c-10.3 14.4-6.9 34.4 7.4 44.6s34.4 6.9 44.6-7.4l1.1-1.6C416.5 260.8 410 182 360 132c-56.5-56.5-148-56.5-204.5 0L43.2 244.3z"/></svg>
                    </a>
                    <a href="" class="bookmark like-button" data-like="${
                      element["id"]
                    }">
                    <svg class="heart-icon stroke" viewBox="0 0 24 24">
                      <defs>
                      <clipPath id="mask">
                      <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z"/>
                      </clipPath>
                      </defs>
                      <circle id="first-stroke" r="0" cx="12" cy="12" clip-path="url(#mask)"></circle>
                      <circle id="second-stroke" r="0" cx="12" cy="12" clip-path="url(#mask)"></circle>
                    </svg> 
                    <svg class="heart-icon red"  viewBox="0 0 24 24">
                      <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z"/>
                    </svg>
                    <svg class="heart-icon" viewBox="0 0 24 24">
                      <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z"/>
                    </svg>
                    </a>
                      </div>
                      <div class="mt-auto" >
                        <span class="grid__tag">${element["tag"]}</span>
                      </div>
                    </div>
                  </div>
                  <!-- End Gallery Item -->`);

              // const curLikeId = $('.like-button').index(this);

              // var specificElement = $('.like-button').eq(curLikeId);

              // console.log(curLikeId);

              // console.log(specificElement);

              // specificElement.html(`
              //   <svg class="heart-icon stroke" viewBox="0 0 24 24">
              //   <defs>
              //   <clipPath id="mask">
              //     <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z"/>
              //   </clipPath>
              //   </defs>
              //   <circle id="first-stroke" r="0" cx="12" cy="12" clip-path="url(#mask)"></circle>
              //   <circle id="second-stroke" r="0" cx="12" cy="12" clip-path="url(#mask)"></circle>
              //   </svg>
              //   <svg class="heart-icon red" style="transform:initial"  viewBox="0 0 24 24">
              //     <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z"/>
              //   </svg>
              //   <svg class="heart-icon" viewBox="0 0 24 24">
              //     <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z"/>
              //   </svg>
              // `);
            } else {
              $("#explore-items").append(`
                  <div class="grid">
                    <img src="${`peco_image_store/thumbnail/${element["thumbnail"]}`}" alt="" onerror="this.style.display='none'" />
                    <div class="grid__body">
                      <div class="relative">
                        <a class="grid__link details-link" href="" data-link="${
                          element["id"]
                        }" ></a>
                        <a href="" class="details-link" data-link="${
                          element["id"]
                        }">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#ff565b" viewBox="0 0 640 512">
                    <path d="M562.8 267.7c56.5-56.5 56.5-148 0-204.5c-50-50-128.8-56.5-186.3-15.4l-1.6 1.1c-14.4 10.3-17.7 30.3-7.4 44.6s30.3 17.7 44.6 7.4l1.6-1.1c32.1-22.9 76-19.3 103.8 8.6c31.5 31.5 31.5 82.5 0 114L405.3 334.8c-31.5 31.5-82.5 31.5-114 0c-27.9-27.9-31.5-71.8-8.6-103.8l1.1-1.6c10.3-14.4 6.9-34.4-7.4-44.6s-34.4-6.9-44.6 7.4l-1.1 1.6C189.5 251.2 196 330 246 380c56.5 56.5 148 56.5 204.5 0L562.8 267.7zM43.2 244.3c-56.5 56.5-56.5 148 0 204.5c50 50 128.8 56.5 186.3 15.4l1.6-1.1c14.4-10.3 17.7-30.3 7.4-44.6s-30.3-17.7-44.6-7.4l-1.6 1.1c-32.1 22.9-76 19.3-103.8-8.6C57 372 57 321 88.5 289.5L200.7 177.2c31.5-31.5 82.5-31.5 114 0c27.9 27.9 31.5 71.8 8.6 103.9l-1.1 1.6c-10.3 14.4-6.9 34.4 7.4 44.6s34.4 6.9 44.6-7.4l1.1-1.6C416.5 260.8 410 182 360 132c-56.5-56.5-148-56.5-204.5 0L43.2 244.3z"/></svg>
                    </a>
                    <a href="" class="bookmark like-button" data-like="${
                      element["id"]
                    }">
                    <svg class="heart-icon stroke" viewBox="0 0 24 24">
                      <defs>
                      <clipPath id="mask">
                      <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z"/>
                      </clipPath>
                      </defs>
                      <circle id="first-stroke" r="0" cx="12" cy="12" clip-path="url(#mask)"></circle>
                      <circle id="second-stroke" r="0" cx="12" cy="12" clip-path="url(#mask)"></circle>
                    </svg> 
                    <svg class="heart-icon red"  viewBox="0 0 24 24">
                      <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z"/>
                    </svg>
                    <svg class="heart-icon" viewBox="0 0 24 24">
                      <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z"/>
                    </svg>
                    </a>
                      </div>
                      <div class="mt-auto" >
                        <span class="grid__tag">${element["tag"]}</span>
                      </div>
                    </div>
                  </div>
                  <!-- End Gallery Item -->`);
            }
          }
        }
      );

      //});
    });

    // $("#explore-items").html(exploreItem);

    $("body").on("click", ".like-button", function (event) {
      event.preventDefault();

      const dataId = $(this).attr("data-like");

      const curLikeId = $(".like-button").index(this);

      var explore_like_session = { els: "1" };

      //alert("This is the power of team work!");

      // console.log(dataId);

      $.post(
        "scripts/data-scripts/explore.data.php",
        { dataId: dataId },
        function (return_data) {
          var result = return_data;
          // console.log(result);
          if (result["status"] == "inactive") {
            window.location = "signup.php";
          } else if (result["status"] == "active") {
            if (result["liked"] == "yes") {
              var specificElement = $(".like-button").eq(curLikeId);

              // console.log(curLikeId);

              // console.log(specificElement);

              specificElement.html(`
                  <svg class="heart-icon stroke" viewBox="0 0 24 24">
                  <defs>
                  <clipPath id="mask">
                    <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z"/>
                  </clipPath>
                  </defs>
                  <circle id="first-stroke" r="0" cx="12" cy="12" clip-path="url(#mask)"></circle>
                  <circle id="second-stroke" r="0" cx="12" cy="12" clip-path="url(#mask)"></circle>
                  </svg> 
                  <svg class="heart-icon red" style="transform:initial"  viewBox="0 0 24 24">
                    <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z"/>
                  </svg>
                  <svg class="heart-icon" viewBox="0 0 24 24">
                    <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z"/>
                  </svg>
                `);
            } else if (result["liked"] == "no") {
              var specificElement = $(".like-button").eq(curLikeId);

              // console.log(curLikeId);

              // console.log(specificElement);

              specificElement.html(`
                  <svg class="heart-icon stroke" viewBox="0 0 24 24">
                  <defs>
                  <clipPath id="mask">
                    <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z"/>
                  </clipPath>
                  </defs>
                  <circle id="first-stroke" r="0" cx="12" cy="12" clip-path="url(#mask)"></circle>
                  <circle id="second-stroke" r="0" cx="12" cy="12" clip-path="url(#mask)"></circle>
                  </svg> 
                  <svg class="heart-icon red" viewBox="0 0 24 24">
                    <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z"/>
                  </svg>
                  <svg class="heart-icon" viewBox="0 0 24 24">
                    <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z"/>
                  </svg>
                `);
            }
          }
        }
      );
      //console.log("This is the power of team work!" + dataId);

      /* $.ajax({
            type: "POST",
            url: 'scripts/data-scripts/explore.data.php',       
            data: jQuery.param({ els: "1" }) ,
            contentType: 'application/x-www-form-urlencoded; charset=UTF-8',

            success: function(response)
            {
              console.log(response);

                var jsonData = JSON.parse(response);                        
                var results = jsonData.message;
                console.log(results);

              console.log("results out");
              
            },error: function(response){
              
              console.log(response);
              console.log("results failed");

            }
            
          });  */
    });

    $("body").on("click", ".details-link", function (event) {
      event.preventDefault();
      var dataId = $(this).attr("data-link");
      //check if user is logged in
      $.post(
        "scripts/data-scripts/auth-status.data.php",
        { request_type: "auth-session-check" },
        function (return_data) {
          if (return_data.message !== "error") {
            window.location = "photos.php?" + dataId;
          } else {
            window.location = "signup.php?login";
          }
        }
      );

      // console.log("This is the power of team work!" + dataId);
    });
  }

  $(".white-button a").click(function (e) {
    e.preventDefault();
    $.post(
      "scripts/data-scripts/auth-status.data.php",
      { request_type: "auth-session-check" },
      function (return_data) {
        if (return_data.message !== "error") {
          window.location = "plan.php";
        } else {
          window.location = "signup.php?login";
        }
      }
    );
  });

  // Reusable view functions
  // function disableLoader() {
  //   $("#collections-loader").css("display", "none");
  //   $("#collection").css("display", "block");
  // }
  // function enableLoader() {
  //   $("#collections-loader").css("display", "block");
  //   $("#collection").css("display", "none");
  // }

  // EXPLORE FILTER

  //check if user has paid for subscription---code written by emmanuel

  //   function check_user_subscription() {
  //     $.post(
  //       "scripts/data-scripts/plans.data.php",
  //       { request_type: "get-plans" },
  //       function (subscription_data) {
  //         console.log(subscription_data);

  //         if (subscription_data.plans && Array.isArray(subscription_data.plans)) {
  //           // Find the first subscribed plan
  //           let subscribedPlan = subscription_data.plans.find(
  //             (plan) => plan.is_subscribed === true
  //           );

  //           if (subscribedPlan) {
  //             // Hide the subscription plans section
  //             $("#subscription_plans").css("display", "none");

  //             // Display the plan amount
  //             $("#plan1").html(`Subscribed to (${subscribedPlan.plan_name})`);
  //           } else {
  //             $("#plan1").html("Subscribe Now");
  //           }
  //         }
  //       },
  //       "json"
  //     ); // Ensure the response is parsed as JSON
  //   }

  function check_user_subscription() {
    $.post(
      "scripts/data-scripts/plans.data.php",
      { request_type: "get-plans" },
      function (subscription_data) {
        console.log(subscription_data);

        if (subscription_data.plans && Array.isArray(subscription_data.plans)) {
          // Find the first subscribed plan
          let subscribedPlan = subscription_data.plans.find(
            (plan) => plan.is_subscribed === true
          );

          if (subscribedPlan) {
            // Hide the subscription plans section
            $("#subscription_plans").css("display", "none");

            // Display the plan name
            $("#plan1").html(
              `Subscribed to <strong>${subscribedPlan.plan_name}</strong>`
            );

            // Add a class to style it
            $("#plan1").addClass("subscribed-plan");
          } else {
            // Check if user was previously subscribed to any plan
            let previouslySubscribed = subscription_data.plans.some(
              (plan) => plan.was_subscribed === true
            );

            if (previouslySubscribed) {
              $("#plan1").html("Renew Subscription");
            } else {
              $("#plan1").html("Subscribe Now");
            }

            // Remove the class if it exists
            $("#plan1").removeClass("subscribed-plan");
          }
        }
      },
      "json"
    ); // Ensure the response is parsed as JSON
  }

  check_user_subscription();
});

/* function Like_click(event){
     //alert("The paragraph was clicked.");
       event.preventDefault()
     $.post("scripts/data-scripts/explore.data.php", { explore_like_session: "" }, function (return_session) {
       var login_return_session_value = return_session;
       //login_return_session_value = JSON.parse (login_return_session_value)
 
       console.log(login_return_session_value["message"]);
           if (login_return_session_value['message'] == "session_error") {
             window.location = "\\***********\Pecophotos\signup.php"
 
           }
 
     }
     );
 
   };
 
 
   function icon_link(event){
     //alert("The paragraph was clicked.");
       event.preventDefault()
     $.post("scripts/data-scripts/explore.data.php", { explore_link_icon: "" }, function (return_icon_response) {
       var icon_response = return_icon_response;
       //icon_response = JSON.parse(icon_response);
 
       console.log(icon_response["id"]);
             //window.location = "photos.php" + element['id']
 
           
 
     }
     );
 
   }; */
