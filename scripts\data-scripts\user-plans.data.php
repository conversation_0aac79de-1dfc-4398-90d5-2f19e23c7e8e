<?php

// header("Content-Type: application/json");
// session_start();

// include '../class-scripts/db-connection.class.php';
// include '../class-scripts/user.class.php';

// $sessionUserID = $_SESSION['id'];

// if(isset($_POST['sql_offset'])){
//     $SqlOffset = $_POST['sql_offset'];
//     $searchPhrase = $_POST['search_phrase'];
//     $startDate = $_POST['start_date'];
//     $endDate = $_POST['end_date'];

//     $userObj = new User();
//     $userObj->fetchUserPlans($sessionUserID, $SqlOffset, $searchPhrase, $startDate, $endDate);
// }

header("Content-Type: application/json");
session_start();

include '../class-scripts/db-connection.class.php';
include '../class-scripts/user.class.php';

$sessionUserID = $_SESSION['id'];

if (isset($_POST['sql_offset'])) {
    $SqlOffset = $_POST['sql_offset'];
    $searchPhrase = $_POST['search_phrase'];
    $startDate = $_POST['start_date'];
    $endDate = $_POST['end_date'];

    $userObj = new User();
    $userObj->fetchUserPlans($sessionUserID, $SqlOffset, $searchPhrase, $startDate, $endDate);
} else if (isset($_POST['request_type']) && $_POST['request_type'] === "get-user-plans") {
    getUserPlans($sessionUserID);
}

function getUserPlans($user_id)
{
    $db = new DBconnection();
    $conn = $db->connect();

    // Get all user plans, including expired ones
    $sql = "SELECT up.*, pp.plan_name, pp.amount, pp.duration 
            FROM user_plans up 
            JOIN payment_plan pp ON up.plan_id = pp.id 
            WHERE up.user_id = $user_id 
            ORDER BY up.date_end DESC";

    $result = mysqli_query($conn, $sql);

    $plans = [];

    if ($result && mysqli_num_rows($result) > 0) {
        while ($row = mysqli_fetch_assoc($result)) {
            // Determine if the plan is active
            $status = 'Expired';
            if ($row['status'] == 1 && strtotime($row['date_end']) >= time()) {
                $status = 'Active';
            }

            $plans[] = [
                'id' => $row['id'],
                'plan_id' => $row['plan_id'],
                'plan_name' => $row['plan_name'],
                'amount' => $row['amount'],
                'duration' => $row['duration'],
                'start_date' => $row['state_date'],
                'expiry_date' => $row['date_end'],
                'status' => $status
            ];
        }
    }

    echo json_encode([
        "message" => "success",
        "plans" => $plans
    ]);
}
