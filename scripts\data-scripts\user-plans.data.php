<?php

// header("Content-Type: application/json");
// session_start();

// include '../class-scripts/db-connection.class.php';
// include '../class-scripts/user.class.php';

// $sessionUserID = $_SESSION['id'];

// if(isset($_POST['sql_offset'])){
//     $SqlOffset = $_POST['sql_offset'];
//     $searchPhrase = $_POST['search_phrase'];
//     $startDate = $_POST['start_date'];
//     $endDate = $_POST['end_date'];

//     $userObj = new User();
//     $userObj->fetchUserPlans($sessionUserID, $SqlOffset, $searchPhrase, $startDate, $endDate);
// }

header("Content-Type: application/json");
session_start();

include '../class-scripts/db-connection.class.php';
include '../class-scripts/user.class.php';

$sessionUserID = $_SESSION['id'];

if (isset($_POST['sql_offset'])) {
    $SqlOffset = $_POST['sql_offset'];
    $searchPhrase = $_POST['search_phrase'];
    $startDate = $_POST['start_date'];
    $endDate = $_POST['end_date'];

    // Use the improved fetchUserPlans function instead of the class method
    fetchUserPlansImproved($sessionUserID, $SqlOffset, $searchPhrase, $startDate, $endDate);
} else if (isset($_POST['request_type']) && $_POST['request_type'] === "get-user-plans") {
    getUserPlans($sessionUserID);
}

function fetchUserPlansImproved($userId, $offset, $searchphrase, $startdate, $enddate)
{
    $db = new DBconnection();
    $conn = $db->connect();

    $currentDate = date("Y-m-d");

    // Try both admin_plan and payment_plan tables to ensure compatibility
    $sql = "SELECT up.*,
                   COALESCE(ap.plan_name, pp.plan_name) as plan_name,
                   COALESCE(ap.amount, pp.amount) as amount,
                   COALESCE(ap.duration, pp.duration) as duration,
                   up.date_end as expiry_date,
                   up.state_date as start_date
            FROM user_plans up
            LEFT JOIN admin_plan ap ON up.plan_id = ap.id
            LEFT JOIN payment_plan pp ON up.plan_id = pp.id
            WHERE up.user_id = $userId";

    // Apply date filters
    if ($startdate !== '' && $enddate !== '') {
        $sql .= " AND up.state_date BETWEEN '$startdate' AND '$enddate'";
    } else if ($startdate !== '' && $enddate == '') {
        $sql .= " AND up.state_date BETWEEN '$startdate' AND '$currentDate'";
    } else if ($startdate == '' && $enddate !== '') {
        $sql .= " AND up.state_date BETWEEN '1999-01-01' AND '$enddate'";
    }

    // Apply search filter
    if ($searchphrase !== '') {
        $sql .= " AND (COALESCE(ap.plan_name, pp.plan_name) LIKE '%$searchphrase%')";
    }

    // Order by expiry date descending to show active plans first
    $sql .= " ORDER BY up.date_end DESC";

    // Get count for pagination
    $countResult = mysqli_query($conn, $sql);
    $count = mysqli_num_rows($countResult);

    // Apply pagination
    $appDataConfigs = include('../config/app-data-configs.php');
    $sql .= " LIMIT " . $appDataConfigs['table_paginations'];
    if ($offset > 0) {
        $sql .= " OFFSET $offset";
    }

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        echo json_encode(["message" => "error"]);
        return;
    }

    if (mysqli_num_rows($result) < 1) {
        echo json_encode(["message" => "no result"]);
        return;
    }

    $userPlans = [];
    while ($row = mysqli_fetch_assoc($result)) {
        // Determine if the plan is active based on both status and expiry date
        $isActive = ($row['status'] == 1 && strtotime($row['expiry_date']) >= strtotime($currentDate));
        $status = $isActive ? 'Active' : 'Expired';

        $planData = [
            "plan_name" => $row['plan_name'],
            "amount" => $row['amount'],
            "expiry_date" => $row['expiry_date'],
            "start_date" => $row['start_date'],
            "status" => $status
        ];

        array_push($userPlans, $planData);
    }

    $responseBody = [
        "message" => "User plans fetched successfully",
        "pagination_configurations" => [
            "row_set" => $appDataConfigs['table_paginations'],
            "page_divisions" => ceil($count / $appDataConfigs['table_paginations']),
            "db_result_count" => $count
        ],
        "plans" => $userPlans
    ];

    echo json_encode($responseBody);
}

function getUserPlans($user_id)
{
    $db = new DBconnection();
    $conn = $db->connect();

    // Try both admin_plan and payment_plan tables to ensure compatibility
    $sql = "SELECT up.*,
                   COALESCE(ap.plan_name, pp.plan_name) as plan_name,
                   COALESCE(ap.amount, pp.amount) as amount,
                   COALESCE(ap.duration, pp.duration) as duration
            FROM user_plans up
            LEFT JOIN admin_plan ap ON up.plan_id = ap.id
            LEFT JOIN payment_plan pp ON up.plan_id = pp.id
            WHERE up.user_id = $user_id
            ORDER BY up.date_end DESC";

    $result = mysqli_query($conn, $sql);

    $plans = [];

    if ($result && mysqli_num_rows($result) > 0) {
        while ($row = mysqli_fetch_assoc($result)) {
            // Determine if the plan is active based on both status and expiry date
            // $isActive = ($row['status'] == 1 && strtotime($row['date_end']) >= time());
            // $status = $isActive ? 'Active' : 'Expired';
            // Determine if the plan is active based on both status and expiry date
            $isActive = ($row['status'] == 1 && strtotime($row['date_end']) >= time());
            $status = $isActive ? 'Active' : 'Expired';


            $plans[] = [
                'id' => $row['id'],
                'plan_id' => $row['plan_id'],
                'plan_name' => $row['plan_name'],
                'amount' => $row['amount'],
                'duration' => $row['duration'],
                'start_date' => $row['state_date'],
                'expiry_date' => $row['date_end'],
                'status' => $status
            ];
        }
    }

    echo json_encode([
        "message" => "success",
        "plans" => $plans
    ]);
}
