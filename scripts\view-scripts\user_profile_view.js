$(document).ready(function () {
  // Get user ID from URL or use current user
  let userId = new URLSearchParams(window.location.search).get("user_id") || 0;

  // Load profile data including avatar
  $.ajax({
    type: "get",
    url: "scripts/data-scripts/user_profile.data.php",
    data: {
      user_id: userId,
    },
    dataType: "json",
    success: function (result) {
      if (result && result.length > 0) {
        // Update profile image on the profile page
        if (result[0].avatar && result[0].avatar !== "") {
          $("#item-img-output").attr(
            "src",
            `assets/images/${result[0].avatar}`
          );
        }

        // Update other profile information
        $(".users_name").text(result[0].username || "User");
        $("#user_country").text(result[0].location || "");
        $("#user_bio").text(result[0].bio || "No bio available");

        // Show remove button if it's the user's own profile
        if (userId == 0 || userId == result[0].id) {
          $("#remove_avatar").removeClass("hidden");
          $("#icon_upload").removeClass("hidden");
        }
      }
    },
    error: function (xhr, status, error) {
      console.error("Error loading profile data:", error);
    },
  });
  let user_id = 0;

  const searchParams = new URLSearchParams(window.location.search);
  if (searchParams.has("user_id")) {
    user_id = searchParams.get("user_id");
  }

  var offset = 0;
  var offset2 = 0;

  var isOwnedImagesLoading = false;
  var currentOwnedImagesLength = 0;

  var isLikedImagesLoading = false;
  var currentLikedImagesLength = 0;

  $.ajax({
    type: "get",
    url: "scripts/data-scripts/user_profile.data.php",
    data: {
      user_id: user_id,
    },
    dataType: "json",
    success: function (result) {
      console.log(result);
      let list = document.getElementsByClassName("users_name");
      for (i = 0; i < list.length; i++) {
        list[i].innerHTML = result[0].username;
      }

      $("#user_country").html(result[0].country);
      $("#user_bio").html(result[0].bio);

      if (result[0].user_status == 1) {
        $("#icon_upload").show();
        $("#remove_avatar").show();
      }
      //corper emmanuel added purchased images
      var text = `
         <div class="col-md-12 border-top border-bottom">
         <ul class="nav nav-pills pull-left countlist" role="tablist">
           <li role="presentation">
             <h3>${result[0].followers}<br>
               <small>Followers</small> </h3>
           </li>
           <li role="presentation">
             <h3>${result[0].likes_no}<br>
               <small>Likes</small> </h3>
           </li>
           <li role="presentation">
             <h3>${result[0].images_no}<br>
               <small>Images</small> </h3>
           </li>
            <li role="presentation">
             <h3>${result[0].purchased_images_no}<br>
               <small>Purchased Images</small> </h3>
           </li>
             <li role="presentation">
             <h3>${result[0].bookmarker_count}<br>
               <small>Bookmarkers</small> </h3>
           </li>

         </ul>
         <!-- <button class="btn btn-primary followbtn">Follow</button> -->
       </div>`;

      /* ${`peco_image_store/avatar/${result[0].avatar}`}*/
      $("#user_info_count").html(text);

      //   $("#cropImageBtn").on("click", function (ev) {
      //     $uploadCrop
      //       .croppie("result", {
      //         type: "base64",
      //         // format: 'jpeg',
      //         backgroundColor: "#000000",
      //         format: "png",
      //         size: { width: 160, height: 160 },
      //       })
      //       .then(function (resp) {
      //         $("#item-img-output").attr("src", resp);
      //         var imgsrc = document.getElementById("item-img-output").src;
      //         console.log(imgsrc);
      //         $("#cropImagePop").modal("hide");
      //         $(".item-img").val("");

      //         $.post(
      //           "scripts/data-scripts/user_profile.data.php",
      //           { image_src: imgsrc },
      //           function (return_data) {
      //             // assign Callback Function Return Data to Variable for Usability

      //             console.log(return_data);
      //             if (return_data.message == "updated") {
      //               document.getElementById("item-img-output").src = imgsrc;
      //             }
      //           }
      //         );
      //       });
      //   });
      $("#cropImageBtn").on("click", function (ev) {
        $uploadCrop
          .croppie("result", {
            type: "base64",
            backgroundColor: "#000000",
            format: "png",
            size: { width: 160, height: 160 },
          })
          .then(function (resp) {
            $("#item-img-output").attr("src", resp);
            var imgsrc = resp;
            $("#cropImagePop").modal("hide");
            $(".item-img").val("");

            $.post(
              "scripts/data-scripts/user_profile.data.php",
              { image_src: imgsrc },
              function (return_data) {
                console.log(return_data);
                if (
                  return_data.status == 1 &&
                  return_data.message == "updated"
                ) {
                  // Update all profile images on the page
                  $("#item-img-output").attr(
                    "src",
                    `assets/images/${return_data.avatar}`
                  );
                  // Update navbar profile images
                  $("#auth-imgg").attr(
                    "src",
                    `assets/images/${return_data.avatar}`
                  );
                  $("#auth-img").attr(
                    "src",
                    `assets/images/${return_data.avatar}`
                  );
                  // Show remove button
                  $("#remove_avatar").removeClass("hidden");
                } else {
                  alert("Failed to update profile picture. Please try again.");
                }
              }
            );
          });
      });
    },
  });

  // Add this to your existing script section
  $(".btn-danger").click(function () {
    $.post(
      "scripts/data-scripts/user_profile.data.php",
      { remove_avatar: 1 },
      function (return_data) {
        if (return_data.status == 1) {
          // Update all profile images to default
          $("#item-img-output").attr("src", "assets/images/default.jpg");
          $("#auth-imgg").attr("src", "assets/images/default.jpg");
          $("#auth-img").attr("src", "assets/images/default.jpg");
          // Hide remove button
          $("#remove_avatar").addClass("hidden");
          // Close modal
          $("#exampleModal").modal("hide");
        } else {
          alert("Failed to remove profile picture. Please try again.");
        }
      }
    );
  });

  /* console.log($("#item-img-output")[0].currentSrc);
$('#cropImageBtn').on('click', function (ev) {
  var imgsrc = document.getElementById("item-img-output").src;
  console.log("Germany");
  console.log(imgsrc);
  //$('#item-img-output').attr('src', resp);
}); */

  // ! commented - DO NOT DELETE
  // $.post("scripts/data-scripts/user_profile.data.php", {fetch_liked_images: 1, user_id: user_id}, function (return_data) {
  //   // assign Callback Function Return Data to Variable for Usability

  //   console.log(return_data);

  //   if (return_data["status"] == "1") {
  //     createLikedImagesViewByLoop(return_data.results);
  //   }else {

  //     if(return_data["message"] == "no records") {
  //       $("#liked-images-feedback").show();
  //       $("#liked-images-feedback").html("No images");
  //     }
  //       // ! server error
  //       // disableLoader();
  //   }
  // });

  // ! commented - DO NOT DELETE
  // $.post("scripts/data-scripts/user_profile.data.php", {fetch_own_images: 1, user_id: user_id}, function (return_data) {
  //   // assign Callback Function Return Data to Variable for Usability

  //   console.log(return_data);

  //   if (return_data["status"] == "1") {
  //     createUploadedImagesViewByLoop(return_data.results);
  //   }else {

  //     if(return_data["message"] == "no records") {
  //       $("#uploaded-images-feedback").show();
  //       $("#uploaded-images-feedback").html("No images");
  //     }
  //     // ! server error
  //     // disableLoader();
  //   }
  // });

  $.post(
    "scripts/data-scripts/user_profile.data.php",
    { fetch_bookmarks: 1, user_id: user_id },
    function (return_data) {
      // assign Callback Function Return Data to Variable for Usability

      console.log(return_data);
      console.log(return_data.results[0]);

      /*   for( {collection, image} of return_data.results){
   
      if (collection ){
        console.log(return_data.results[i][0], return_data.results[j][0]);
      }
  }
 */
    }
  );

  function loadMoreLikedImages() {
    $.post(
      "scripts/data-scripts/user_profile.data.php",
      { fetch_liked_images: 1, user_id: user_id, offset: offset },
      function (return_data) {
        // assign Callback Function Return Data to Variable for Usability

        console.log(return_data);

        if (return_data["status"] == "1") {
          var likedItem = "";

          return_data.results.forEach((element) => {
            if (element["image"] != "") {
              likedItem += `
              <div class="grid">
                  <img src="peco_image_store/${element["image"]}" alt="" />
                  <div class="grid__body">
                      <div class="relative">
                          <a class="grid__link details-link" href=""></a>
                          <a href="" class="details-link grid__tag">
                              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#ff565b" viewBox="-100 0 700 512">
                                  <path d="M562.8 267.7c56.5-56.5 56.5-148 0-204.5c-50-50-128.8-56.5-186.3-15.4l-1.6 1.1c-14.4 10.3-17.7 30.3-7.4 44.6s30.3 17.7 44.6 7.4l1.6-1.1c32.1-22.9 76-19.3 103.8 8.6c31.5 31.5 31.5 82.5 0 114L405.3 334.8c-31.5 31.5-82.5 31.5-114 0c-27.9-27.9-31.5-71.8-8.6-103.8l1.1-1.6c10.3-14.4 6.9-34.4-7.4-44.6s-34.4-6.9-44.6 7.4l-1.1 1.6C189.5 251.2 196 330 246 380c56.5 56.5 148 56.5 204.5 0L562.8 267.7zM43.2 244.3c-56.5 56.5-56.5 148 0 204.5c50 50 128.8 56.5 186.3 15.4l1.6-1.1c14.4-10.3 17.7-30.3 7.4-44.6s-30.3-17.7-44.6-7.4l-1.6 1.1c-32.1 22.9-76 19.3-103.8-8.6C57 372 57 321 88.5 289.5L200.7 177.2c31.5-31.5 82.5-31.5 114 0c27.9 27.9 31.5 71.8 8.6 103.9l-1.1 1.6c-10.3 14.4-6.9 34.4 7.4 44.6s34.4 6.9 44.6-7.4l1.1-1.6C416.5 260.8 410 182 360 132c-56.5-56.5-148-56.5-204.5 0L43.2 244.3z" />
                              </svg>
                          </a>
                          <a href="" class="bookmark like-button grid__tag like_btn__tag_p">
                              <svg class="heart-icon stroke" viewBox="0 0 24 24">
                                  <defs>
                                      <clipPath id="mask">
                                          <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z" />
                                      </clipPath>
                                  </defs>
                                  <circle id="first-stroke" r="0" cx="12" cy="12" clip-path="url(#mask)"></circle>
                                  <circle id="second-stroke" r="0" cx="12" cy="12" clip-path="url(#mask)"></circle>
                              </svg>
                              <svg class="heart-icon red" viewBox="0 0 24 24">
                                  <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z" />
                              </svg>
                              <svg class="heart-icon" viewBox="0 0 24 24">
                                  <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z" />
                              </svg>
                          </a>
                          </p>
                      </div>
                      <div class="mt-auto">
                          <span class="grid__tag">${element["category"]}</span>
                      </div>
                  </div>
              </div>`;
            }
          });

          $("#liked-images-pane").append(likedItem);

          offset += return_data.results.length;
        } else {
          if (return_data["message"] == "no records") {
            // TODO: change to toast
            // $("#liked-images-feedback").show();
            // $("#liked-images-feedback").html("No images");
          } else {
            $("#liked-images-feedback").show();
            $("#liked-images-feedback").html("Unable to fetch images");
          }
        }
      }
    );
  }

  //corper emmanuel wrote this function
  function loadMorePurchasedImages() {
    $.post(
      "scripts/data-scripts/user_profile.data.php",
      { fetch_purchased_images: 1, user_id: user_id, offset: offset },
      function (return_data) {
        // assign Callback Function Return Data to Variable for Usability

        console.log(return_data);

        if (return_data["status"] == "1") {
          var purchasedItem = "";

          return_data.results.forEach((element) => {
            if (element["image"] != "") {
              purchasedItem += `
              <div class="grid">
                  <img src="peco_image_store/thumbnail/${element["thumbnail"]}" alt="" />
                  <div class="grid__body">
                      <div class="relative">
                          <a class="grid__link details-link" href=""></a>
                          <a href="" class="details-link grid__tag">
                              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#ff565b" viewBox="-100 0 700 512">
                                  <path d="M562.8 267.7c56.5-56.5 56.5-148 0-204.5c-50-50-128.8-56.5-186.3-15.4l-1.6 1.1c-14.4 10.3-17.7 30.3-7.4 44.6s30.3 17.7 44.6 7.4l1.6-1.1c32.1-22.9 76-19.3 103.8 8.6c31.5 31.5 31.5 82.5 0 114L405.3 334.8c-31.5 31.5-82.5 31.5-114 0c-27.9-27.9-31.5-71.8-8.6-103.8l1.1-1.6c10.3-14.4 6.9-34.4-7.4-44.6s-34.4-6.9-44.6 7.4l-1.1 1.6C189.5 251.2 196 330 246 380c56.5 56.5 148 56.5 204.5 0L562.8 267.7zM43.2 244.3c-56.5 56.5-56.5 148 0 204.5c50 50 128.8 56.5 186.3 15.4l1.6-1.1c14.4-10.3 17.7-30.3 7.4-44.6s-30.3-17.7-44.6-7.4l-1.6 1.1c-32.1 22.9-76 19.3-103.8-8.6C57 372 57 321 88.5 289.5L200.7 177.2c31.5-31.5 82.5-31.5 114 0c27.9 27.9 31.5 71.8 8.6 103.9l-1.1 1.6c-10.3 14.4-6.9 34.4 7.4 44.6s34.4 6.9 44.6-7.4l1.1-1.6C416.5 260.8 410 182 360 132c-56.5-56.5-148-56.5-204.5 0L43.2 244.3z" />
                              </svg>
                          </a>
                          <a href="" class="bookmark like-button grid__tag like_btn__tag_p">
                              <svg class="heart-icon stroke" viewBox="0 0 24 24">
                                  <defs>
                                      <clipPath id="mask">
                                          <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z" />
                                      </clipPath>
                                  </defs>
                                  <circle id="first-stroke" r="0" cx="12" cy="12" clip-path="url(#mask)"></circle>
                                  <circle id="second-stroke" r="0" cx="12" cy="12" clip-path="url(#mask)"></circle>
                              </svg>
                              <svg class="heart-icon red" viewBox="0 0 24 24">
                                  <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z" />
                              </svg>
                              <svg class="heart-icon" viewBox="0 0 24 24">
                                  <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z" />
                              </svg>
                          </a></p>
                      </div>
                      <div class="mt-auto">
                          <span class="grid__tag">${element["category"]}</span>
                      </div>
                  </div>
              </div>`;
            }
          });

          $("#purchased-images-pane").append(purchasedItem);

          offset += return_data.results.length;
        } else {
          if (return_data["message"] == "no records") {
            // TODO: change to toast
            // $("#liked-images-feedback").show();
            // $("#liked-images-feedback").html("No images");
          } else {
            $("#purchased-images-feedback").show();
            $("#purchased-images-feedback").html("Unable to fetch images");
          }
        }
      }
    );
  }

  function loadMoreBookmarkers() {
    $.post(
      "scripts/data-scripts/user_profile.data.php",
      { fetch_bookmarkers: 1, user_id: user_id, offset: offset },
      function (return_data) {
        // assign Callback Function Return Data to Variable for Usability

        console.log(return_data);

        if (return_data["status"] == "1" && return_data.results) {
          if (return_data.results.length === 0) {
            // Hide the container if there are no bookmarkers
            // $("#pills-bookmarkers").hide();
            $("#pills-bookmarkers")
              .html(
                '<h1 style="text-align:center; margin: 50px;">No bookmarkers</h1>'
              )
              .show();
          } else {
            var bookmarkers = "";
            let number = 1;

            return_data.results.forEach((element) => {
              if (element["thumbnail"] != "") {
                bookmarkers += `
                  <tr>
                    <td>${number++}</td>
                    <td><img src="assets/images/${
                      element["avatar"]
                    }" alt="User Avatar" style="width:50px; height:50px"></td>
                    <td>${element["username"]}</td>                       
                    <td><img src="peco_image_store/thumbnail/${
                      element["thumbnail"]
                    }" style="width:120px; height:120px" alt="" /></td>
                    <td>${element["created_at"]}</td>
                  </tr>
                `;
              }
            });

            $("#bookmarkers-pane").append(bookmarkers);

            offset += return_data.results.length;
          }
        } else {
          if (return_data["message"] == "no records") {
            // Hide the container if there are no bookmarkers
            // $("#pills-bookmarkers").hide();
            $("#pills-bookmarkers")
              .html(
                '<h1 style="text-align:center; margin: 50px;">No bookmarkers</h1>'
              )
              .show();
            // TODO: change to toast
            // $("#liked-images-feedback").show();
            // $("#liked-images-feedback").html("No images");
          } else {
            $("#bookmarkers-feedback").show();
            $("#bookmarkers-feedback").html("Unable to fetch people");
          }
        }
      }
    );
  }
  loadMoreBookmarkers();

  function loadMoreUploadedImages() {
    $.post(
      "scripts/data-scripts/user_profile.data.php",
      { fetch_own_images: 1, user_id: user_id, offset: offset2 },
      function (return_data) {
        // assign Callback Function Return Data to Variable for Usability

        console.log(return_data);

        if (return_data["status"] == "1") {
          var uploadedItem = "";

          return_data.results.forEach((element) => {
            if (element["image"] != "") {
              uploadedItem += `
              <div class="grid">
                  <img src="peco_image_store/${element["image"]}" alt="" />
                  <div class="grid__body">
                      <div class="relative">
                          <a class="grid__link details-link" href=""></a>
                          <a href="" class="details-link grid__tag">
                              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#ff565b" viewBox="-100 0 700 512">
                                  <path d="M562.8 267.7c56.5-56.5 56.5-148 0-204.5c-50-50-128.8-56.5-186.3-15.4l-1.6 1.1c-14.4 10.3-17.7 30.3-7.4 44.6s30.3 17.7 44.6 7.4l1.6-1.1c32.1-22.9 76-19.3 103.8 8.6c31.5 31.5 31.5 82.5 0 114L405.3 334.8c-31.5 31.5-82.5 31.5-114 0c-27.9-27.9-31.5-71.8-8.6-103.8l1.1-1.6c10.3-14.4 6.9-34.4-7.4-44.6s-34.4-6.9-44.6 7.4l-1.1 1.6C189.5 251.2 196 330 246 380c56.5 56.5 148 56.5 204.5 0L562.8 267.7zM43.2 244.3c-56.5 56.5-56.5 148 0 204.5c50 50 128.8 56.5 186.3 15.4l1.6-1.1c14.4-10.3 17.7-30.3 7.4-44.6s-30.3-17.7-44.6-7.4l-1.6 1.1c-32.1 22.9-76 19.3-103.8-8.6C57 372 57 321 88.5 289.5L200.7 177.2c31.5-31.5 82.5-31.5 114 0c27.9 27.9 31.5 71.8 8.6 103.9l-1.1 1.6c-10.3 14.4-6.9 34.4 7.4 44.6s34.4 6.9 44.6-7.4l1.1-1.6C416.5 260.8 410 182 360 132c-56.5-56.5-148-56.5-204.5 0L43.2 244.3z" />
                              </svg>
                          </a>
                          <a href="" class="bookmark like-button grid__tag like_btn__tag_p">
                              <svg class="heart-icon stroke" viewBox="0 0 24 24">
                                  <defs>
                                      <clipPath id="mask">
                                          <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z" />
                                      </clipPath>
                                  </defs>
                                  <circle id="first-stroke" r="0" cx="12" cy="12" clip-path="url(#mask)"></circle>
                                  <circle id="second-stroke" r="0" cx="12" cy="12" clip-path="url(#mask)"></circle>
                              </svg>
                              <svg class="heart-icon red" viewBox="0 0 24 24">
                                  <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z" />
                              </svg>
                              <svg class="heart-icon" viewBox="0 0 24 24">
                                  <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z" />
                              </svg>
                          </a></p>
                      </div>
                      <div class="mt-auto">
                          <span class="grid__tag">${element["category"]}</span>
                      </div>
                  </div>
              </div>`;
            }
          });

          $("#uploaded-images-pane").append(uploadedItem);

          offset2 += return_data.results.length;
        } else {
          if (return_data["message"] == "no records") {
            // TODO: change to toast
            // $("#uploaded-images-feedback").show();
            // $("#uploaded-images-feedback").html("No images");
          } else {
            $("#uploaded-images-feedback").show();
            $("#uploaded-images-feedback").html("Unable to fetch images");
          }
        }
      }
    );
  }

  loadMorePurchasedImages();
  // ! Load initial batch of liked images on page load
  loadMoreLikedImages();
  // ! Load initial batch of uploaded images on page load
  loadMoreUploadedImages();

  function createLikedImagesViewByLoop(data) {
    var likedItem = "";

    data.forEach((element) => {
      if (element["image"] != "") {
        likedItem += `
        <div class="grid">
            <img src="peco_image_store/${element["image"]}" alt="" />
            <div class="grid__body">
                <div class="relative">
                    <a class="grid__link details-link" href=""></a>
                    <a href="" class="details-link grid__tag">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#ff565b" viewBox="-100 0 700 512">
                            <path d="M562.8 267.7c56.5-56.5 56.5-148 0-204.5c-50-50-128.8-56.5-186.3-15.4l-1.6 1.1c-14.4 10.3-17.7 30.3-7.4 44.6s30.3 17.7 44.6 7.4l1.6-1.1c32.1-22.9 76-19.3 103.8 8.6c31.5 31.5 31.5 82.5 0 114L405.3 334.8c-31.5 31.5-82.5 31.5-114 0c-27.9-27.9-31.5-71.8-8.6-103.8l1.1-1.6c10.3-14.4 6.9-34.4-7.4-44.6s-34.4-6.9-44.6 7.4l-1.1 1.6C189.5 251.2 196 330 246 380c56.5 56.5 148 56.5 204.5 0L562.8 267.7zM43.2 244.3c-56.5 56.5-56.5 148 0 204.5c50 50 128.8 56.5 186.3 15.4l1.6-1.1c14.4-10.3 17.7-30.3 7.4-44.6s-30.3-17.7-44.6-7.4l-1.6 1.1c-32.1 22.9-76 19.3-103.8-8.6C57 372 57 321 88.5 289.5L200.7 177.2c31.5-31.5 82.5-31.5 114 0c27.9 27.9 31.5 71.8 8.6 103.9l-1.1 1.6c-10.3 14.4-6.9 34.4 7.4 44.6s34.4 6.9 44.6-7.4l1.1-1.6C416.5 260.8 410 182 360 132c-56.5-56.5-148-56.5-204.5 0L43.2 244.3z" />
                        </svg>
                    </a>
                    <a href="" class="bookmark like-button grid__tag like_btn__tag_p">
                        <svg class="heart-icon stroke" viewBox="0 0 24 24">
                            <defs>
                                <clipPath id="mask">
                                    <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z" />
                                </clipPath>
                            </defs>
                            <circle id="first-stroke" r="0" cx="12" cy="12" clip-path="url(#mask)"></circle>
                            <circle id="second-stroke" r="0" cx="12" cy="12" clip-path="url(#mask)"></circle>
                        </svg>
                        <svg class="heart-icon red" viewBox="0 0 24 24">
                            <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z" />
                        </svg>
                        <svg class="heart-icon" viewBox="0 0 24 24">
                            <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z" />
                        </svg>
                    </a></p>
                </div>
                <div class="mt-auto">
                    <span class="grid__tag">${element["category"]}</span>
                </div>
            </div>
        </div>`;
      }
    });

    $("#liked-images-pane").html(likedItem);
  }

  function createPurchasedImagesViewByLoop(data) {
    var purchasedItem = "";

    data.forEach((element) => {
      if (element["image"] != "") {
        likedItem += `
        <div class="grid">
            <img src="peco_image_store/${element["image"]}" alt="" />
            <div class="grid__body">
                <div class="relative">
                    <a class="grid__link details-link" href=""></a>
                    <a href="" class="details-link grid__tag">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#ff565b" viewBox="-100 0 700 512">
                            <path d="M562.8 267.7c56.5-56.5 56.5-148 0-204.5c-50-50-128.8-56.5-186.3-15.4l-1.6 1.1c-14.4 10.3-17.7 30.3-7.4 44.6s30.3 17.7 44.6 7.4l1.6-1.1c32.1-22.9 76-19.3 103.8 8.6c31.5 31.5 31.5 82.5 0 114L405.3 334.8c-31.5 31.5-82.5 31.5-114 0c-27.9-27.9-31.5-71.8-8.6-103.8l1.1-1.6c10.3-14.4 6.9-34.4-7.4-44.6s-34.4-6.9-44.6 7.4l-1.1 1.6C189.5 251.2 196 330 246 380c56.5 56.5 148 56.5 204.5 0L562.8 267.7zM43.2 244.3c-56.5 56.5-56.5 148 0 204.5c50 50 128.8 56.5 186.3 15.4l1.6-1.1c14.4-10.3 17.7-30.3 7.4-44.6s-30.3-17.7-44.6-7.4l-1.6 1.1c-32.1 22.9-76 19.3-103.8-8.6C57 372 57 321 88.5 289.5L200.7 177.2c31.5-31.5 82.5-31.5 114 0c27.9 27.9 31.5 71.8 8.6 103.9l-1.1 1.6c-10.3 14.4-6.9 34.4 7.4 44.6s34.4 6.9 44.6-7.4l1.1-1.6C416.5 260.8 410 182 360 132c-56.5-56.5-148-56.5-204.5 0L43.2 244.3z" />
                        </svg>
                    </a>
                    <a href="" class="bookmark like-button grid__tag like_btn__tag_p">
                        <svg class="heart-icon stroke" viewBox="0 0 24 24">
                            <defs>
                                <clipPath id="mask">
                                    <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z" />
                                </clipPath>
                            </defs>
                            <circle id="first-stroke" r="0" cx="12" cy="12" clip-path="url(#mask)"></circle>
                            <circle id="second-stroke" r="0" cx="12" cy="12" clip-path="url(#mask)"></circle>
                        </svg>
                        <svg class="heart-icon red" viewBox="0 0 24 24">
                            <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z" />
                        </svg>
                        <svg class="heart-icon" viewBox="0 0 24 24">
                            <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z" />
                        </svg>
                    </a></p>
                </div>
                <div class="mt-auto">
                    <span class="grid__tag">${element["category"]}</span>
                </div>
            </div>
        </div>`;
      }
    });

    $("#purchased-images-pane").html(purchasedItem);
  }

  function createBookmarkersViewByLoop(data) {
    var bookmarkers = "";

    data.forEach((element) => {
      if (element["thumbnail"] != "") {
        bookmarkers += `
               <section class="section-50">
                <div class="container">
                 <div class="notification-ui_dd-content">
                  <div class="notification-list notification-list--unread">
                    <div class="notification-list_content">
                        <div class="notification-list_img">
                            <img src="assets/images/${element["avatar"]}" alt="" />
                        </div>
                        <div class="notification-list_detail">
                            <p><b>${element["username"]} bookmarked your image</p>
                            <p class="text-muted"><small>${element["created_at"]}</small></p>
                        </div>
                    </div>
                    <div class="notification-list_feature-img">
                        <img src="peco_image_store/thumbnail/${element["thumbnail"]}" alt="" />
                    </div>
                </div>
              </div>
              </div>
              </section>
             `;
      }
    });

    $("#bookmarkers-pane").html(bookmarkers);
  }
  // createPurchasedImagesViewByLoop()

  function createUploadedImagesViewByLoop(data) {
    var uploadedItem = "";

    data.forEach((element) => {
      if (element["image"] != "") {
        uploadedItem += `
        <div class="grid">
            <img src="peco_image_store/${element["image"]}" alt="" />
            <div class="grid__body">
                <div class="relative">
                    <a class="grid__link details-link" href=""></a>
                    <a href="" class="details-link grid__tag">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#ff565b" viewBox="-100 0 700 512">
                            <path d="M562.8 267.7c56.5-56.5 56.5-148 0-204.5c-50-50-128.8-56.5-186.3-15.4l-1.6 1.1c-14.4 10.3-17.7 30.3-7.4 44.6s30.3 17.7 44.6 7.4l1.6-1.1c32.1-22.9 76-19.3 103.8 8.6c31.5 31.5 31.5 82.5 0 114L405.3 334.8c-31.5 31.5-82.5 31.5-114 0c-27.9-27.9-31.5-71.8-8.6-103.8l1.1-1.6c10.3-14.4 6.9-34.4-7.4-44.6s-34.4-6.9-44.6 7.4l-1.1 1.6C189.5 251.2 196 330 246 380c56.5 56.5 148 56.5 204.5 0L562.8 267.7zM43.2 244.3c-56.5 56.5-56.5 148 0 204.5c50 50 128.8 56.5 186.3 15.4l1.6-1.1c14.4-10.3 17.7-30.3 7.4-44.6s-30.3-17.7-44.6-7.4l-1.6 1.1c-32.1 22.9-76 19.3-103.8-8.6C57 372 57 321 88.5 289.5L200.7 177.2c31.5-31.5 82.5-31.5 114 0c27.9 27.9 31.5 71.8 8.6 103.9l-1.1 1.6c-10.3 14.4-6.9 34.4 7.4 44.6s34.4 6.9 44.6-7.4l1.1-1.6C416.5 260.8 410 182 360 132c-56.5-56.5-148-56.5-204.5 0L43.2 244.3z" />
                        </svg>
                    </a>
                    <a href="" class="bookmark like-button grid__tag like_btn__tag_p">
                        <svg class="heart-icon stroke" viewBox="0 0 24 24">
                            <defs>
                                <clipPath id="mask">
                                    <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z" />
                                </clipPath>
                            </defs>
                            <circle id="first-stroke" r="0" cx="12" cy="12" clip-path="url(#mask)"></circle>
                            <circle id="second-stroke" r="0" cx="12" cy="12" clip-path="url(#mask)"></circle>
                        </svg>
                        <svg class="heart-icon red" viewBox="0 0 24 24">
                            <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z" />
                        </svg>
                        <svg class="heart-icon" viewBox="0 0 24 24">
                            <path d="M12 4.435c-1.989-5.399-12-4.597-12 3.568 0 4.068 3.06 9.481 12 14.997 8.94-5.516 12-10.929 12-14.997 0-8.118-10-8.999-12-3.568z" />
                        </svg>
                    </a></p>
                </div>
                <div class="mt-auto">
                    <span class="grid__tag">${element["category"]}</span>
                </div>
            </div>
        </div>`;
      }
    });

    $("#uploaded-images-pane").html(uploadedItem);
  }

  // Implement lazy loading as the user scrolls
  $(window).scroll(function () {
    if (
      $(window).scrollTop() + $(window).height() >=
      $(document).height() - 700
    ) {
      console.log("lazy load happenning");
      loadMoreLikedImages().slideDown(1000);
      // loadMoreUploadedImages().slideDown(1000);
    }
  });
});
