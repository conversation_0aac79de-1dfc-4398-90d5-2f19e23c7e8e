<?php

// include 'db_config/db_connection.php';
// include "../scripts/config/encryption-handlers.php";
// session_start();

// $curl = curl_init();

// $ref = $_GET["reference"];
// $user_id = $_GET["user_id"];
// $email = $_GET["email"];

// curl_setopt_array($curl, array(
//     CURLOPT_URL => "https://api.paystack.co/transaction/verify/$ref",
//     CURLOPT_RETURNTRANSFER => true,
//     CURLOPT_ENCODING => "",
//     CURLOPT_MAXREDIRS => 10,
//     CURLOPT_TIMEOUT => 30,
//     CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
//     CURLOPT_CUSTOMREQUEST => "GET",
//     CURLOPT_HTTPHEADER => array(
//         "Authorization: Bearer sk_test_da3e0e887b69f7342464f89a6656150a09d676a0",
//         "Cache-Control: no-cache",
//     ),
// ));

// $response = curl_exec($curl);
// $err = curl_error($curl);

// curl_close($curl);

// if ($err) {
//     echo "cURL Error #:" . $err;
// } else {
//     $result = json_decode($response, true);

//     if ($result['status'] == true && $result['data']['status'] == 'success') {
//         // Payment verified successfully
//         $amount = $result['data']['amount'] / 100; // Convert from kobo to naira
//         $transaction_ref = $result['data']['reference'];

//         // Get cart items from session
//         if (!isset($_SESSION['cart']) || empty($_SESSION['cart'])) {
//             echo "error: Cart is empty";
//             exit;
//         }

//         $cart_items = $_SESSION['cart'];
//         $purchase_date = date('Y-m-d H:i:s');

//         // Begin transaction
//         mysqli_begin_transaction($connection);

//         try {
//             // Insert each cart item as a separate purchase record
//             foreach ($cart_items as $item) {
//                 $image_id = $item['id'];
//                 $image_title = mysqli_real_escape_string($connection, $item['title']);
//                 $image_price = $item['image_price'];
//                 $image_resolution = isset($item['image_resolution']) ? mysqli_real_escape_string($connection, $item['image_resolution']) : '';
//                 $tags = isset($item['tags']) ? mysqli_real_escape_string($connection, $item['tags']) : '';

//                 // Insert into purchase_history table
//                 $sql = "INSERT INTO purchase_history (
//                     user_id, 
//                     img_id, 
//                     title, 
//                     amount, 
//                     image_resolution, 
//                     tags, 
//                     transaction_reference, 
//                     Date_created, 
//                     status
//                 ) VALUES (
//                     '$user_id', 
//                     '$image_id', 
//                     '$image_title', 
//                     '$image_price', 
//                     '$image_resolution', 
//                     '$tags', 
//                     '$transaction_ref', 
//                     '$purchase_date', 
//                     1
//                 )";

//                 if (!mysqli_query($connection, $sql)) {
//                     throw new Exception("Failed to insert purchase record for image ID: $image_id");
//                 }
//             }

//             // Commit transaction
//             mysqli_commit($connection);

//             // Clear the cart after successful purchase
//             $_SESSION['cart'] = [];

//             echo "successful";

//         } catch (Exception $e) {
//             // Rollback transaction on error
//             mysqli_rollback($connection);
//             error_log("Cart payment verification error: " . $e->getMessage());
//             echo "error: " . $e->getMessage();
//         }

//     } else {
//         echo "error: Payment verification failed";
//     }
// }



include 'db_config/db_connection.php';
include "../scripts/config/encryption-handlers.php";
session_start();

$curl = curl_init();

$ref = $_GET["reference"];
$user_id = $_GET["user_id"];
$email = $_GET["email"];

curl_setopt_array($curl, array(
    CURLOPT_URL => "https://api.paystack.co/transaction/verify/$ref",
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => "",
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => "GET",
    CURLOPT_HTTPHEADER => array(
        "Authorization: Bearer sk_test_da3e0e887b69f7342464f89a6656150a09d676a0",
        "Cache-Control: no-cache",
    ),
));

$response = curl_exec($curl);
$err = curl_error($curl);

curl_close($curl);

if ($err) {
    echo "cURL Error #:" . $err;
} else {
    $result = json_decode($response, true);

    if ($result['status'] == true && $result['data']['status'] == 'success') {
        // Payment verified successfully
        $amount = $result['data']['amount'] / 100; // Convert from kobo to naira
        $transaction_ref = $result['data']['reference'];

        // Get cart items from session
        if (!isset($_SESSION['cart']) || empty($_SESSION['cart'])) {
            echo "error: Cart is empty";
            exit;
        }

        $cart_items = $_SESSION['cart'];
        $purchase_date = date('Y-m-d H:i:s');

        // Begin transaction
        mysqli_begin_transaction($connection);

        try {
            // Insert each cart item as a separate purchase record
            foreach ($cart_items as $item) {
                $image_id = $item['id'];
                $image_title = mysqli_real_escape_string($connection, $item['title']);
                $image_price = $item['image_price'];
                $image_resolution = isset($item['image_resolution']) ? mysqli_real_escape_string($connection, $item['image_resolution']) : '';
                $tags = isset($item['tags']) ? mysqli_real_escape_string($connection, $item['tags']) : '';

                // Insert into purchase_history table (using the correct table name)
                $sql = "INSERT INTO purchase_history (
                    user_id,
                    img_id,
                    amount,
                    transaction_reference,
                    Date_created,
                    status
                ) VALUES (
                    '$user_id',
                    '$image_id',
                    '$image_price',
                    '$transaction_ref',
                    '$purchase_date',
                    1
                )";

                if (!mysqli_query($connection, $sql)) {
                    throw new Exception("Failed to insert purchase record for image ID: $image_id");
                }
            }

            // Commit transaction
            mysqli_commit($connection);

            // Clear the cart after successful purchase
            $_SESSION['cart'] = [];

            echo "successful";
        } catch (Exception $e) {
            // Rollback transaction on error
            mysqli_rollback($connection);
            error_log("Cart payment verification error: " . $e->getMessage());
            echo "error: " . $e->getMessage();
        }
    } else {
        echo "error: Payment verification failed";
    }
}
