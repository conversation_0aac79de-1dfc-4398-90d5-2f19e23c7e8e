<!-- navbar section-->
<?php require_once(__DIR__ . '/resources/header.php'); ?>
<?php
session_start();
?>
</head>

<style>
    .disabled-btn {
        background-color: #ccc !important;
        color: #666 !important;
        cursor: not-allowed !important;
        border-color: #666 !important;
        box-shadow: none !important;
        opacity: 1 !important;
        filter: none !important;
    }
</style>

<body>

    <!-- navbar section-->
    <?php require_once(__DIR__ . '/resources/loader.php'); ?>


    <header id="#top">

        <!-- navbar section-->
        <?php require_once(__DIR__ . '/resources/navbar.php'); ?>
    </header>

    <div class="page-banner change-name">
        <div class="container">
            <div class="row">
                <div class="col-lg-6 offset-lg-3">
                    <div class="header-text">
                        <h2><em>Subscription</em> Plan</h2>
                        <p>Choose a payment plan that works for you.</p>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Success Modal -->
    <!-- Payment Success Modal -->
    <div class="modal fade" id="paymentSuccessModal" tabindex="-1" aria-labelledby="paymentSuccessModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="paymentSuccessModalLabel">Payment Successful</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Your payment was successful!</p>
                    <p><strong>Reference:</strong> <span id="paymentReference"></span></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal" style="background-color:#ff565b; border-color:#ff565b;">OK</button>
                </div>
            </div>
        </div>
    </div>






    <!-- Subscription Confirmation Modal -->
    <!-- <div class="modal fade" id="subscriptionConfirmModal" tabindex="-1" aria-labelledby="confirmModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      Modal Header
      <div class="modal-header bg-light border-bottom">
        <h5 class="modal-title text-dark fw-bold" id="confirmModalLabel">Subscription Change Confirmation</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>

      <p class="mt-3 mb-0" style="display: none">
          <strong>Plan Details:</strong><br>
          Plan ID: <span id="modalPlanId"></span><br>
          Plan Amount: <span id="modalPlanAmount"></span><br>
          Plan Duration: <span id="modalPlanDuration"></span>
          User ID: <span id="user_id1"></span>
          Email: <span id="email1"></span>
        </p>

      Modal Body
      <div class="modal-body">
        <p class="mb-0">
          You currently have an active subscription. Proceeding with this change will <strong>terminate your current subscription</strong> and replace it with the new plan.
        </p>
        <p class="mt-3 mb-0">
          Are you sure you want to continue?
        </p>
      </div>

      Modal Footer
      <div class="modal-footer border-top">
        <button type="button" class="btn btn-outline-secondary" id="disagreeBtn" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" id="agreeBtn" style="background-color:#ff565b; border-color:#ff565b;">Confirm Change</button>
      </div>
    </div>
  </div>
</div> -->

    <!-- Subscription Confirmation Modal -->
    <div class="modal fade" id="subscriptionConfirmModal" tabindex="-1" role="dialog" aria-labelledby="subscriptionConfirmModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header bg-warning">
                    <h5 class="modal-title" id="subscriptionConfirmModalLabel">Change Subscription Plan?</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>

                <p class="mt-3 mb-0" style="display: none">
                    <strong>Plan Details:</strong><br>
                    Plan ID: <span id="modalPlanId"></span><br>
                    Plan Amount: <span id="modalPlanAmount"></span><br>
                    Plan Duration: <span id="modalPlanDuration"></span>
                    User ID: <span id="user_id1"></span>
                    Email: <span id="email1"></span>
                </p>

                <!-- Modal Body -->
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <strong>Warning:</strong> You currently have an active subscription.
                    </div>

                    <p>
                        Proceeding with this change will <strong>terminate your current subscription</strong> and replace it with the new plan.
                    </p>

                    <p class="mb-0">
                        Are you sure you want to continue?
                    </p>
                </div>

                <!-- Modal Footer -->
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="confirmSubscriptionChange">Yes, Change My Plan</button>
                </div>
            </div>
        </div>
    </div>




    <script src="scripts/view-scripts/plans-view.js"></script>
    <!-- navbar section-->
    <?php require_once(__DIR__ . '/resources/plans.php'); ?>


    <!-- Footer Start-->
    <?php require_once(__DIR__ . '/resources/footer.php'); ?>
    <!-- Footer End-->

    <?php require_once(__DIR__ . '/resources/utility_scripts.php'); ?>
    <!-- Scripts -->
    <!-- Bootstrap core JavaScript -->
    <script src="vendor/jquery/jquery.min.js"></script>
    <script src="vendor/bootstrap/js/bootstrap.min.js"></script>
    <!-- <script src="vendor/bootstrap/js/bootstrap.5.2.3.min.js"></script> -->
    <script src="vendor/bootstrap/js/popper.min.js"></script>
    <script src="vendor/swiper/swiper-bundle.min.js"></script>
    <script src="vendor/glightbox/js/glightbox.min.js"></script>
    <script src="vendor/aos/aos.js"></script>
    <script src="assets/js/isotope.min.js"></script>
    <script src="assets/js/owl-carousel.js"></script>
    <script src="assets/js/mo.js"></script>
    <script src="assets/js/typed.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/5.0.0-alpha1/js/bootstrap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <script src="https://js.paystack.co/v1/inline.js"></script>


    <script src="assets/js/tabs.js"></script>
    <script src="assets/js/popup.js"></script>
    <script src="assets/js/custom.js"></script>
    <script src="https://unpkg.com/isotope-layout@3/dist/isotope.pkgd.min.js"></script>
    <script src="https://unpkg.com/imagesloaded@4/imagesloaded.pkgd.js"></script>
    <script src="assets/js/custom.js"></script>
    <script src="assets/js/checkout.js"></script>
    <script src="resources/make_payment.js"></script>
    <script src="scripts/view-scripts/check_out-view.js"></script>
    <script src="scripts/view-scripts/fetch-payment-cards-modal.js"></script>
    <!-- <script src="scripts/view-scripts/view-carts.js"></script> -->
    <script>
        $(document).ready(function() {
            set_interval();
        });
        $('body').click(function() {
            reset_interval();
        });
        $('body').mousemove(function() {
            reset_interval();
        });
        $('body').keypress(function() {
            reset_interval();
        });

        // setTimeout(function() {
        //     $('.loader').fadeToggle();
        // }, 1500);

        $("a[href='#top']").click(function() {
            $("html, body").animate({
                scrollTop: 0
            }, "slow");
            return false;
        });

        let logout_link = document.getElementsByClassName("logged-out");
        let login_link = document.getElementsByClassName("logged-in");

        $.post("scripts/data-scripts/auth-status.data.php", {
            request_type: 'auth-session-check'
        }, function(data) {
            if (data["message"] == "success") {
                // authed
                $(logout_link).addClass("hide");
                $(login_link).removeClass("hide");

                $("#authenticated-username").html(data["username"]);
            } else {
                // not authed
                $(logout_link).removeClass("hide");
                $(login_link).addClass("hide");
            }
        });

        $.ajax({
            type: 'get',
            url: 'scripts/data-scripts/user_profile.data.php',
            data: {
                user_id: 0
            },
            dataType: 'json',
            success: function(result) {
                if (result[0].avatar == "") {
                    return;
                }
                $("#auth-imgg").attr("src", `assets/images/${result[0].avatar}`);
                $("#auth-img").attr("src", `assets/images/${result[0].avatar} `);
            }
        });



        $("#signout-btn").click(function() {
            logout_call();
        });



        function set_interval() {
            timer = setInterval(() => {
                auto_logout();
            }, 3600000);
        }

        function reset_interval() {
            clearInterval(timer);
            timer = setInterval(() => {
                auto_logout();
            }, 3600000);
        }

        function auto_logout() {
            logout_call();
        }

        function logout_call() {
            console.log("auto logout called");
            $.post("scripts/data-scripts/auth-status.data.php", {
                request_type: 'auth-session-end'
            }, function(data) {
                if (data["message"] == "success") {
                    // signed out
                    window.location.href = "signup.php?login";
                }
            });
        }
    </script>


    <script>
        setTimeout(function() {
            $('.loader').fadeToggle();
        }, 1500);

        $("a[href='#top']").click(function() {
            $("html, body").animate({
                scrollTop: 0
            }, "slow");
            return false;
        });
    </script>

</body>

</html>