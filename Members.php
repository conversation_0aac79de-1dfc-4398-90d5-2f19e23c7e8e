<!-- Header section-->
    <?php require_once(__DIR__ . '/resources/header.php'); ?>

    <link rel="stylesheet" href="assets/css/members.css">
    <!-- <link rel="stylesheet" href="assets/new-js/members.js"> -->
    
	<!-----------Members css--------->

    <!-- <link rel="stylesheet" href="assets/css/app.css">
    <link rel="stylesheet" href="assets/css/fontawesome.css">
    <link rel="stylesheet" href="assets/css/icomoon.css">
    <link rel="stylesheet" href="assets/css/main_members.css">
    <link rel="stylesheet" href="assets/css/strokeicons.css"> -->
    <style>
        :root {
            --primary-color: rgb(255, 86, 91);
            --default-theme: rgb(255, 86, 91);
        }

        .profile-card {
            color: var(--default-theme);
            position: relative;
            padding: 1.5rem; /* Reduced padding */
            background: white;
            border-radius: 1.5rem;
            overflow: hidden;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 4px 15px rgba(255, 86, 91, 0.15);
        }
        .profile-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(255, 86, 91, 0.2);
        }
        .avatar-wrapper {
            position: relative;
            width: 80px; /* Reduced size */
            height: 80px; /* Reduced size */
            margin: 0 auto 1rem; /* Reduced margin */
        }

        .avatar {
            position: relative;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            overflow: hidden;
            background: #f8f8f8;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 3px solid var(--default-theme);
        }

        .avatar i {
            font-size: 35px; /* Reduced size */
            color: rgb(255, 86, 91);
        }

        .profile-info {
            text-align: center;
        }

        .name {
            font-size: 1.4rem; /* Reduced size */
            margin-bottom: 0.25rem;
            color: #333;
        }

        .title {
            color: #666;
            font-size: 0.9rem; /* Reduced size */
            margin-bottom: 0.5rem; /* Reduced margin */
        }

        .stats {
            display: flex;
            justify-content: center;
            gap: 2rem; /* Reduced gap */
            margin: 0.5rem 0; /* Reduced margin */
            padding: 0.5rem 0; /* Reduced padding */
            border-top: 1px solid rgba(255, 86, 91, 0.2);
            border-bottom: 1px solid rgba(255, 86, 91, 0.2);
        }

        .stat-value {
            font-size: 1rem; /* Reduced size */
            font-weight: bold;
            color: #333;
        }

        .stat-label {
            font-size: 0.8rem; /* Reduced size */
            color: #666;
        }

        .bio {
            color: #666 !important;
            margin: 0.5rem 0 !important; /* Reduced margin */
            font-size: 0.85rem; /* Reduced size */
        }

        .action-btn {
            position: relative;
            padding: 0.5rem; /* Reduced padding */
            border: none;
            border-radius: 0.8rem;
            font-size: 0.9rem; /* Reduced size */
            font-weight: 600;
            cursor: pointer;
            overflow: hidden;
            transition: all 0.3s ease;
            width: 100%;
            background: var(--default-theme);
            color: white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            z-index: 1;
        }

        .action-btn:hover {
            background: rgb(235, 66, 71);
            transform: translateY(-2px);
        }

        /* Rest of the styles remain the same */

        @media (max-width: 576px) {
            .container {
                padding: 0.5rem;
            }

            .profile-card {
                padding: 1rem;
            }

            .avatar-wrapper {
                width: 70px;
                height: 70px;
            }

            .name {
                font-size: 1.2rem;
            }

            .stats {
                gap: 1.5rem;
            }
        }
         /* Set a fixed height for all profile cards */
  .profile-card {
    height: 380px; /* Adjust this value based on your needs */
    display: flex;
    flex-direction: column;
  }
  
  /* Make the card content take up all available space */
  .card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  
  /* Ensure the profile info section grows to fill available space */
  .profile-info {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  
  /* Push the button to the bottom if needed */
  .profile-info .d-grid {
    margin-top: auto;
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin: 2rem 0;
    padding: 1rem;
}

.page-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    background: white;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.page-btn:hover:not(:disabled) {
    background: #f0f0f0;
}

.page-btn.active {
    background: #007bff;
    color: white;
    border-color: #0056b3;
}

.page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
    </style>
</head>

<body>

     <!-- Loader section-->
    <?php require_once(__DIR__ . '/resources/loader.php'); ?>

    <header id="#top">
		<!-- navbar section-->
      <?php require_once(__DIR__.'/resources/navbar.php'); ?>

    </header>

    <div class="page-banner change-name">
        <div class="container">
            <div class="row">
                <div class="col-lg-6 offset-lg-3">
                     <!-- total number of users section-->
					<?php require_once(__DIR__ . '/resources/total_members_count.php'); ?>
                </div>
            </div>
        </div>
    </div>


    <!------------------------MEMBER SECTION---------------------->


    <div class="container">
      <div class="row">
        <div class="section over-hide z-bigger" >
          <div class="section over-hide z-bigger">
            <div class="container pb-5">
            <!-- members page filter section-->
            <?php require_once(__DIR__ . '/resources/members_filter.php'); ?>
            </div>	
          </div>
        </div>
            <!-- members cards section-->
            <?php require_once(__DIR__ . '/resources/member_cards.php'); ?>
      </div>
    </div>
      

      <!-- Loader section-->
		  <?php require_once(__DIR__.'/resources/footer.php'); ?>
  

    <!-- Scripts -->
	<!-- Scripts section -->
  <?php require_once(__DIR__ . '/resources/scripts.php'); ?>
  <script src="scripts/view-scripts/members.js"></script>
  <script src="scripts/view-scripts/member-filter-view.js"></script>



	<!-- Utility scripts section -->
    <?php require_once(__DIR__ . '/resources/utility_scripts.php'); ?>


</body>

</html>