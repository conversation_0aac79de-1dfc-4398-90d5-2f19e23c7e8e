<?php
// session_start();
// include 'resources/db_config/db_connection.php';

// echo "<h2>Direct Purchase Check</h2>";

// if (!isset($_SESSION['id'])) {
//     echo "<p>❌ Please login first</p>";
//     exit;
// }

// $user_id = $_SESSION['id'];
// echo "<p>Checking purchases for User ID: $user_id</p>";

// // Check both possible table names
// $tables = ['puchase_history', 'purchase_history'];

// foreach ($tables as $table) {
//     echo "<h3>Checking table: $table</h3>";

//     // Check if table exists
//     $check_table = $db->query("SHOW TABLES LIKE '$table'");
//     if ($check_table->num_rows == 0) {
//         echo "<p>❌ Table '$table' does not exist</p>";
//         continue;
//     }

//     echo "<p>✅ Table '$table' exists</p>";

//     // Get all records for this user
//     $query = "SELECT * FROM $table WHERE user_id = $user_id ORDER BY Date_created DESC";
//     echo "<p>Query: $query</p>";

//     $result = $db->query($query);

//     if (!$result) {
//         echo "<p>❌ Query failed: " . $db->error . "</p>";
//         continue;
//     }

//     if ($result->num_rows == 0) {
//         echo "<p>⚠️ No records found for user $user_id in table $table</p>";

//         // Check if there are any records at all in this table
//         $all_records = $db->query("SELECT COUNT(*) as total FROM $table");
//         if ($all_records) {
//             $total = $all_records->fetch_assoc()['total'];
//             echo "<p>Total records in $table: $total</p>";

//             if ($total > 0) {
//                 echo "<p>Sample records from $table:</p>";
//                 $sample = $db->query("SELECT * FROM $table LIMIT 5");
//                 if ($sample && $sample->num_rows > 0) {
//                     echo "<table border='1' style='border-collapse: collapse;'>";

//                     // Headers
//                     $fields = $sample->fetch_fields();
//                     echo "<tr>";
//                     foreach ($fields as $field) {
//                         echo "<th>" . $field->name . "</th>";
//                     }
//                     echo "</tr>";

//                     // Data
//                     $sample->data_seek(0);
//                     while ($row = $sample->fetch_assoc()) {
//                         echo "<tr>";
//                         foreach ($row as $value) {
//                             echo "<td>" . htmlspecialchars($value) . "</td>";
//                         }
//                         echo "</tr>";
//                     }
//                     echo "</table>";
//                 }
//             }
//         }
//     } else {
//         echo "<p>✅ Found " . $result->num_rows . " records for user $user_id</p>";

//         echo "<table border='1' style='border-collapse: collapse;'>";

//         // Headers
//         $fields = $result->fetch_fields();
//         echo "<tr>";
//         foreach ($fields as $field) {
//             echo "<th>" . $field->name . "</th>";
//         }
//         echo "</tr>";

//         // Data
//         $result->data_seek(0);
//         while ($row = $result->fetch_assoc()) {
//             echo "<tr>";
//             foreach ($row as $value) {
//                 echo "<td>" . htmlspecialchars($value) . "</td>";
//             }
//             echo "</tr>";
//         }
//         echo "</table>";
//     }

//     echo "<hr>";
// }

// // Test the User class method directly
// echo "<h3>Testing User Class Method</h3>";

// include 'scripts/class-scripts/user.class.php';

// $userObj = new User();

// // Capture the output
// ob_start();
// $userObj->fetchPurchaseHistories($user_id, 0, '', '', '');
// $output = ob_get_clean();

// echo "<p>User class output:</p>";
// echo "<pre style='background: #f0f0f0; padding: 10px;'>" . htmlspecialchars($output) . "</pre>";

// // Parse the JSON to see what's returned
// $data = json_decode($output, true);
// if ($data) {
//     echo "<p>Parsed data:</p>";
//     echo "<pre>" . print_r($data, true) . "</pre>";
// } else {
//     echo "<p>❌ Failed to parse JSON output</p>";
// }

?>

<!-- <p><a href="test_purchase_flow.php">Back to Purchase Flow Test</a></p>
<p><a href="account-settings.php">Account Settings</a></p> -->


<?php
session_start();
include 'resources/db_config/db_connection.php';

echo "<h2>Direct Purchase Check</h2>";

if (!isset($_SESSION['id'])) {
    echo "<p>❌ Please login first</p>";
    exit;
}

$user_id = $_SESSION['id'];
echo "<p>Checking purchases for User ID: $user_id</p>";

// Check both possible table names
$tables = ['puchase_history', 'purchase_history'];

foreach ($tables as $table) {
    echo "<h3>Checking table: $table</h3>";

    // Check if table exists
    $check_table = $db->query("SHOW TABLES LIKE '$table'");
    if ($check_table->num_rows == 0) {
        echo "<p>❌ Table '$table' does not exist</p>";
        continue;
    }

    echo "<p>✅ Table '$table' exists</p>";

    // Get all records for this user
    $query = "SELECT * FROM $table WHERE user_id = $user_id ORDER BY Date_created DESC";
    echo "<p>Query: $query</p>";

    $result = $db->query($query);

    if (!$result) {
        echo "<p>❌ Query failed: " . $db->error . "</p>";
        continue;
    }

    if ($result->num_rows == 0) {
        echo "<p>⚠️ No records found for user $user_id in table $table</p>";

        // Check if there are any records at all in this table
        $all_records = $db->query("SELECT COUNT(*) as total FROM $table");
        if ($all_records) {
            $total = $all_records->fetch_assoc()['total'];
            echo "<p>Total records in $table: $total</p>";

            if ($total > 0) {
                echo "<p>Sample records from $table:</p>";
                $sample = $db->query("SELECT * FROM $table LIMIT 5");
                if ($sample && $sample->num_rows > 0) {
                    echo "<table border='1' style='border-collapse: collapse;'>";

                    // Headers
                    $fields = $sample->fetch_fields();
                    echo "<tr>";
                    foreach ($fields as $field) {
                        echo "<th>" . $field->name . "</th>";
                    }
                    echo "</tr>";

                    // Data
                    $sample->data_seek(0);
                    while ($row = $sample->fetch_assoc()) {
                        echo "<tr>";
                        foreach ($row as $value) {
                            echo "<td>" . htmlspecialchars($value) . "</td>";
                        }
                        echo "</tr>";
                    }
                    echo "</table>";
                }
            }
        }
    } else {
        echo "<p>✅ Found " . $result->num_rows . " records for user $user_id</p>";

        echo "<table border='1' style='border-collapse: collapse;'>";

        // Headers
        $fields = $result->fetch_fields();
        echo "<tr>";
        foreach ($fields as $field) {
            echo "<th>" . $field->name . "</th>";
        }
        echo "</tr>";

        // Data
        $result->data_seek(0);
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    }

    echo "<hr>";
}

// Test the User class method directly
echo "<h3>Testing User Class Method</h3>";

include 'scripts/class-scripts/user.class.php';

$userObj = new User();

// Capture the output
ob_start();
$userObj->fetchPurchaseHistories($user_id, 0, '', '', '');
$output = ob_get_clean();

echo "<p>User class output:</p>";
echo "<pre style='background: #f0f0f0; padding: 10px;'>" . htmlspecialchars($output) . "</pre>";

// Parse the JSON to see what's returned
$data = json_decode($output, true);
if ($data) {
    echo "<p>Parsed data:</p>";
    echo "<pre>" . print_r($data, true) . "</pre>";
} else {
    echo "<p>❌ Failed to parse JSON output</p>";
}

?>

<p><a href="test_purchase_flow.php">Back to Purchase Flow Test</a></p>
<p><a href="account-settings.php">Account Settings</a></p>