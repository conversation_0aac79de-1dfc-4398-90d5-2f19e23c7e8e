//import "../assets/css/user-profile.css";
var modal = document.getElementById("myModal");
var modalCls = document.getElementsByClassName("profileModal");
var span = document.getElementsByClassName("close")[0]; //close button for the profile modal
var closePassword = document.getElementsByClassName("close")[1]; //close button for the password modal
/* var subscriptionAlert = document.getElementsByClassName("close")[2]  //close button for the profile modal
 */ var closeSpan = document.getElementsByClassName("close")[3]; //close button for the billing modal

/*active button class onclick*/
//console.log("Let us honor God");

// Function to show a specific tab
function showTab(tabId) {
  // Remove "active" from all links, then add to the clicked link
  $(".acct-setting a").removeClass("active");
  $(`#${tabId}`).addClass("active");

  // Hide all sections in .rightbox
  $(".rightbox").children().addClass("noshow");

  // Show the chosen section
  if (tabId === "profile") {
    $(".profile").removeClass("noshow");
  } else if (tabId === "payment") {
    $(".payment").removeClass("noshow");
  } else if (tabId === "plans") {
    $(".plans").removeClass("noshow");
  } else if (tabId === "purchaseHistory") {
    $(".purchaseHistory").removeClass("noshow");
  } else if (tabId === "settings") {
    $(".settings").removeClass("noshow");
  }
}

// Check URL parameters on page load
$(document).ready(function () {
  const urlParams = new URLSearchParams(window.location.search);
  const tab = urlParams.get("tab");

  if (tab) {
    showTab(tab);
  }
});

$(".acct-setting a").click(function (e) {
  e.preventDefault();
  showTab(this.id);
});

$(".btnAct").click(function () {
  //e.preventDefault();
  $("#myModal").show();
});

$(".newPayCard").click(function () {
  //e.preventDefault();
  $("#paymentModal").show();
});

$(closeSpan).click(function () {
  //e.preventDefault();
  $("#paymentModal").hide();
});

$(closePassword).click(function () {
  //e.preventDefault();
  $("#passcodeModal").hide();
});

$(".P-word-btn").click(function () {
  $("#passcodeModal").show();
});

$(".choose-default-card").click(function (e) {
  e.preventDefault(e);
  $(".finish-default-card").show();
  $(".show-default-card").show();
  $(".activated-default-card").hide();
  $(".choose-default-card").hide();
});

$(".finish-default-card").click(function (e) {
  e.preventDefault(e);
  $(".show-default-card").hide();
  /* setTimeout(() => { }, 1000);  */
  $(".finish-default-card").hide();
  $(".choose-default-card").show();
});

// $('.remove-card').click(function(e) {
//   e.preventDefault(e);
//   var text = ` <div class="modal-header">
//   <h4 class="modal-title">Remove Card</h4>
//   <button type="button" class="close_settings_confirmation" data-dismiss="modal">&times;</button>
// </div>
// <div class="modal-body">
//   <div>Are you sure you want to remove this card?</div>
// </div>
// <div class="modal-footer">
//  <button id="yes-remove" type="button" class="btn btn-default" data-dismiss="modal">Yes, Remove</button>
//   <button type="button" class="btn btn-default close_settings_confirmation" data-dismiss="modal">Close</button>
// </div>`;
//   $('.settings_confirmation').html(text);
//   $('#settings_confirmation').show();
// });

// $('.cancel-btn').click(function() {
//   var text = ` <div class="modal-header">
//   <h4 class="modal-title">Cancel Subscription</h4>
//   <button type="button" class="close_settings_confirmation" data-dismiss="modal">&times;</button>
// </div>
// <div class="modal-body">
//   <div>Are you sure you want to cancel your subscription?</div>
// </div>
// <div class="modal-footer">
//  <button type="button" class="btn btn-default" data-dismiss="modal">Yes, Cancel</button>
//   <button type="button" class="btn btn-defaults close_settings_confirmation" data-dismiss="modal">Close</button>
// </div>`;
//   $('.settings_confirmation').html(text);
//   $('#settings_confirmation').show();
// });

$("body").on("click", ".close_settings_confirmation", function (event) {
  event.preventDefault();

  $("#settings_confirmation").hide();
});

window.onclick = function (event) {
  if (event.target == modal) {
    modal.visibility = "none";
  }
};

/* When the user clicks on the button,
toggle between hiding and showing the dropdown content */
function myFunction() {
  document.getElementById("setting-myDropdown").classList.toggle("show");
}

function filterFunction() {
  var input, filter, ul, li, a, i;
  input = document.getElementById("myInput");
  filter = input.value.toUpperCase();
  div = document.getElementById("setting-myDropdown");
  a = div.getElementsByTagName("a");
  for (i = 0; i < a.length; i++) {
    txtValue = a[i].textContent || a[i].innerText;
    if (txtValue.toUpperCase().indexOf(filter) > -1) {
      a[i].style.display = "";
    } else {
      a[i].style.display = "none";
    }
  }
}
