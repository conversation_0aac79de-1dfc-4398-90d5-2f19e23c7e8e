// payment-information-views.js
$(document).ready(function () {
  $("close-modal").on("click", function (e) {
    $("#addCardModal").hide();
  });
  var paymentCards = []; // Store all payment cards

  var dbCardsId = [];
  var isEditMode = false;
  var selectedCardIndexToRemove = null;
  var selectedCardIdToSetDefault = null;

  // Function to obscure card number (show last 4 digits)
  function obscureText(text, visibleDigits) {
    if (!text) return "";
    const obscuredLength = text.length - visibleDigits;
    if (obscuredLength > 0) {
      return "*".repeat(obscuredLength) + text.slice(-visibleDigits);
    }
    return text;
  }

  // Enhanced card removal functionality
  function setupCardRemovalHandlers() {
    $(".remove-card")
      .off("click")
      .click(function () {
        const cardId = $(this).data("card-id");
        const cardIndex = $(this).data("card-index");

        // Get card details directly from data attributes
        const cardNumber = $(this).data("card-number");
        const cardHolder = $(this).data("card-holder");

        // Store the selected card info for removal
        selectedCardIndexToRemove = cardIndex;
        selectedCardIdToRemove = cardId;

        // Update the confirmation modal with card details
        $("#card-number-preview").text(cardNumber);
        $("#card-holder-preview").text(cardHolder);

        // Get card type from the image in the same row
        const cardTypeImg = $(this)
          .closest("tr")
          .find(".card-type img")
          .attr("src");
        let cardIconHtml = `<img src="${cardTypeImg}" alt="Card" height="30">`;

        // If no image found, use a generic icon
        if (!cardTypeImg) {
          cardIconHtml = '<i class="fas fa-credit-card fa-2x"></i>';
        }

        $("#card-icon").html(cardIconHtml);

        // Show the confirmation modal
        $("#removal-alert").modal("show");
      });

    // Event listener for the "Yes, Remove" button in the removal confirmation modal
    $("#yes-remove")
      .off("click")
      .click(function () {
        $("#removal-alert").modal("hide");
        removeCard(selectedCardIdToRemove);
      });
  }

  // Function to handle card removal
  function removeCard(cardId) {
    $("#card-loader").show();

    $.ajax({
      url: "scripts/data-scripts/payment-information.data.php",
      type: "POST",
      dataType: "json",
      data: {
        action: "remove-card",
        id: cardId,
      },
      success: function (response) {
        $("#card-loader").hide();
        if (response.success) {
          // Show toast notification instead of modal
          showToastMessage("Success", "Card removed successfully!");
          fetchPaymentCards();
          selectedCardIndexToRemove = null;
          selectedCardIdToRemove = null;
        } else {
          // For errors, we'll still use the modal
          showModalAlert("Error", "Error removing card: " + response.message);
        }
      },
      error: function (xhr, status, error) {
        $("#card-loader").hide();
        console.error("AJAX error removing card:", error);
        showModalAlert("Error", "An error occurred while removing the card.");
      },
    });
  }

  // Function to fetch payment cards
  function fetchPaymentCards() {
    $("#card-loader").show();

    $.ajax({
      url: "scripts/data-scripts/payment-information.data.php",
      type: "POST",
      dataType: "json",
      data: {
        action: "get-cards",
      },
      success: function (response) {
        $("#card-loader").hide();
        if (response.success) {
          // Store the cards globally
          paymentCards = response.cards;

          // Create the payment information view
          createPaymentInfoView(paymentCards);
        } else {
          showModalAlert(
            "Error",
            "Error fetching payment cards: " + response.message
          );
        }
      },
      error: function (xhr, status, error) {
        $("#card-loader").hide();
        console.error("AJAX error fetching cards:", error);
        showModalAlert(
          "Error",
          "An error occurred while fetching payment cards."
        );
      },
    });
  }

  // Function to populate the default card selection modal
  function populateDefaultCardModal() {
    const tableBody = $("#default_card_selection_table tbody");
    tableBody.empty();

    // Check if we have cards
    if (paymentCards && paymentCards.length > 0) {
      $("#no-cards-message").addClass("d-none");

      // Card image paths
      const cardImages = {
        visa: "assets/img/gallery/Visa-card.png",
        mastercard: "assets/img/gallery/Master-card.png",
        "american express": "assets/img/gallery/amex-card.png",
        discover: "assets/img/gallery/discover-card.png",
        default: "assets/images/credit-card-outline.png",
      };

      // Create a row for each card
      paymentCards.forEach((card) => {
        // Get the appropriate card image
        let cardImage = cardImages.default;
        const cardType = card.card_type
          ? card.card_type.trim().toLowerCase()
          : "";

        if (cardImages[cardType]) {
          cardImage = cardImages[cardType];
        }

        // Display the card number exactly as stored in the database
        // Only obscure it for display purposes
        const cardNumber = card.card_no || "";
        const displayCardNumber = obscureText(cardNumber, 4);

        // Create the row
        const isDefault = card.default_card == "1";
        const row = `
        <tr class="${isDefault ? "table-success" : ""}">
          <td>
            <img src="${cardImage}" alt="${
          card.card_type
        }" style="max-height: 30px;">
          </td>
          <td>${displayCardNumber}</td>
          <td>${card.exp_month}/${card.exp_year}</td>
          <td>${card.card_holder_name || "Not specified"}</td>
          <td>
            ${
              isDefault
                ? '<span class="badge bg-success rounded-pill"><i class="fas fa-check me-1"></i>Current Default</span>'
                : `<button class="btn btn-sm btn-primary set-default-card-btn" data-card-id="${card.id}">
                Set as Default
              </button>`
            }
          </td>
        </tr>
      `;

        tableBody.append(row);
      });

      // Add event listeners to the "Set as Default" buttons
      $(".set-default-card-btn")
        .off("click")
        .on("click", function () {
          const cardId = $(this).data("card-id");
          setDefaultCard(cardId);
          $("#defaultCardModal").modal("hide");
        });
    } else {
      // No cards available
      $("#no-cards-message").removeClass("d-none");
    }
  }

  // Event listener for the "Choose default card" button
  $(".choose-default-card")
    .off("click")
    .on("click", function (e) {
      e.preventDefault();

      // Populate the modal with card data
      populateDefaultCardModal();

      // Show the modal
      $("#defaultCardModal").modal("show");
    });

  // Function to set a card as default
  function setDefaultCard(cardId) {
    $("#card-loader").show();

    $.ajax({
      url: "scripts/data-scripts/payment-information.data.php",
      type: "POST",
      dataType: "json",
      data: {
        action: "set-default-card",
        id: cardId,
      },
      success: function (response) {
        $("#card-loader").hide();
        if (response.success) {
          // Show success message
          showToastMessage("Success", "Default card updated successfully!");

          // Refresh the payment cards
          fetchPaymentCards();
        } else {
          showModalAlert(
            "Error",
            "Error setting default card: " + response.message
          );
        }
      },
      error: function (xhr, status, error) {
        $("#card-loader").hide();
        console.error("AJAX error setting default card:", error);
        showModalAlert(
          "Error",
          "An error occurred while setting the default card."
        );
      },
    });
  }

  // Function to create the payment information view table
  function createPaymentInfoView(data) {
    var tableRows = "";

    // Card image paths
    const cardImages = {
      visa: "assets/img/gallery/Visa-card.png",
      mastercard: "assets/img/gallery/Master-card.png",
      "american express": "assets/img/gallery/amex-card.png",
      discover: "assets/img/gallery/discover-card.png",
      default: "assets/images/credit-card-outline.png",
    };

    $("#payment_info_view_table tbody").empty();
    dbCardsId = [];

    if (data && Array.isArray(data)) {
      for (let i = 0; i < data.length; i++) {
        const element = data[i];
        dbCardsId.push(element.id);

        // Get the appropriate card image
        let cardImage = cardImages.default;
        const cardType = element.card_type
          ? element.card_type.trim().toLowerCase()
          : "";

        if (cardImages[cardType]) {
          cardImage = cardImages[cardType];
        }

        // Make sure card_holder_name is displayed properly with fallback
        const cardHolderName = element.card_holder_name || "Not specified";

        // Display the card number exactly as stored in the database
        // Only obscure it for display purposes
        const cardNumber = element.card_no || "";
        const displayCardNumber = obscureText(cardNumber, 4);

        // Check if this is the default card
        const isDefault = element.default_card == "1";

        tableRows += `
        <tr class="${isDefault ? "table-success" : ""}">
          <td class="card-type">
            <img src="${cardImage}" alt="${
          element.card_type
        }" style="max-height: 30px;">
          </td>
          <td class="card-number">${displayCardNumber}</td>
          <td>${element.exp_month}/${element.exp_year}</td>
          <td>
            ${
              isDefault
                ? '<span class="badge bg-success rounded-pill"><i class="fas fa-check me-1"></i>Default</span>'
                : ""
            }
          </td>
          <td class="card-holder fw-bold">${cardHolderName}</td>
          <td>
            <div class="btn-group">
              <button class="btn btn-sm btn-outline-primary edit-card-btn" data-card-id="${
                element.id
              }">
                <i class="fas fa-edit"></i> Edit
              </button>
              <button class="btn btn-sm btn-outline-danger remove-card" 
                data-card-index="${i}" 
                data-card-id="${element.id}"
                data-card-holder="${cardHolderName}"
                data-card-number="${displayCardNumber}">
                <i class="fas fa-trash-alt"></i> Remove
              </button>
            </div>
          </td>
        </tr>`;
      }
    }

    $("#payment_info_view_table tbody").html(tableRows);

    // Set up event handlers
    setupCardRemovalHandlers();

    // Set up edit button handlers
    $(".edit-card-btn")
      .off("click")
      .on("click", function () {
        const cardId = $(this).data("card-id");
        fetchCardDetailsForUpdate(cardId);
      });
  }
  // Event listener for the "Add new card" button
  $("#new-paycard-btn").click(function () {
    $("#addCardModal").modal("show");
  });
  // Event listener for the "Save Card" button
  $("#saveCardBtn").click(function () {
    $("#card-loader").show();

    // Get the exact card number as entered by the user
    const cardNumber = $("#cardNumber").val().trim();

    // Log for debugging
    console.log("Saving card with number:", cardNumber);

    $.ajax({
      url: "scripts/data-scripts/payment-information.data.php",
      type: "POST",
      dataType: "json",
      data: {
        action: "add-card",
        cardNumber: cardNumber,
        cardName: $("#cardName").val().trim(),
        card_type: $("input[name='cardType']:checked").val(),
        expiryMonth: $("#expiryMonth").val(),
        expiryYear: $("#expiryYear").val(),
      },
      success: function (response) {
        $("#card-loader").hide();
        if (response.success) {
          showToastMessage("Success", "Card added successfully!");
          $("#addCardModal").modal("hide");
          $("#addCardForm")[0].reset();
          fetchPaymentCards();
        } else {
          showModalAlert("Error", "Error adding card: " + response.message);
        }
      },
      error: function (xhr, status, error) {
        $("#card-loader").hide();
        console.error("AJAX error adding card:", error);
        showModalAlert("Error", "An error occurred while adding the card.");
      },
    });
  });

  // Function to fetch card details for updating
  function fetchCardDetailsForUpdate(cardId) {
    $.ajax({
      url: "scripts/data-scripts/payment-information.data.php",
      type: "POST",
      dataType: "json",
      data: { action: "get-card-details", card_id: cardId },
      success: function (response) {
        if (response.success) {
          var card = response.card;
          $("#update_id").val(card.id);
          $("#update_card_no").val(card.card_no);
          $("#update_card_holder_name").val(card.card_holder_name);

          // Set the card type radio button
          $(
            `input[name='card_type'][value='${card.card_type.toLowerCase()}']`
          ).prop("checked", true);

          // Ensure expiry month is properly selected
          // First convert to string and pad with leading zero if needed
          const expMonth = String(card.exp_month).padStart(2, "0");
          $("#update_exp_month").val(expMonth);

          // If the above doesn't work, try this alternative approach
          const expMonthSelect = document.getElementById("update_exp_month");
          for (let i = 0; i < expMonthSelect.options.length; i++) {
            if (expMonthSelect.options[i].value === expMonth) {
              expMonthSelect.selectedIndex = i;
              break;
            }
          }

          $("#update_exp_year").val(card.exp_year);
          $("#update_default_card").val(card.default_card);
          $("#update_add_reference").val(card.add_reference);

          // Log for debugging
          console.log("Card details loaded:", {
            id: card.id,
            card_no: card.card_no,
            card_holder_name: card.card_holder_name,
            card_type: card.card_type,
            exp_month: card.exp_month,
            formatted_exp_month: expMonth,
            exp_year: card.exp_year,
            default_card: card.default_card,
          });
        } else {
          showModalAlert(
            "Error",
            "Error fetching card details: " + response.message
          );
        }
      },
      error: function (xhr, status, error) {
        console.error("AJAX error fetching card details:", error);
        showModalAlert(
          "Error",
          "An error occurred while fetching card details."
        );
      },
    });
  }

  // Event listener for the "Update Card" button
  $("#updateCardBtn").click(function (event) {
    event.preventDefault();
    $("#card-loader").show();

    var updateData = $("#updateCardForm").serializeArray();
    var postData = {};
    $(updateData).each(function (i, field) {
      postData[field.name] = field.value;
    });
    postData["action"] = "update-card";

    $.ajax({
      url: "scripts/data-scripts/payment-information.data.php",
      type: "POST",
      dataType: "json",
      data: postData,
      success: function (response) {
        $("#card-loader").hide();
        if (response.success) {
          showModalAlert("Success", "Card updated successfully!");
          $("#updateCardModal").modal("hide");
          fetchPaymentCards();
        } else {
          showModalAlert("Error", "Error updating card: " + response.message);
        }
      },
      error: function (xhr, status, error) {
        $("#card-loader").hide();
        console.error("AJAX error updating card:", error);
        showModalAlert("Error", "An error occurred while updating the card.");
      },
    });
  });

  // Event listener for the "Update Card" button
  $("#updateCardBtn").click(function (event) {
    event.preventDefault();
    $("#card-loader").show();

    // Get the form data
    var updateData = $("#updateCardForm").serializeArray();
    var postData = {};

    // Process each form field
    $(updateData).each(function (i, field) {
      // Trim string values but don't modify them otherwise
      if (typeof field.value === "string") {
        postData[field.name] = field.value.trim();
      } else {
        postData[field.name] = field.value;
      }
    });

    // Add the action
    postData["action"] = "update-card";

    // Log for debugging
    console.log("Updating card with data:", postData);

    $.ajax({
      url: "scripts/data-scripts/payment-information.data.php",
      type: "POST",
      dataType: "json",
      data: postData,
      success: function (response) {
        $("#card-loader").hide();
        if (response.success) {
          showToastMessage("Success", "Card updated successfully!");
          $("#updateCardModal").modal("hide");
          fetchPaymentCards();
        } else {
          showModalAlert("Error", "Error updating card: " + response.message);
        }
      },
      error: function (xhr, status, error) {
        $("#card-loader").hide();
        console.error("AJAX error updating card:", error);
        showModalAlert("Error", "An error occurred while updating the card.");
      },
    });
  });

  // Event listener for the "Yes, Remove" button in the removal confirmation modal
  $("#yes-remove").click(function () {
    $("#removal-alert").modal("hide");
    $("#card-loader").show();
    $.ajax({
      url: "scripts/data-scripts/payment-information.data.php",
      type: "POST",
      dataType: "json",
      data: {
        action: "remove-card",
        id: selectedCardIdToRemove,
      },
      success: function (response) {
        $("#card-loader").hide();
        if (response.success) {
          //alert("Card removed successfully!"); // Replaced with modal
          showModalAlert("Success", "Card removed successfully!");
          fetchPaymentCards();
          selectedCardIndexToRemove = null;
          selectedCardIdToRemove = null;
        } else {
          //alert("Error removing card: " + response.message); // Replaced with modal
          showModalAlert("Error", "Error removing card: " + response.message);
        }
      },
      error: function (xhr, status, error) {
        $("#card-loader").hide();
        console.error("AJAX error removing card:", error);
        //alert('An error occurred while removing the card.'); // Replaced with modal
        showModalAlert("Error", "An error occurred while removing the card.");
      },
    });
  });

  // Function to display a Bootstrap modal alert
  function showModalAlert(title, message) {
    $("#modal-alert-title").text(title);
    $("#modal-alert-body").text(message);
    $("#modal-alert").modal("show");
  }

  // Initial load of payment cards when the page loads
  fetchPaymentCards();

  // Toggle edit mode
  $(".choose-default-card").click(function (e) {
    e.preventDefault();
    isEditMode = true;
    $(".show-default-card").removeClass("hidden");
    $(".edit-card-btn").prop("disabled", true);
    $(".choose-default-card").addClass("hidden");
    $(".finish-default-card").removeClass("hidden");
  });

  $(".finish-default-card").click(function (e) {
    e.preventDefault();
    isEditMode = false;
    $(".show-default-card").addClass("hidden");
    $(".edit-card-btn").prop("disabled", false);
    $(".choose-default-card").removeClass("hidden");
    $(".finish-default-card").addClass("hidden");
  });

  // Alternative toast message function that doesn't rely on Bootstrap
  function showToastMessage(title, message) {
    // Check if Bootstrap's Toast is available
    if (typeof bootstrap !== "undefined" && bootstrap.Toast) {
      // Use Bootstrap Toast (code from previous snippet)
      // Create toast container if it doesn't exist
      if ($("#toast-container").length === 0) {
        $("body").append(
          '<div id="toast-container" class="position-fixed top-0 end-0 p-3" style="z-index: 1050;"></div>'
        );
      }

      // Generate a unique ID for this toast
      const toastId = "toast-" + Date.now();

      // Create toast HTML
      const toastHtml = `
      <div id="${toastId}" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
          <div class="toast-body">
            <strong>${title}:</strong> ${message}
          </div>
          <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
      </div>
    `;

      // Add toast to container
      $("#toast-container").append(toastHtml);

      // Initialize and show the toast
      const toastElement = document.getElementById(toastId);
      const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: 3000,
      });
      toast.show();

      // Remove toast from DOM after it's hidden
      $(toastElement).on("hidden.bs.toast", function () {
        $(this).remove();
      });
    } else {
      // Fallback to a simple notification
      // Create notification container if it doesn't exist
      if ($("#notification-container").length === 0) {
        $("body").append(`
        <div id="notification-container" class="position-fixed top-0 end-0 p-3" style="z-index: 1050;">
          <div id="notification" class="alert alert-success alert-dismissible fade" role="alert" style="display: none;">
            <strong id="notification-title"></strong> <span id="notification-message"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>
        </div>
      `);
      }

      // Update notification content
      $("#notification-title").text(title + ":");
      $("#notification-message").text(" " + message);

      // Show notification
      $("#notification").fadeIn().addClass("show");

      // Hide notification after 3 seconds
      setTimeout(function () {
        $("#notification").fadeOut().removeClass("show");
      }, 3000);
    }
  }

  // Simple notification function that doesn't rely on Bootstrap
  function showToastMessage(title, message) {
    // Create or get the notification element
    let notification = $("#simple-notification");

    if (notification.length === 0) {
      // Create the notification element if it doesn't exist
      $("body").append(`
      <div id="simple-notification" style="
        position: fixed;
        top: 20px;
        right: 20px;
        max-width: 300px;
        background-color: #28a745;
        color: white;
        padding: 15px;
        border-radius: 4px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        z-index: 9999;
        display: none;
        transition: opacity 0.3s ease-in-out;
      ">
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <div>
            <strong id="notification-title"></strong>
            <span id="notification-message"></span>
          </div>
          <button id="close-notification" style="
            background: transparent;
            border: none;
            color: white;
            font-size: 20px;
            line-height: 20px;
            cursor: pointer;
            margin-left: 10px;
          ">&times;</button>
        </div>
      </div>
    `);

      notification = $("#simple-notification");

      // Add click handler for close button
      $("#close-notification").on("click", function () {
        notification.fadeOut();
      });
    }

    // Update notification content
    $("#notification-title").text(title);
    $("#notification-message").text(": " + message);

    // Show notification
    notification.fadeIn();

    // Hide notification after 3 seconds
    setTimeout(function () {
      notification.fadeOut();
    }, 3000);
  }
});
