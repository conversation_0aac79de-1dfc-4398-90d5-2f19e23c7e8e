<?php

class UserSettings extends DBconnection
{
    private $userId;

    public function __construct($userId = null)
    {
        parent::__construct();
        $this->userId = $userId;
    }
    public function checkConnection()
    {
        $conn = $this->connect();
        if ($conn) {
            return ["status" => "success", "message" => "Database connection successful"];
        } else {
            return ["status" => "error", "message" => "Failed to connect to database"];
        }
    }

    public function disableNotifications()
    {
        $conn = $this->connect();

        try {
            // Check if user_preferences table exists
            $checkTableSql = "SHOW TABLES LIKE 'user_preferences'";
            $tableResult = $conn->query($checkTableSql);

            if ($tableResult->num_rows == 0) {
                return ["status" => "error", "message" => "User preferences table does not exist"];
            }

            // Check if user has preferences record
            $checkSql = "SELECT * FROM user_preferences WHERE user_id = ?";
            $checkStmt = $conn->prepare($checkSql);

            if ($checkStmt === false) {
                return ["status" => "error", "message" => "Database query preparation failed: " . $conn->error];
            }

            $checkStmt->bind_param("i", $this->userId);
            $checkStmt->execute();
            $result = $checkStmt->get_result();

            if ($result->num_rows === 0) {
                // Create preferences record
                $insertSql = "INSERT INTO user_preferences (user_id, order_notifications) VALUES (?, 0)";
                $insertStmt = $conn->prepare($insertSql);

                if ($insertStmt === false) {
                    return ["status" => "error", "message" => "Failed to prepare insert statement: " . $conn->error];
                }

                $insertStmt->bind_param("i", $this->userId);

                if ($insertStmt->execute()) {
                    return ["status" => "success", "message" => "Notifications disabled"];
                } else {
                    return ["status" => "error", "message" => "Database error: " . $insertStmt->error];
                }
            } else {
                // Update existing record
                $updateSql = "UPDATE user_preferences SET order_notifications = 0 WHERE user_id = ?";
                $updateStmt = $conn->prepare($updateSql);

                if ($updateStmt === false) {
                    return ["status" => "error", "message" => "Failed to prepare update statement: " . $conn->error];
                }

                $updateStmt->bind_param("i", $this->userId);

                if ($updateStmt->execute()) {
                    return ["status" => "success", "message" => "Notifications disabled"];
                } else {
                    return ["status" => "error", "message" => "Failed to update preferences: " . $updateStmt->error];
                }
            }
        } catch (Exception $e) {
            return ["status" => "error", "message" => "An error occurred: " . $e->getMessage()];
        }
    }

    public function cancelSubscription()
    {
        $conn = $this->connect();
        $debug = ["user_id" => $this->userId];

        try {
            // Query to find active subscription in user_plans table
            $planSql = "SELECT id, plan_id, state_date, date_end, amount, status 
                   FROM user_plans 
                   WHERE user_id = ? AND date_end >= CURDATE() AND status = 1 
                   ORDER BY date_end DESC LIMIT 1";

            $debug["query"] = $planSql;
            $planStmt = $conn->prepare($planSql);

            if ($planStmt === false) {
                $debug["prepare_error"] = $conn->error;
                return ["status" => "error", "message" => "Database query preparation failed: " . $conn->error, "debug" => $debug];
            }

            $planStmt->bind_param("i", $this->userId);
            $planStmt->execute();
            $planResult = $planStmt->get_result();
            $debug["num_rows"] = $planResult->num_rows;

            if ($planResult->num_rows > 0) {
                // Found active plan in user_plans
                $plan = $planResult->fetch_assoc();
                $planId = $plan["id"];
                $debug["plan_found"] = $plan;

                // Update plan status to inactive (0)
                $updateSql = "UPDATE user_plans SET status = 0 WHERE id = ?";
                $updateStmt = $conn->prepare($updateSql);

                if ($updateStmt === false) {
                    return ["status" => "error", "message" => "Failed to prepare update statement: " . $conn->error, "debug" => $debug];
                }

                $updateStmt->bind_param("i", $planId);

                if ($updateStmt->execute()) {
                    return ["status" => "success", "message" => "Subscription cancelled successfully", "debug" => $debug];
                } else {
                    $debug["update_error"] = $updateStmt->error;
                    return ["status" => "error", "message" => "Failed to cancel subscription: " . $updateStmt->error, "debug" => $debug];
                }
            } else {
                // No active subscription found, check if there are any subscriptions at all
                $allPlansSql = "SELECT * FROM user_plans WHERE user_id = ? ORDER BY date_end DESC";
                $allPlansStmt = $conn->prepare($allPlansSql);

                if ($allPlansStmt === false) {
                    $debug["all_plans_prepare_error"] = $conn->error;
                    return ["status" => "error", "message" => "Database query preparation failed: " . $conn->error, "debug" => $debug];
                }

                $allPlansStmt->bind_param("i", $this->userId);
                $allPlansStmt->execute();
                $allPlansResult = $allPlansStmt->get_result();
                $debug["all_plans_count"] = $allPlansResult->num_rows;

                if ($allPlansResult->num_rows > 0) {
                    $debug["all_plans"] = [];
                    while ($row = $allPlansResult->fetch_assoc()) {
                        $debug["all_plans"][] = $row;
                    }
                    return ["status" => "error", "message" => "No active subscription found. All your subscriptions have already expired or been cancelled.", "debug" => $debug];
                } else {
                    return ["status" => "error", "message" => "No subscription records found for your account.", "debug" => $debug];
                }
            }
        } catch (Exception $e) {
            $debug["exception"] = $e->getMessage();
            return ["status" => "error", "message" => "An error occurred: " . $e->getMessage(), "debug" => $debug];
        }
    }

    public function checkSubscriptionStatus()
    {
        $conn = $this->connect();
        $result = ["status" => "error", "message" => "No subscription found", "details" => []];

        try {
            // Get all user plans
            $allPlansSql = "SELECT id, plan_id, state_date, date_end, amount, status 
                        FROM user_plans 
                        WHERE user_id = ? 
                        ORDER BY date_end DESC";

            $allPlansStmt = $conn->prepare($allPlansSql);

            if ($allPlansStmt === false) {
                return ["status" => "error", "message" => "Database query preparation failed: " . $conn->error];
            }

            $allPlansStmt->bind_param("i", $this->userId);
            $allPlansStmt->execute();
            $allPlansResult = $allPlansStmt->get_result();

            if ($allPlansResult->num_rows > 0) {
                $result["details"]["all_plans"] = [];
                while ($row = $allPlansResult->fetch_assoc()) {
                    // Add human-readable status
                    $row["status_text"] = ($row["status"] == 1) ? "Active" : "Inactive";

                    // Add plan details if possible
                    $planDetailsSql = "SELECT plan_name FROM admin_plan WHERE id = ?";
                    $planDetailsStmt = $conn->prepare($planDetailsSql);

                    if ($planDetailsStmt !== false) {
                        $planDetailsStmt->bind_param("i", $row["plan_id"]);
                        $planDetailsStmt->execute();
                        $planDetailsResult = $planDetailsStmt->get_result();

                        if ($planDetailsResult->num_rows > 0) {
                            $planDetails = $planDetailsResult->fetch_assoc();
                            $row["plan_name"] = $planDetails["plan_name"];
                        }
                    }

                    $result["details"]["all_plans"][] = $row;
                }
            }

            // Check for active plans specifically
            $activePlansSql = "SELECT COUNT(*) as count 
                          FROM user_plans 
                          WHERE user_id = ? AND date_end >= CURDATE() AND status = 1";

            $activePlansStmt = $conn->prepare($activePlansSql);

            if ($activePlansStmt !== false) {
                $activePlansStmt->bind_param("i", $this->userId);
                $activePlansStmt->execute();
                $activePlansResult = $activePlansStmt->get_result();

                if ($activePlansResult->num_rows > 0) {
                    $row = $activePlansResult->fetch_assoc();
                    $result["details"]["active_plans_count"] = $row["count"];

                    if ($row["count"] > 0) {
                        $result["status"] = "success";
                        $result["message"] = "User has active subscription(s)";
                    } else {
                        $result["status"] = "success";
                        $result["message"] = "User has no active subscriptions";
                    }
                }
            }

            return $result;
        } catch (Exception $e) {
            return ["status" => "error", "message" => "An error occurred: " . $e->getMessage()];
        }
    }
}
