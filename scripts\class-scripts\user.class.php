
<?php

include "../config/encryption-handlers.php";

class User extends DBconnection
{
    private $_appDataConfigs;

    public function __construct()
    {
        // init statements here ....
        $this->_appDataConfigs = include('../config/app-data-configs.php');
    }

    public function fetchCuratorData($func_photoId)
    {

        $PASSED_ID = decrypt_data($func_photoId);

        // $PASSED_ID = 1;

        $sql = "SELECT user_id FROM images WHERE id = '$PASSED_ID'";
        $runSQL = mysqli_query($this->connect(), $sql);

        if (!$runSQL) {
            $responseBody = array(
                "message" => "error"
            );

            echo json_encode($responseBody);
        } else {
            while ($row = mysqli_fetch_assoc($runSQL)) {
                $USER_ID = $row['user_id'];
            }

            // $USER_ID = 1;

            $sql2 = "SELECT * FROM users WHERE id = '$USER_ID'";
            $runSQL2 = mysqli_query($this->connect(), $sql2);

            while ($row2 = mysqli_fetch_assoc($runSQL2)) {
                $AVATAR = $row2['avatar'];
                $USERNAME = $row2['username'];
                $USERID = $row2['id'];
            }

            $followersCount = $this->getFollowersCount($USER_ID);
            $viewsCount = $this->getImageViewsCount($func_photoId);
            $likesCount = $this->getImageLikesCount($func_photoId);
            $downloadsCount = $this->getImageDownloadsCount($func_photoId);

            // declare empty array to push array datas into
            $responseData = array();

            // prepare response body
            $responseBody = array(
                "message" => "Curator data fetched successfully",
                "avatar" => $AVATAR,
                "username" => $USERNAME,
                "followers" => $followersCount,
                "views" => $viewsCount,
                "likes" => $likesCount,
                "downloads" => $downloadsCount,
                "user_id" => $USERID,
                "encrypted_id" => secured_encryption($USERID)
            );

            // send response data
            echo json_encode($responseBody);
        }
    }

    // public function fetchPurchaseHistories($userId, $offset, $searchphrase, $startdate, $enddate)
    // { // pass 66 as param for test
    //     $currentDate = date("Y-m-d");

    //     $sql = "SELECT * FROM puchase_history a INNER JOIN images b ON a.img_id = b.id WHERE a.user_id = $userId";

    //     if ($startdate !== '' && $enddate !== '') $sql = $sql . " AND a.Date_created BETWEEN '$startdate' AND '$enddate'";
    //     if ($startdate !== '' && $enddate == '') $sql = $sql . " AND a.Date_created BETWEEN '$startdate' AND '$currentDate'";
    //     if ($startdate == '' && $enddate !== '') $sql = $sql . " AND a.Date_created BETWEEN '1999-01-01' AND '$enddate'";

    //     if ($searchphrase !== '') $sql = $sql . " AND b.title LIKE '%$searchphrase%'";

    //     // if($startdate !== '' || $enddate !== '') {
    //     $runCountSQL2 = mysqli_query($this->connect(), $sql);
    //     $countRow2 = mysqli_fetch_assoc($runCountSQL2);
    //     $count = mysqli_num_rows($runCountSQL2);
    //     // }

    //     $sql = $sql . " LIMIT " . $this->_appDataConfigs['table_paginations'];
    //     if ($offset > 0) $sql = $sql . " OFFSET $offset";

    //     $runSQL = mysqli_query($this->connect(), $sql);

    //     // $count = mysqli_num_rows($runSQL);

    //     // declare empty array to push array datas into
    //     $purchaseHistories = array();

    //     if (!$runSQL) {
    //         echo $sql;
    //         $responseBody = array(
    //             "message" => "error"
    //         );

    //         echo json_encode($responseBody);
    //     } else {
    //         if (mysqli_num_rows($runSQL) < 1) {
    //             $responseBody = array(
    //                 "message" => "no result"
    //             );

    //             echo json_encode($responseBody);
    //         } else {
    //             while ($row = mysqli_fetch_assoc($runSQL)) {
    //                 $purchaseData = array(
    //                     "image_id" => $row['img_id'],
    //                     "image_amount" => $row['amount'],
    //                     "image_name" => $row['title'],
    //                     "image_tags" => $row['tags']
    //                 );

    //                 array_push($purchaseHistories, $purchaseData);
    //             }


    //             // to debug add: "SQL_QUERY" => $sql
    //             $responseBody = array(
    //                 "message" => "Purchase histories fetched successfully",
    //                 "pagination_configurations" => [
    //                     "row_set" => $this->_appDataConfigs['table_paginations'],
    //                     "page_divisions" => ceil($count / $this->_appDataConfigs['table_paginations']),
    //                     "db_result_count" => $count
    //                 ]
    //             );
    //             $responseBody["purchase_histories"] = $purchaseHistories;

    //             // send response data
    //             echo json_encode($responseBody);
    //         }
    //     }
    // }

    public function fetchUserPlans($userId, $offset, $searchphrase, $startdate, $enddate)
    {
        $currentDate = date("Y-m-d");

        $sql = "SELECT * FROM user_plans a INNER JOIN admin_plan b ON a.plan_id = b.id WHERE a.user_id = $userId";

        if ($startdate !== '' && $enddate !== '') $sql = $sql . " AND b.date_created BETWEEN '$startdate' AND '$enddate'";
        if ($startdate !== '' && $enddate == '') $sql = $sql . " AND b.date_created BETWEEN '$startdate' AND '$currentDate'";
        if ($startdate == '' && $enddate !== '') $sql = $sql . " AND b.date_created BETWEEN '1999-01-01' AND '$enddate'";

        if ($searchphrase !== '') $sql = $sql . " AND b.plan_name LIKE '%$searchphrase%'";

        $runCountSQL2 = mysqli_query($this->connect(), $sql);
        $countRow2 = mysqli_fetch_assoc($runCountSQL2);
        $count = mysqli_num_rows($runCountSQL2);

        $sql = $sql . " LIMIT " . $this->_appDataConfigs['table_paginations'];
        if ($offset > 0) $sql = $sql . " OFFSET $offset";

        $runSQL = mysqli_query($this->connect(), $sql);

        // declare empty array to push array datas into
        $userPlans = array();

        if (!$runSQL) {
            // echo $sql;
            $responseBody = array(
                "message" => "error"
            );

            echo json_encode($responseBody);
        } else {
            if (mysqli_num_rows($runSQL) < 1) {
                $responseBody = array(
                    "message" => "no result"
                );

                echo json_encode($responseBody);
            } else {
                while ($row = mysqli_fetch_assoc($runSQL)) {
                    $planData = array(
                        "plan_name" => $row['plan_name'],
                        "amount" => $row['amount'],
                        "expiry_date" => $row['expire_date'],
                        "status" => $row['status'] == 1 ? 'Active' : 'Expired'
                    );

                    array_push($userPlans, $planData);
                }

                // to debug add: "SQL_QUERY" => $sql
                $responseBody = array(
                    "message" => "User plans fetched successfully",
                    "pagination_configurations" => [
                        "row_set" => $this->_appDataConfigs['table_paginations'],
                        "page_divisions" => ceil($count / $this->_appDataConfigs['table_paginations']),
                        "db_result_count" => $count
                    ]
                );
                $responseBody["plans"] = $userPlans;

                // send response data
                echo json_encode($responseBody);
            }
        }
    }

    public function getFollowersCount($userId)
    {
        $sql = "SELECT COUNT(*) AS `count` FROM followers WHERE following_id = '$userId'";
        $runSQL = mysqli_query($this->connect(), $sql);

        if (!$runSQL) {
            return 0;
        } else {
            $row = mysqli_fetch_assoc($runSQL);
            $count = $row['count'];

            return $count;
        }
    }

    public function getImageViewsCount($photoId)
    {
        $sql = "SELECT COUNT(*) AS `count` FROM visits WHERE images_id = '$photoId'";
        $runSQL = mysqli_query($this->connect(), $sql);

        if (!$runSQL) {
            return 0;
        } else {
            $row = mysqli_fetch_assoc($runSQL);
            $count = $row['count'];

            return $count;
        }
    }

    public function getImageLikesCount($photoId)
    {
        $sql = "SELECT COUNT(*) AS `count` FROM likes WHERE images_id = '$photoId'";
        $runSQL = mysqli_query($this->connect(), $sql);

        if (!$runSQL) {
            return 0;
        } else {
            $row = mysqli_fetch_assoc($runSQL);
            $count = $row['count'];

            return $count;
        }
    }

    public function getImageDownloadsCount($photoId)
    {
        $sql = "SELECT COUNT(*) AS `count` FROM downloads WHERE images_id = '$photoId'";
        $runSQL = mysqli_query($this->connect(), $sql);

        if (!$runSQL) {
            return 0;
        } else {
            $row = mysqli_fetch_assoc($runSQL);
            $count = $row['count'];

            return $count;
        }
    }

    public function fetchMightLikeImages($func_photoId)
    {

        // $PASSED_ID = decrypt_data($func_photoId);
        $PASSED_ID = 130;

        $sql = "SELECT tags FROM images WHERE id = '$PASSED_ID'";
        $runSQL = mysqli_query($this->connect(), $sql);

        // declare empty array to push array datas into
        $responseData = array();

        if (!$runSQL) {
            $responseBody = array(
                "message" => "error"
            );

            echo json_encode($responseBody);
        } else {
            $row = mysqli_fetch_assoc($runSQL);
            $TAGS = $row['tags'];

            // now fetch images from db table where tags are similar
            $str_arr = explode(",", $TAGS);

            $imagesArray = array();

            $sql2 = "SELECT id, user_id, tags, thumbnail FROM images";
            $runSQL2 = mysqli_query($this->connect(), $sql2);

            while ($row2 = mysqli_fetch_assoc($runSQL2)) {

                if ($row2['id'] != $PASSED_ID) {
                    for ($i = 0; $i < count($str_arr); $i++) {
                        // print_r($str_arr[$i]);
                        // echo "<br><br>";

                        if (str_contains($row2['tags'], strtolower($str_arr[$i]))) {
                            // append to images to be pushed	
                            $imagesArray[$i]['id'] = $row2['id'];
                            $imagesArray[$i]['thumbnail'] = $row2['thumbnail'];

                            // get image's user detail
                            $sql3 = "SELECT username FROM users WHERE id = " . $row2['user_id'] . "";
                            $runSQL3 = mysqli_query($this->connect(), $sql3);
                            $row3 = mysqli_fetch_assoc($runSQL3);
                            $USERNAME = $row3['username'];

                            $imagesArray[$i]['user'] = $USERNAME;
                        }
                    }
                }
            }

            // prepare response body
            $responseBody = array(
                "message" => "Images fetched successfully",
            );

            $responseBody["images"] = $imagesArray;

            // send response data
            array_push($responseData, $responseBody);

            echo json_encode($responseBody);
        }
    }

    public function fetchSimilarImages($func_photoId)
    {

        // $PASSED_ID = decrypt_data($func_photoId);
        $PASSED_ID = 130;

        $sql = "SELECT tags FROM images WHERE id = '$PASSED_ID'";
        $runSQL = mysqli_query($this->connect(), $sql);

        // declare empty array to push array datas into
        $responseData = array();

        if (!$runSQL) {
            $responseBody = array(
                "message" => "error"
            );

            echo json_encode($responseBody);
        } else {
            $row = mysqli_fetch_assoc($runSQL);
            $TAGS = $row['tags'];

            // now fetch images from db table where tags are similar
            $str_arr = explode(",", $TAGS);

            $imagesArray = array();

            $sql = "SELECT colors FROM images WHERE id = '$PASSED_ID'";
            $runSQL = mysqli_query($this->connect(), $sql);
            $row = mysqli_fetch_assoc($runSQL);
            $COLORS = $row['colors'];

            $colors_arr = explode(",", $COLORS);

            $sql2 = "SELECT id, user_id, tags, colors, thumbnail FROM images";
            $runSQL2 = mysqli_query($this->connect(), $sql2);

            $current_index = 0;

            $maxIndex = max(count($str_arr), count($colors_arr));

            while ($row2 = mysqli_fetch_assoc($runSQL2)) {

                if ($row2['id'] != $PASSED_ID) {
                    for ($i = 0; $i < $maxIndex; $i++) {
                        // print_r($str_arr[$i]);
                        // echo "<br><br>";

                        if ($i < count($str_arr)) {
                            if (str_contains($row2['tags'], strtolower($str_arr[$i]))) {
                                // append to images to be pushed	
                                $imagesArray[$i]['id'] = $row2['user_id'];
                                $imagesArray[$i]['thumbnail'] = $row2['thumbnail'];

                                // get image's user detail
                                $sql3 = "SELECT username FROM users WHERE id = " . $row2['user_id'] . "";
                                $runSQL3 = mysqli_query($this->connect(), $sql3);
                                $row3 = mysqli_fetch_assoc($runSQL3);
                                $USERNAME = $row3['username'];

                                $imagesArray[$i]['user'] = $USERNAME;
                                continue;
                            }
                        }

                        if ($i < count($colors_arr)) {
                            if (str_contains($row2['colors'], strtolower($colors_arr[$i]))) {
                                // append to images to be pushed	
                                $imagesArray[$i]['id'] = $row2['user_id'];
                                $imagesArray[$i]['thumbnail'] = $row2['thumbnail'];

                                // get image's user detail
                                $sql3 = "SELECT username FROM users WHERE id = " . $row2['user_id'] . "";
                                $runSQL3 = mysqli_query($this->connect(), $sql3);
                                $row3 = mysqli_fetch_assoc($runSQL3);
                                $USERNAME = $row3['username'];

                                $imagesArray[$i]['user'] = $USERNAME;
                            }
                        }

                        if ($i > 12) {
                            break;
                        }

                        $current_index = $i;
                    }
                }
            }
            // prepare response body
            $responseBody = array(
                "message" => "Images fetched successfully",
            );

            $responseBody["images"] = array_values($imagesArray);

            // send response data
            array_push($responseData, $responseBody);

            echo json_encode($responseBody);
        }
    }

    public function fetchArtistOthers($func_photoId)
    {
        // $PASSED_ID = decrypt_data($func_photoId);
        $PASSED_ID = 130;


        $query = "SELECT user_id FROM images WHERE id = $PASSED_ID";
        $runSQL = mysqli_query($this->connect(), $query);

        $responseData = array();
        if (!$runSQL) {
            $responseBody = array(
                "message" => "error"
            );

            echo json_encode($responseBody);
        } else {
            $row = mysqli_fetch_assoc($runSQL);
            $user_id = $row['user_id'];

            $query = "SELECT username FROM users WHERE id = $user_id";
            $runSQL = mysqli_query($this->connect(), $query);
            $row = mysqli_fetch_assoc($runSQL);
            $USERNAME = $row['username'];

            $query = "SELECT * FROM images WHERE user_id = $user_id";
            $runSQL = mysqli_query($this->connect(), $query);

            $imagesArray = array();
            $i = 0;

            while ($result = mysqli_fetch_assoc($runSQL)) {
                $imagesArray[$i]['id'] = $result['user_id'];
                $imagesArray[$i]['thumbnail'] = $result['thumbnail'];

                $imagesArray[$i]['user'] = $USERNAME;
                $i += 1;
            }

            $responseBody = array(
                "message" => "Images fetched successfully",
            );

            $responseBody["images"] = $imagesArray;

            // send response data
            array_push($responseData, $responseBody);

            echo json_encode($responseBody);
        }
    }

    public function fetchPurchaseHistories($userId, $offset, $searchphrase, $startdate, $enddate)
    {
        $currentDate = date("Y-m-d");
        $allPurchases = array();

        // First query: Get image purchases (using the correct table name)
        $sql1 = "SELECT ph.*, i.title, i.tags, 'image' as purchase_type,
                 COALESCE(ph.payment_status, ph.status, 1) as status,
                 ph.Date_created as purchase_date
             FROM puchase_history ph
             INNER JOIN images i ON ph.img_id = i.id
             WHERE ph.user_id = $userId";

        if ($startdate !== '' && $enddate !== '') $sql1 = $sql1 . " AND ph.Date_created BETWEEN '$startdate' AND '$enddate'";
        if ($startdate !== '' && $enddate == '') $sql1 = $sql1 . " AND ph.Date_created BETWEEN '$startdate' AND '$currentDate'";
        if ($startdate == '' && $enddate !== '') $sql1 = $sql1 . " AND ph.Date_created BETWEEN '1999-01-01' AND '$enddate'";
        if ($searchphrase !== '') $sql1 = $sql1 . " AND i.title LIKE '%$searchphrase%'";

        // Second query: Get subscription plans
        $sql2 = "SELECT up.*, ap.plan_name, ap.amount, 'subscription' as purchase_type,
             CASE WHEN up.date_end >= CURDATE() THEN 'Active' ELSE 'Expired' END as status,
             COALESCE(up.date_created, up.state_date) as purchase_date
             FROM user_plans up
             INNER JOIN admin_plan ap ON up.plan_id = ap.id
             WHERE up.user_id = $userId";

        if ($startdate !== '' && $enddate !== '') $sql2 = $sql2 . " AND COALESCE(up.date_created, up.state_date) BETWEEN '$startdate' AND '$enddate'";
        if ($startdate !== '' && $enddate == '') $sql2 = $sql2 . " AND COALESCE(up.date_created, up.state_date) BETWEEN '$startdate' AND '$currentDate'";
        if ($startdate == '' && $enddate !== '') $sql2 = $sql2 . " AND COALESCE(up.date_created, up.state_date) BETWEEN '1999-01-01' AND '$enddate'";
        if ($searchphrase !== '') $sql2 = $sql2 . " AND ap.plan_name LIKE '%$searchphrase%'";

        // Execute first query
        $runSQL1 = mysqli_query($this->connect(), $sql1);
        if ($runSQL1 && mysqli_num_rows($runSQL1) > 0) {
            while ($row = mysqli_fetch_assoc($runSQL1)) {
                $purchaseData = array(
                    "purchase_id" => $row['id'],
                    "purchase_type" => "Image",
                    "item_name" => $row['title'],
                    "item_id" => $row['img_id'],
                    "amount" => $row['amount'],
                    // "purchase_date" => $row['Date_created'],
                    "purchase_date" => $row['purchase_date'],
                    "status" => $row['status'] == 1 ? 'Successful' : 'Failed',
                    "tags" => $row['tags']
                );
                array_push($allPurchases, $purchaseData);
            }
        }

        // Execute second query
        $runSQL2 = mysqli_query($this->connect(), $sql2);
        if ($runSQL2 && mysqli_num_rows($runSQL2) > 0) {
            while ($row = mysqli_fetch_assoc($runSQL2)) {
                // Check if the plan is actually active by comparing current date with expiry date
                $isActive = strtotime($row['date_end']) >= strtotime(date('Y-m-d'));

                $purchaseData = array(
                    "purchase_id" => $row['id'],
                    "purchase_type" => "Subscription",
                    "item_name" => $row['plan_name'],
                    "item_id" => $row['plan_id'],
                    "amount" => $row['amount'],
                    "purchase_date" => $row['purchase_date'],
                    "status" => $isActive ? 'Active' : 'Expired',
                    "expiry_date" => $row['date_end']
                );
                array_push($allPurchases, $purchaseData);
            }
        }

        // Sort all purchases by date (newest first)
        usort($allPurchases, function ($a, $b) {
            return strtotime($b['purchase_date']) - strtotime($a['purchase_date']);
        });

        // Count total records for pagination
        $count = count($allPurchases);

        // Apply pagination
        $paginatedPurchases = array_slice($allPurchases, $offset, $this->_appDataConfigs['table_paginations']);

        if (count($allPurchases) < 1) {
            $responseBody = array(
                "message" => "no result",
                "error_message" => "No purchase history found for this user."
            );
            echo json_encode($responseBody);
            return;
        }


        if (count($paginatedPurchases) < 1) {
            $responseBody = array(
                "message" => "no result"
            );
            echo json_encode($responseBody);
        } else {
            $responseBody = array(
                "message" => "Purchase histories fetched successfully",
                "pagination_configurations" => [
                    "row_set" => $this->_appDataConfigs['table_paginations'],
                    "page_divisions" => ceil($count / $this->_appDataConfigs['table_paginations']),
                    "db_result_count" => $count
                ],
                "purchase_histories" => $paginatedPurchases
            );
            echo json_encode($responseBody);
        }
    }
}

?>