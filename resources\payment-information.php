<style>
    .hidden {
        display: none;
    }

    .show {
        display: block;
    }

    /* .btn.btn-primary {
        background-color: #198754;
    } */

    #card-loader {
        display: none;
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        padding: 20px;
        background-color: rgba(0, 0, 0, 0.8);
        color: grey;
        border-radius: 5px;
        z-index: 1000;
    }

    .form-check {
        color: grey;
    }

    .activated-default-card {
        height: 20px;
        width: 20px;
    }


    /* Enhanced Modal Styles */
    .modal-content {
        border: none;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .modal-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #eee;
        padding: 1.25rem 1.5rem;
    }

    .modal-title {
        color: #333;
        font-weight: 600;
    }

    .modal-body {
        padding: 1.5rem;
    }

    .modal-footer {
        background-color: #f8f9fa;
        border-top: 1px solid #eee;
        padding: 1.25rem 1.5rem;
    }

    .btn-close {
        opacity: 0.6;
        transition: opacity 0.2s;
    }

    .btn-close:hover {
        opacity: 1;
    }

    .card-type-options {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-top: 0.5rem;
    }

    .form-check-inline {
        margin-right: 1rem;
    }

    .form-label {
        font-weight: 500;
        color: #555;
    }

    .form-control,
    .form-select {
        border-radius: 8px;
        padding: 0.6rem 0.75rem;
        border: 1px solid #ddd;
        transition: border-color 0.2s, box-shadow 0.2s;
    }

    .form-control:focus,
    .form-select:focus {
        border-color: #ff565b;
        box-shadow: 0 0 0 0.25rem rgba(255, 86, 91, 0.25);
    }

    .btn-primary {
        background-color: #ff565b;
        border-color: #ff565b;
    }

    .btn-primary:hover {
        background-color: #e64a4f;
        border-color: #e64a4f;
    }

    .btn-outline-secondary {
        color: #6c757d;
        border-color: #6c757d;
    }

    .btn-outline-secondary:hover {
        background-color: #6c757d;
        color: white;
    }

    /* Enhanced card details in removal modal */
    .card-preview {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 20px !important;
        margin: 15px 0;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        border: 1px solid #dee2e6 !important;
    }

    #card-number-preview,
    #card-holder-preview {
        font-size: 1.2rem !important;
        font-weight: 600 !important;
        color: #333;
        padding: 5px 0;
    }

    .form-label.text-muted {
        font-size: 0.9rem;
        font-weight: 500;
    }

    #card-icon {
        min-width: 60px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    #card-icon img {
        max-height: 40px;
        max-width: 60px;
    }

    /* Make the modal wider on mobile */
    @media (max-width: 576px) {
        .modal-dialog.modal-lg {
            max-width: 95%;
            margin: 1.75rem auto;
        }

        .card-preview {
            padding: 15px !important;
        }
    }

    /* Make the table more responsive */
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    /* Ensure the card holder column is visible */
    #payment_info_view_table th:nth-child(5),
    #payment_info_view_table td:nth-child(5) {
        min-width: 150px;
    }
</style>
</head>

<body>
    <div class="container">
        <div class="py-5 text-center">
            <h2>Payment Information</h2>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div id="payment-info-noresults-msg" class="alert alert-warning hidden" role="alert">
                    No payment cards added.
                </div>
                <!-- Cards table -->
                <div class="table-responsive">
                    <table id="payment_info_view_table" class="table table-hover align-middle">
                        <thead class="table-light">
                            <tr>
                                <th>Card Type</th>
                                <th>Card Number</th>
                                <th>Expiry</th>
                                <th>Default</th>
                                <th>Card Holder</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Table content will be loaded dynamically -->
                        </tbody>
                    </table>
                </div>

                <div class="row pt-3">
                    <div class="col-6 col-lg-3 pt-2">
                        <a href="#" class="btn btn-secondary btn-block choose-default-card">
                            <i class="fas fa-credit-card me-2"></i>Choose default card
                        </a>
                    </div>
                    <div class="col-6 col-lg-3">
                        <a href="#" id="new-paycard-btn" class="btn btn-primary btn-block">
                            <i class="fas fa-plus me-2"></i>Add new card
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Default Card Selection Modal -->
    <div class="modal fade" id="defaultCardModal" tabindex="-1" aria-labelledby="defaultCardModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="defaultCardModalLabel">Choose Default Payment Card</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p class="text-muted mb-4">Select the card you want to use as your default payment method.</p>

                    <div class="table-responsive">
                        <table class="table table-hover" id="default_card_selection_table">
                            <thead class="table-light">
                                <tr>
                                    <th>Card Type</th>
                                    <th>Card Number</th>
                                    <th>Expiry</th>
                                    <th>Card Holder</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Card rows will be populated dynamically -->
                            </tbody>
                        </table>
                    </div>

                    <div id="no-cards-message" class="alert alert-info d-none">
                        You don't have any payment cards yet. Please add a card first.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Improved Modal -->
    <div class="modal fade" id="addCardModal" tabindex="-1" role="dialog" aria-labelledby="addCardModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addCardModalLabel">Add New Payment Card</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addCardForm">
                        <div class="mb-3">
                            <label for="cardNumber" class="form-label">Card Number</label>
                            <input type="text" class="form-control" id="cardNumber" placeholder="Enter Card Number">
                        </div>
                        <div class="mb-3">
                            <label for="cardName" class="form-label">Card Holder Name</label>
                            <input type="text" class="form-control" id="cardName" placeholder="Enter Card Holder Name">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Card Type</label>
                            <div class="card-type-options">
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="cardType" id="mastercard" value="MasterCard" checked>
                                    <label class="form-check-label" for="mastercard">MasterCard</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="cardType" id="visa" value="Visa">
                                    <label class="form-check-label" for="visa">Visa</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="cardType" id="amex" value="American Express">
                                    <label class="form-check-label" for="amex">American Express</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="cardType" id="discover" value="Discover">
                                    <label class="form-check-label" for="discover">Discover</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="cardType" id="other" value="Other">
                                    <label class="form-check-label" for="other">Other</label>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="expiryMonth" class="form-label">Expiry Month</label>
                                <select class="form-select" id="expiryMonth">
                                    <option value="">MM</option>
                                    <option value="01">01</option>
                                    <option value="02">02</option>
                                    <option value="03">03</option>
                                    <option value="04">04</option>
                                    <option value="05">05</option>
                                    <option value="06">06</option>
                                    <option value="07">07</option>
                                    <option value="08">08</option>
                                    <option value="09">09</option>
                                    <option value="10">10</option>
                                    <option value="11">11</option>
                                    <option value="12">12</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="expiryYear" class="form-label">Expiry Year</label>
                                <select class="form-select" id="expiryYear">
                                    <option value="">YYYY</option>
                                    <?php
                                    $currentYear = date("Y");
                                    for ($i = 0; $i < 10; $i++) {
                                        echo '<option value="' . ($currentYear + $i) . '">' . ($currentYear + $i) . '</option>';
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="cvv" class="form-label">CVV</label>
                            <input type="text" class="form-control" id="cvv" placeholder="CVV">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveCardBtn">Save Card</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Improved Edit Card Modal -->
    <div class="modal fade" id="updateCardModal" tabindex="-1" role="dialog" aria-labelledby="updateCardModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="updateCardModalLabel">Edit Payment Card</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="updateCardForm">
                        <input type="hidden" id="update_id" name="id">

                        <div class="mb-3">
                            <label for="update_card_no" class="form-label">Card Number</label>
                            <input type="text" class="form-control" id="update_card_no" name="card_no" placeholder="Enter Card Number">
                        </div>

                        <div class="mb-3">
                            <label for="update_card_holder_name" class="form-label">Card Holder Name</label>
                            <input type="text" class="form-control" id="update_card_holder_name" name="card_holder_name" placeholder="Enter Card Holder Name">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Card Type</label>
                            <div class="d-flex gap-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="card_type" id="update_visa" value="visa">
                                    <label class="form-check-label" for="update_visa">
                                        <img src="assets/img/gallery/Visa-card.png" alt="Visa" height="30">
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="card_type" id="update_mastercard" value="mastercard">
                                    <label class="form-check-label" for="update_mastercard">
                                        <img src="assets/img/gallery/Master-card.png" alt="Mastercard" height="30">
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="card_type" id="update_amex" value="american express">
                                    <label class="form-check-label" for="update_amex">
                                        <img src="assets/img/gallery/amex-card.png" alt="American Express" height="30">
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="update_exp_month" class="form-label">Expiry Month</label>
                                <select class="form-select" id="update_exp_month" name="exp_month">
                                    <option value="">Month</option>
                                    <option value="01">01</option>
                                    <option value="02">02</option>
                                    <option value="03">03</option>
                                    <option value="04">04</option>
                                    <option value="05">05</option>
                                    <option value="06">06</option>
                                    <option value="07">07</option>
                                    <option value="08">08</option>
                                    <option value="09">09</option>
                                    <option value="10">10</option>
                                    <option value="11">11</option>
                                    <option value="12">12</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="update_exp_year" class="form-label">Expiry Year</label>
                                <select class="form-select" id="update_exp_year" name="exp_year">
                                    <option value="">Year</option>
                                    <option value="2023">2023</option>
                                    <option value="2024">2024</option>
                                    <option value="2025">2025</option>
                                    <option value="2026">2026</option>
                                    <option value="2027">2027</option>
                                    <option value="2028">2028</option>
                                    <option value="2029">2029</option>
                                    <option value="2030">2030</option>
                                    <option value="2031">2031</option>
                                    <option value="2032">2032</option>
                                    <option value="2033">2033</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="update_default_card" class="form-label">Set as Default Card</label>
                            <select class="form-select" id="update_default_card" name="default_card">
                                <option value="0">No</option>
                                <option value="1">Yes</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="update_add_reference" class="form-label">Additional Reference (Optional)</label>
                            <input type="text" class="form-control" id="update_add_reference" name="add_reference" placeholder="Additional Reference">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="updateCardBtn">Update Card</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Improved Card Removal Confirmation Modal with wider card details -->
    <div class="modal fade" id="removal-alert" tabindex="-1" role="dialog" aria-labelledby="removalAlertTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="removalAlertTitle">Confirm Card Removal</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3">
                        <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                    </div>
                    <p class="text-center fs-5">Are you sure you want to remove this payment card?</p>

                    <div class="card-preview mt-4 p-4 border rounded bg-light">
                        <div class="d-flex align-items-center mb-3">
                            <div id="card-icon" class="me-3">
                                <!-- Card icon will be inserted here via JS -->
                            </div>
                            <div class="fs-5 fw-bold">Card Details</div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted mb-1">Card Number:</label>
                                <div id="card-number-preview" class="fs-5 fw-bold">**** **** **** ****</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted mb-1">Card Holder:</label>
                                <div id="card-holder-preview" class="fs-5 fw-bold">-</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="yes-remove">
                        <i class="fas fa-trash-alt me-2"></i>Remove Card
                    </button>
                </div>
            </div>
        </div>
    </div>


    <div id="card-loader">Loading...</div>