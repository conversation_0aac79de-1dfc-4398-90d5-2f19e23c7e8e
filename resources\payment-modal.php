<div id="default-card" class="payment">
    <a data-toggle="modal" data-target="#exampleModal" class="change-card-btn">Add</a>

    <!-- Modal -->
    <div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Select card</h5>
                </div>
                <div class="modal-body" id="payment_cards_modal">


                    <h4 class="headline-primary">Payment</h3>
                        <div class="ux-card">
                            <div class="payment-card-details" id="added_payment_card">
                                <!-- <img src="assets/img/gallery/Visa-card.png" class="card-type" />
                <div class="card-info">
                    <p>**** **** **** **22</p>
                    <span class="card-exp">
                        <em>Expiry Date</em>
                        <p>06/28</p>
                    </span>

                </div> -->
                            </div>
                        </div>
                        </form>

                        <div></div>
                        <button class="w-100 btn btn-danger btn-lg" type="submit" onclick="make_payment(100, '<EMAIL>', 1, 1)">Continue to checkout</button>

                        <script>
                            function payWithPaystack() {
                                var handler = PaystackPop.setup({
                                    key: 'YOUR_PUBLIC_KEY', // Replace with your public key
                                    email: "<EMAIL>",
                                    amount: 100 * 100, // the amount value is multiplied by 100 to convert to the lowest currency unit
                                    currency: 'NGN', // Use GHS for Ghana Cedis or USD for US Dollars
                                    ref: 'YOUR_REFERENCE', // Replace with a reference you generated
                                    callback: function(response) {
                                        //this happens after the payment is completed successfully
                                        var reference = response.reference;
                                        alert('Payment complete! Reference: ' + reference);
                                        // Make an AJAX call to your server with the reference to verify the transaction
                                    },
                                    onClose: function() {
                                        alert('Transaction was not completed, window closed.');
                                    },
                                });
                                handler.openIframe();
                            }
                        </script>
                </div>