<?php
class PaymentInfo extends DBconnection
{

    // public function fetchPaymentInfo($userId) {
    //     $conn = $this->connect();
    //     $sql = "SELECT * FROM payment_card WHERE user_id = ? ORDER BY default_card DESC, id DESC";
    //     $stmt = $conn->prepare($sql);
    //     $stmt->bind_param("i", $userId); // "i" for integer
    //     $stmt->execute();
    //     $result = $stmt->get_result();
    //     return $result->fetch_all(MYSQLI_ASSOC);
    //     $stmt->close();
    //     $conn->close();
    // }


    public function fetchPaymentInfo($userId)
    {
        $conn = $this->connect();
        $sql = "SELECT * FROM payment_card WHERE user_id = ? ORDER BY default_card DESC, id DESC";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $userId); // "i" for integer
        $stmt->execute();
        $result = $stmt->get_result();
        $cards = $result->fetch_all(MYSQLI_ASSOC);
        $stmt->close();
        $conn->close();

        // Ensure each card has the correct card number
        foreach ($cards as &$card) {
            // Make sure card_no is properly set and not empty
            if (empty($card['card_no'])) {
                $card['card_no'] = "0000"; // Fallback if empty
            }
        }

        return $cards;
    }
    public function removePaymentCard($userId, $cardId)
    {
        $conn = $this->connect();
        $sql = "DELETE FROM payment_card WHERE user_id = ? AND id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ii", $userId, $cardId); // "ii" for two integers
        $result = $stmt->execute();
        $stmt->close();
        $conn->close();
        return $result;
    }

    public function editDefaultPaymentCard($userId, $cardId)
    {
        $conn = $this->connect();

        try {
            $conn->begin_transaction();

            // Reset all cards to non-default
            $sql = "UPDATE payment_card SET default_card = 0 WHERE user_id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $userId);
            $stmt->execute();
            $stmt->close();

            // Set the specified card as default
            $sql = "UPDATE payment_card SET default_card = 1 WHERE user_id = ? AND id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("ii", $userId, $cardId);
            $result = $stmt->execute();
            $stmt->close();

            $conn->commit();
            $conn->close();
            return $result;
        } catch (Exception $e) {
            $conn->rollback();
            $conn->close();
            throw $e;
        }
    }

    // public function addPaymentCard($userId, $cardNumber, $cardName, $expMonth, $expYear, $cardType)
    // {
    //     $conn = $this->connect();
    //     $authCode = bin2hex(random_bytes(8));

    //     $sql = "INSERT INTO payment_card
    //                 (user_id, card_no, card_holder_name, exp_month, exp_year, card_type, auth_code)
    //                 VALUES (?, ?, ?, ?, ?, ?, ?)";

    //     $stmt = $conn->prepare($sql);
    //     $stmt->bind_param("issssss", $userId, $cardNumber, $cardName, $expMonth, $expYear, $cardType, $authCode);
    //     $result = $stmt->execute();
    //     $stmt->close();
    //     $conn->close();
    //     return $result;
    // }

    // public function updatePaymentCard($userId, $cardId, $cardData)
    // {
    //     $conn = $this->connect();

    //     $sql = "UPDATE payment_card SET
    //                 card_no = ?,
    //                 card_holder_name = ?,
    //                 card_type = ?,
    //                 exp_month = ?,
    //                 exp_year = ?,
    //                 add_reference = ?,
    //                 default_card = ?
    //             WHERE user_id = ? AND id = ?";

    //     $stmt = $conn->prepare($sql);
    //     $stmt->bind_param(
    //         "ssssssiii",
    //         $cardData['card_no'],
    //         $cardData['card_holder_name'],
    //         $cardData['card_type'],
    //         $cardData['exp_month'],
    //         $cardData['exp_year'],
    //         $cardData['add_reference'],
    //         $cardData['default_card'],
    //         $userId,
    //         $cardId
    //     );
    //     $result = $stmt->execute();
    //     $stmt->close();
    //     $conn->close();
    //     return $result;
    // }


    public function addPaymentCard($userId, $cardNumber, $cardName, $expMonth, $expYear, $cardType)
    {
        $conn = $this->connect();
        $authCode = bin2hex(random_bytes(8));

        // Don't sanitize or modify the card number - keep it exactly as entered
        // Just trim any whitespace
        $cardNumber = trim($cardNumber);

        // Log the card number being added for debugging
        error_log("Adding card with number: " . $cardNumber);

        $sql = "INSERT INTO payment_card
                (user_id, card_no, card_holder_name, exp_month, exp_year, card_type, auth_code)
                VALUES (?, ?, ?, ?, ?, ?, ?)";

        $stmt = $conn->prepare($sql);
        $stmt->bind_param("issssss", $userId, $cardNumber, $cardName, $expMonth, $expYear, $cardType, $authCode);
        $result = $stmt->execute();

        // Log the result for debugging
        error_log("Card add result: " . ($result ? "success" : "failed") . ", Error: " . $stmt->error);

        $stmt->close();
        $conn->close();
        return $result;
    }

    public function updatePaymentCard($userId, $cardId, $cardData)
    {
        $conn = $this->connect();

        // Don't modify the card number - keep it exactly as entered
        $cardData['card_no'] = trim($cardData['card_no']);

        // Log the card number being updated for debugging
        error_log("Updating card with number: " . $cardData['card_no']);

        $sql = "UPDATE payment_card SET
                card_no = ?,
                card_holder_name = ?,
                card_type = ?,
                exp_month = ?,
                exp_year = ?,
                add_reference = ?,
                default_card = ?
            WHERE user_id = ? AND id = ?";

        $stmt = $conn->prepare($sql);
        $stmt->bind_param(
            "ssssssiii",
            $cardData['card_no'],
            $cardData['card_holder_name'],
            $cardData['card_type'],
            $cardData['exp_month'],
            $cardData['exp_year'],
            $cardData['add_reference'],
            $cardData['default_card'],
            $userId,
            $cardId
        );
        $result = $stmt->execute();
        $stmt->close();
        $conn->close();
        return $result;
    }
    public function getCardDetails($userId, $cardId)
    {
        $conn = $this->connect();
        $sql = "SELECT * FROM payment_card WHERE user_id = ? AND id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ii", $userId, $cardId);
        $stmt->execute();
        $result = $stmt->get_result();
        $card = $result->fetch_assoc();
        $stmt->close();
        $conn->close();
        return $card;
    }
}
