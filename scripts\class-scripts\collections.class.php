<?php

include "../config/encryption-handlers.php";

class Collections extends DBconnection
{
	public function createCollection($userid, $title, $type)
	{
		// Sanitize inputs
		$userid = (int)$userid;
		$title = mysqli_real_escape_string($this->connect(), $title);
		$type = mysqli_real_escape_string($this->connect(), $type);
		
		// Validate collection type
		if (!in_array($type, ['public', 'private'])) {
			return [
				'success' => false,
				'message' => 'Invalid collection type'
			];
		}
		
		$sql = "INSERT INTO collections(user_id, title, type) VALUES(?, ?, ?)";
		$stmt = mysqli_prepare($this->connect(), $sql);
		mysqli_stmt_bind_param($stmt, "iss", $userid, $title, $type);
		$runSQL = mysqli_stmt_execute($stmt);

		if (!$runSQL) {
			return [
				'success' => false,
				'message' => 'Failed to create collection: ' . mysqli_error($this->connect())
			];
		}
		
		return [
			'success' => true,
			'message' => 'Collection created successfully',
			'data' => [
				'collection_id' => mysqli_insert_id($this->connect()),
				'title' => $title,
				'type' => $type
			]
		];
	}

	public function updateImageInCollections($userid, $photoid, $imgCollectionStats)
	{
		$img_id = '';
        if($photoid != '') $img_id = decrypt_data($photoid);

		$responseData = array();

		try {
			foreach ($imgCollectionStats as $key => $value) {
				// echo "Key: " . $key . ", Value: " . $value . "\n";
				
				if($value == "true") {
					$sql = "INSERT INTO collections_images(collections_id, images_id) VALUES($key, $img_id)";
					$runSQL = mysqli_query($this->connect(), $sql);
				}
				else if($value == "false") {
					$sql = "DELETE FROM collections_images WHERE collections_id = $key AND images_id = $img_id";
					$runSQL = mysqli_query($this->connect(), $sql);
				}
			}
			
			$responseBody = array(
				"status" => "1",
				"message" => "collections updated"
			);

			echo json_encode($responseBody);

		} catch (\Throwable $th) {
			$responseBody = array(
				"status" => "0",
				"message"=>"error"
			);

			echo json_encode($responseBody);
		}
	}

	public function fetchCollections($userId = null)
{
    // Base query with collection images
    $baseQuery = "SELECT 
                    c.id AS collection_id, 
                    c.title AS collection_title,
                    c.type AS collection_type, 
                    CONCAT(u.firstname, ' ', u.lastname) AS user_name, 
                    COUNT(DISTINCT ci.images_id) AS image_count,
                    GROUP_CONCAT(DISTINCT 
                        JSON_OBJECT(
                            'image_id', i.id,
                            'thumbnail', i.thumbnail,
                            'preview', i.preview,
                            'title', i.title
                        )
                    ) AS collection_images
                FROM collections c 
                JOIN users u ON c.user_id = u.id 
                LEFT JOIN collections_images ci ON c.id = ci.collections_id
                LEFT JOIN images i ON ci.images_id = i.id";

    if ($userId) {
        // Query for both public collections and user's private collections
        $sql = $baseQuery . " 
                WHERE (c.type = 'public' OR (c.type = 'private' AND c.user_id = ?))
                GROUP BY c.id, c.title, c.type, u.firstname, u.lastname 
                ORDER BY c.created_at DESC";
        
        $stmt = mysqli_prepare($this->connect(), $sql);
        mysqli_stmt_bind_param($stmt, "i", $userId);
        mysqli_stmt_execute($stmt);
        $runSQL = mysqli_stmt_get_result($stmt);
    } else {
        // Query for public collections only
        $sql = $baseQuery . " 
                WHERE c.type = 'public'
                GROUP BY c.id, c.title, c.type, u.firstname, u.lastname 
                ORDER BY c.created_at DESC";
        
        $runSQL = mysqli_query($this->connect(), $sql);
    }

    // Initialize arrays for both types of collections
    $response = array(
        'private' => array(),
        'public' => array()
    );
    
    if($runSQL) {
        while($row = mysqli_fetch_assoc($runSQL)) {
            $collection_images = [];
            
            // Check if collection has any images and valid JSON data
            if (!empty($row['collection_images']) && $row['collection_images'] !== 'null') {
                $decoded_image = json_decode($row['collection_images'], true);
                // Only add image if it has valid data
                if ($decoded_image !== null && !empty($decoded_image['thumbnail'])) {
                    $decoded_image['thumbnail'] = 'peco_image_store/thumbnail/' . $decoded_image['thumbnail'];
                    $collection_images[] = $decoded_image;
                }
            }
            
            // Add images array to row data
            $row['images'] = $collection_images;
            
            // Set thumbnail based on whether collection has valid images
            if (empty($collection_images) || empty($collection_images[0]['thumbnail'])) {
                $row['thumbnail'] = 'assets/images/placeholder.jpg';
                $row['images'] = []; // Reset images array if no valid images
            } else {
                $row['thumbnail'] = $collection_images[0]['thumbnail'];
            }
            
            // Remove the raw JSON string from response
            unset($row['collection_images']);
            
            // Sort collections by type
            if ($row['collection_type'] === 'private') {
                $response['private'][] = $row;
            } else {
                $response['public'][] = $row;
            }
        }
        
        echo json_encode([
            'success' => true,
            'data' => $response
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Error fetching collections',
            'error' => mysqli_error($this->connect())
        ]);
    }
}
}

?>
