<?php
// payment-information.data.php
header("Content-Type: application/json");

if (!isset($_SESSION)) {
    session_start();
}

require_once '../class-scripts/db-connection.class.php';
require_once '../class-scripts/payment-information.class.php';

if (!isset($_SESSION['id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

// At the beginning of the file, check both possible session variables
if (!isset($_SESSION['user_id']) && !isset($_SESSION['id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

// Use whichever session variable is available
$userId = $_SESSION['user_id'] ?? $_SESSION['id'];

$userId = $_SESSION['id'];
$paymentInfo = new PaymentInfo();

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_POST['action'] ?? '';

        switch ($action) {
            case 'get-cards':
                $cards = $paymentInfo->fetchPaymentInfo($userId);
                echo json_encode([
                    'success' => true,
                    'cards' => $cards,
                    'user_email' => $_SESSION['email'] ?? ''
                ]);
                break;


            case 'remove-card':
                if (!isset($_POST['id'])) {
                    throw new Exception('Card ID is required');
                }
                $result = $paymentInfo->removePaymentCard($userId, $_POST['id']);
                echo json_encode(['success' => $result]);
                break;

            case 'set-default-card':
                if (!isset($_POST['id'])) {
                    throw new Exception('Card ID is required');
                }
                $result = $paymentInfo->editDefaultPaymentCard($userId, $_POST['id']);
                echo json_encode(['success' => $result]);
                break;

            case 'add-card':
                $required = ['cardNumber', 'cardName', 'expiryMonth', 'expiryYear', 'card_type'];
                foreach ($required as $field) {
                    if (empty($_POST[$field])) {
                        throw new Exception("Missing required field: $field");
                    }
                }

                // Don't sanitize the card number - keep it exactly as entered
                $cardNumber = trim($_POST['cardNumber']);
                $cardName = trim($_POST['cardName']);
                $expiryMonth = trim($_POST['expiryMonth']);
                $expiryYear = trim($_POST['expiryYear']);
                $cardType = trim($_POST['card_type']);

                // Log the card number for debugging
                error_log("Card number from form: " . $cardNumber);

                $result = $paymentInfo->addPaymentCard(
                    $userId,
                    $cardNumber,
                    $cardName,
                    $expiryMonth,
                    $expiryYear,
                    $cardType
                );
                echo json_encode(['success' => $result]);
                break;

            case 'update-card':
                $required = ['id', 'card_no', 'card_holder_name', 'exp_month', 'exp_year', 'card_type'];
                foreach ($required as $field) {
                    if (empty($_POST[$field])) {
                        throw new Exception("Missing required field: $field");
                    }
                }

                // Don't sanitize the card number - keep it exactly as entered
                $cardData = [
                    'card_no' => trim($_POST['card_no']),
                    'card_holder_name' => trim($_POST['card_holder_name']),
                    'card_type' => trim($_POST['card_type']),
                    'exp_month' => trim($_POST['exp_month']),
                    'exp_year' => trim($_POST['exp_year']),
                    'add_reference' => isset($_POST['add_reference']) ? trim($_POST['add_reference']) : '',
                    'default_card' => isset($_POST['default_card']) ? intval($_POST['default_card']) : 0
                ];

                // Log the card number for debugging
                error_log("Card number from update form: " . $cardData['card_no']);

                $result = $paymentInfo->updatePaymentCard($userId, $_POST['id'], $cardData);
                echo json_encode(['success' => $result]);
                break;

            // case 'update-card':
            //     $required = ['id', 'card_no', 'card_holder_name', 'exp_month', 'exp_year', 'card_type']; // Keep card_type for backend
            //     foreach ($required as $field) {
            //         if (empty($_POST[$field])) {
            //             throw new Exception("Missing required field: $field");
            //         }
            //     }

            //     $cardData = [
            //         'card_no' => $_POST['card_no'],
            //         'card_holder_name' => $_POST['card_holder_name'],
            //         'card_type' => $_POST['card_type'], // Use card_type from form
            //         'exp_month' => $_POST['exp_month'],
            //         'exp_year' => $_POST['exp_year'],
            //         'add_reference' => $_POST['add_reference'] ?? '',
            //         'default_card' => $_POST['default_card'] ?? 0
            //     ];
            //     $result = $paymentInfo->updatePaymentCard($userId, $_POST['id'], $cardData);
            //     echo json_encode(['success' => $result]);
            //     break;

            case 'get-card-details':
                if (!isset($_POST['card_id'])) {
                    throw new Exception('Card ID is required');
                }
                $card = $paymentInfo->getCardDetails($userId, $_POST['card_id']);
                if ($card) {
                    echo json_encode(['success' => true, 'card' => $card]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Card not found']);
                }
                break;

            default:
                throw new Exception('Invalid request type');
        }
    } else {
        throw new Exception('Invalid request method');
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
