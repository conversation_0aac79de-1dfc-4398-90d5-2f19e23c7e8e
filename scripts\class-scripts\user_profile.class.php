<?php
include "../config/encryption-handlers.php";

class UserProfile_Section extends DBconnection
{
    //corper emmanuel added $purchased_images_no,
    private $user_id, $username, $avatar, $bio, $country, $followers, $likes_no, $images_no, $purchased_images_no, $bookmarker_count, $user_status;

    private $_appDataConfigs;


    public function __construct()
    {
        // init statements here ....
        $this->_appDataConfigs = include('../config/app-data-configs.php');
    }

    //coper emmanuel added (SELECT COUNT(ph.id) FROM puchase_history ph WHERE ph.user_id = a.id) AS purchased_images_no
    public function Profile_View($func_userid)
    {

        if (strlen($func_userid) > 10) {
            $func_userid = decrypt_data($func_userid);
        }

        $this->user_id = $func_userid;
        $sql = "SELECT a.username as username, a.bio as bio, a.avatar as avatar, d.name as country, 
            (select count(follower_id) from followers where follower_id = a.id) as no_followers,
            (select count(user_id) from likes where user_id = a.id) as likes_no,
            (select count(user_id) from images where user_id = a.id) as images_no,
            (SELECT COUNT(i.id) FROM puchase_history ph INNER JOIN images i ON ph.img_id = i.id WHERE ph.user_id = a.id) AS number_of_images_purchased,
            (SELECT COUNT(DISTINCT u.id)
             FROM users u
             JOIN collections_images ci ON u.id IN (
                 SELECT c.user_id
                 FROM collections c
                 WHERE c.id = ci.collections_id
             )
             JOIN images i ON i.id = ci.images_id
             WHERE i.user_id = a.id) AS bookmarker_count
            FROM users AS a 
            LEFT JOIN countries AS d ON a.countries_id = d.country_id
            WHERE a.id = '$this->user_id'";
        //$sql = "SELECT  username, cover, avatar FROM `users` WHERE id = '$this->user_id'";
        $result = mysqli_query($this->connect(), $sql);


        $profile = array();

        if ($result) {
            if ($result->num_rows > 0) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $this->username = $row["username"];
                    $this->avatar =  $row["avatar"];
                    $this->bio =  $row["bio"];
                    $this->country =  $row["country"];
                    $this->followers =  $row["no_followers"];
                    $this->likes_no =  $row["likes_no"];
                    $this->images_no =  $row["images_no"];
                    //corper emmanuel added this
                    $this->purchased_images_no = $row["number_of_images_purchased"];
                    $this->bookmarker_count = $row["bookmarker_count"];
                }
            } else {
                echo "No record found on display";
            }
        } else {
            echo "Database error";
        }

        if ($func_userid == $_SESSION["id"]) {
            $this->user_status = 1;
        } else if ($func_userid != $_SESSION["id"]) {
            $this->user_status = 0;
        }

        $profile2 = array(
            "username" => $this->username,
            "avatar" => $this->avatar,
            "bio" => $this->bio,
            "country" => $this->country,
            "followers" => $this->followers,
            "likes_no" => $this->likes_no,
            "images_no" => $this->images_no,
            "user_status" => $this->user_status,
            //coper emmanuel worked here 
            "purchased_images_no" => $this->purchased_images_no,
            "bookmarker_count" => $this->bookmarker_count

        );

        array_push($profile, $profile2);
        echo json_encode($profile);
    }

    public function fetch_bookmarks($func_userid)
    {

        if ($func_userid == 0) {
            if (isset($_SESSION["id"])) {
                $this->user_id = $_SESSION["id"];
            } else {
                $this->user_id = decrypt_data($func_userid);
            }
        }

        $sql = "SELECT a.title as collection_name,
        (select thumbnail from images where id = b.images_id) as bookmark_images
        FROM collections AS a 
        LEFT JOIN collections_images AS b ON a.id = b.collections_id 
        WHERE a.user_id = '$this->user_id'";

        $result = mysqli_query($this->connect(), $sql);

        //echo $result;
        $profiler = array();
        $response = array();


        if ($result) {
            if ($result->num_rows > 0) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $bookmark_images = "";
                    $collection_name = "";
                    $eachrow = array(
                        $collection_name =  $row["collection_name"],
                        $bookmark_images =  $row["bookmark_images"]
                    );
                    $collection_arr = array(
                        "collection" => $collection_name,
                        "image" => $bookmark_images
                    );
                    array_push($response, $collection_arr);
                }

                $profiler = array(
                    "status" => "1",
                    "message" => "bookmarked_images fetched successfully",
                    "results" => $response
                );
            } else {
                $profiler = array(
                    "status" => "0",
                    "message" => "no records"
                );
            }
            echo json_encode($profiler);
        } else {
            echo "Database error";
        }

        /* if($func_userid == $_SESSION["id"]){
                $this->user_status = 1;
            }
            else if($func_userid != $_SESSION["id"]){
                $this->user_status = 0;
            } */

        /*  $profiler_r = array(
                "collection_name" => $collection_name,
                "images" => $no_bookmark_images,
                "no_bookmark_images" => $bookmark_images, 
               
            );
    
            array_push($profiler, $profiler_r);*/
    }

    //coper emmanuel wrote this function
    public function fetchBookmarkers($func_userid, $offset)
    {

        if ($func_userid == 0) {
            if (isset($_SESSION["id"])) {
                $this->user_id = $_SESSION["id"];
            } else {
                $this->user_id = decrypt_data($func_userid);
            }
        }

        $sql = "SELECT DISTINCT u.id AS user_id, u.username AS user_name, u.avatar AS user_avatar, i.id AS image_id, i.thumbnail, c.created_at AS collection_date
        FROM users u
        JOIN collections_images ci ON u.id IN (
            SELECT c.user_id
            FROM collections c
            WHERE c.id = ci.collections_id
        )
        JOIN collections c ON c.id = ci.collections_id
        JOIN images i ON i.id = ci.images_id
        WHERE i.user_id = '$this->user_id'";


        $sql = $sql . " LIMIT " . $this->_appDataConfigs['lazy_paginations'];
        if ($offset > 0) $sql = $sql . " OFFSET $offset";

        $result = mysqli_query($this->connect(), $sql);

        // total data count
        $runCountSQL2 = mysqli_query($this->connect(), $sql);
        $countRow2 = mysqli_fetch_assoc($runCountSQL2);
        $count = mysqli_num_rows($runCountSQL2);


        $response = array();

        if ($result) {
            if ($result->num_rows > 0) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $eachImage = array(
                        "user_id" => $row['user_id'],
                        "username" => $row['user_name'],
                        "avatar" => $row['user_avatar'],
                        "image_id" => $row['image_id'],
                        "thumbnail" => $row['thumbnail'],
                        "created_at" => $row['collection_date'],
                    );
                    array_push($response, $eachImage);
                }

                // send response data
                $responseData = array(
                    "status" => "1",
                    "message" => "Bookmarkers fetched successfully",
                    "pagination_configurations" => [
                        "row_set" => $this->_appDataConfigs['lazy_paginations'],
                        "page_divisions" => ceil($count / $this->_appDataConfigs['lazy_paginations']),
                        "db_result_count" => $count
                    ],
                    "results" => $response
                );

                echo json_encode($responseData);
            } else {
                $response = array(
                    "status" => "0",
                    "message" => "no records"
                );
                echo json_encode($response);
            }
        } else {
            $response = array(
                "status" => "0",
                "message" => "server error"
            );
            echo json_encode($response);
        }
    }

    //coper emmanuel wrote this function
    public function fetchPurchasedImages($func_userid, $offset)
    {

        if ($func_userid == 0) {
            if (isset($_SESSION["id"])) {
                $this->user_id = $_SESSION["id"];
            } else {
                $this->user_id = decrypt_data($func_userid);
            }
        }

        $sql = "SELECT i.id AS image_id, i.preview AS preview_image, i.thumbnail AS thumbnail, i.categories_id, c.name AS category_name FROM puchase_history ph INNER JOIN images i ON ph.img_id = i.id LEFT JOIN categories c ON i.categories_id = c.id WHERE ph.user_id = '$this->user_id' ";


        $sql = $sql . " LIMIT " . $this->_appDataConfigs['lazy_paginations'];
        if ($offset > 0) $sql = $sql . " OFFSET $offset";

        $result = mysqli_query($this->connect(), $sql);

        // total data count
        $runCountSQL2 = mysqli_query($this->connect(), $sql);
        $countRow2 = mysqli_fetch_assoc($runCountSQL2);
        $count = mysqli_num_rows($runCountSQL2);


        $response = array();

        if ($result) {
            if ($result->num_rows > 0) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $eachImage = array(
                        "image_id" => $row['image_id'],
                        "image" => $row['preview_image'],
                        "thumbnail" => $row['thumbnail'],
                        "category_id" => $row['categories_id'],
                        "category" => $row['category_name']
                    );
                    array_push($response, $eachImage);
                }

                // send response data
                $responseData = array(
                    "status" => "1",
                    "message" => "Purchased images fetched successfully",
                    "pagination_configurations" => [
                        "row_set" => $this->_appDataConfigs['lazy_paginations'],
                        "page_divisions" => ceil($count / $this->_appDataConfigs['lazy_paginations']),
                        "db_result_count" => $count
                    ],
                    "results" => $response
                );

                echo json_encode($responseData);
            } else {
                $response = array(
                    "status" => "0",
                    "message" => "no records"
                );
                echo json_encode($response);
            }
        } else {
            $response = array(
                "status" => "0",
                "message" => "server error"
            );
            echo json_encode($response);
        }
    }


    public function fetchLikedImages($func_userid, $offset)
    {

        if ($func_userid == 0) {
            if (isset($_SESSION["id"])) {
                $this->user_id = $_SESSION["id"];
            } else {
                $this->user_id = decrypt_data($func_userid);
            }
        }

        $sql = "SELECT b.id as images_id, b.preview as preview_image, b.categories_id, c.name as category_name FROM likes a 
        INNER JOIN images b ON a.images_id = b.id 
        LEFT JOIN categories c ON b.categories_id = c.id
        WHERE a.user_id = '$this->user_id'";

        $sql = $sql . " LIMIT " . $this->_appDataConfigs['lazy_paginations'];
        if ($offset > 0) $sql = $sql . " OFFSET $offset";
        $result = mysqli_query($this->connect(), $sql);

        // total data count
        $runCountSQL2 = mysqli_query($this->connect(), $sql);
        $countRow2 = mysqli_fetch_assoc($runCountSQL2);
        $count = mysqli_num_rows($runCountSQL2);


        $response = array();

        if ($result) {
            if ($result->num_rows > 0) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $eachImage = array(
                        "image_id" => $row['images_id'],
                        "image" => $row['preview_image'],
                        "category_id" => $row['categories_id'],
                        "category" => $row['category_name']
                    );
                    array_push($response, $eachImage);
                }

                // send response data
                $responseData = array(
                    "status" => "1",
                    "message" => "Liked images fetched successfully",
                    "pagination_configurations" => [
                        "row_set" => $this->_appDataConfigs['lazy_paginations'],
                        "page_divisions" => ceil($count / $this->_appDataConfigs['lazy_paginations']),
                        "db_result_count" => $count
                    ],
                    "results" => $response
                );

                echo json_encode($responseData);
            } else {
                $response = array(
                    "status" => "0",
                    "message" => "no records"
                );
                echo json_encode($response);
            }
        } else {
            $response = array(
                "status" => "0",
                "message" => "server error"
            );
            echo json_encode($response);
        }
    }


    public function fetchOwnImages($func_userid, $offset)
    {

        if ($func_userid == 0) {
            if (isset($_SESSION["id"])) {
                $this->user_id = $_SESSION["id"];
            } else {
                $this->user_id = decrypt_data($func_userid);
            }
        }

        $sql = "SELECT a.id as images_id, a.preview as preview_image, a.categories_id, b.name as category_name FROM images a 
        INNER JOIN categories b ON a.categories_id = b.id
        WHERE a.user_id = '$this->user_id'";

        $sql = $sql . " LIMIT " . $this->_appDataConfigs['lazy_paginations'];
        if ($offset > 0) $sql = $sql . " OFFSET $offset";

        $result = mysqli_query($this->connect(), $sql);

        // total data count
        $runCountSQL2 = mysqli_query($this->connect(), $sql);
        $countRow2 = mysqli_fetch_assoc($runCountSQL2);
        $count = mysqli_num_rows($runCountSQL2);


        $response = array();

        if ($result) {
            if ($result->num_rows > 0) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $eachImage = array(
                        "image_id" => $row['images_id'],
                        "image" => $row['preview_image'],
                        "category_id" => $row['categories_id'],
                        "category" => $row['category_name']
                    );
                    array_push($response, $eachImage);
                }

                // send response data
                $responseData = array(
                    "status" => "1",
                    "message" => "Own images fetched successfully",
                    "pagination_configurations" => [
                        "row_set" => $this->_appDataConfigs['lazy_paginations'],
                        "page_divisions" => ceil($count / $this->_appDataConfigs['lazy_paginations']),
                        "db_result_count" => $count
                    ],
                    "results" => $response
                );

                echo json_encode($responseData);
            } else {
                $response = array(
                    "status" => "0",
                    "message" => "no records"
                );
                echo json_encode($response);
            }
        } else {
            $response = array(
                "status" => "0",
                "message" => "server error"
            );
            echo json_encode($response);
        }
    }

    //    public function Profile_Picture($func_userid, $profile_picture)
    //     {
    //         echo php_ini_loaded_file(); 

    //             $this->user_id = $func_userid;
    //             $sql1 = "SELECT username from users where id = '$this->user_id'";
    //             $result1 = mysqli_query($this->connect(), $sql1);

    //             if($result1){
    //                 if($result1-> num_rows >0){
    //                 while($row1 = mysqli_fetch_assoc($result1)) {
    //                     $this->username = $row1["username"];
    //                 }

    //                 }
    //                 else{
    //                     echo "No record found on display";
    //                 }
    //             }

    //             else{
    //                 echo "Database error";
    //             }


    //             $rand = substr(md5(microtime()),rand(0,26),5);
    //             $avatar_name = $this->username.$rand.".jpg";

    //             $sql = "UPDATE users SET avatar = '$avatar_name' where id = '$this->user_id'";
    //             $result = mysqli_query($this->connect(), $sql);

    //             //echo "<pre>"; print_r($_FILES);echo "</pre>";
    //             $filepath = "../../peco_image_store/avatar/".$avatar_name; 
    //             //$filetmp = $_FILES['image_src']['tmp_name'];
    //            /*  echo move_uploaded_file($_POST["image_src"], $filepath);

    //             $file = 'people.txt';
    //             // Open the file to get existing content
    //             $current = file_get_contents($file);
    //             // Append a new person to the file
    //             $current .= "John Smith\n";
    //             // Write the contents back to the file
    //             echo file_put_contents($profile_picture, $filepath);


    //             if($result){
    //                 //if($result-> num_rows >0){

    //                     $responseBody = array(
    //                         "message" => "updated",
    //                         "status" => 1,
    //                         "avatar for ".$this->username => $avatar_name
    //                       );


    //             }


    //             else
    //             {
    //                 echo "Database error";
    //             }
    //      */
    //             echo $profile_picture;
    //             if (!is_dir(dirname($filepath))) {
    //                 mkdir(dirname($filepath), 0755, true);
    //             }
    //      if (isset($profile_picture)) {

    //         if (move_uploaded_file($profile_picture, $filepath)) { 
    //             /* $file = 'people.txt';
    //             $current = file_get_contents($file);
    //             $current .= "John Smith\n";
    //             file_put_contents($file, $current);
    //  */
    //             if ($result) {
    //                 $responseBody = array(
    //                     "message" => "updated",
    //                     "status" => 1,
    //                     "avatar for " . $this->username => $avatar_name
    //                 );
    //             } else {
    //                 echo "Database error";
    //                 return;
    //             }
    //          } else {
    //             echo "Failed to move uploaded file.";
    //             return;
    //         } 
    //     } else {
    //         echo "File upload error.";
    //         return;
    //     }

    //            echo json_encode($responseBody);

    //     }


    public function Profile_Picture($func_userid, $profile_picture)
    {
        $this->user_id = $func_userid;
        $sql1 = "SELECT username from users where id = '$this->user_id'";
        $result1 = mysqli_query($this->connect(), $sql1);

        if ($result1) {
            if ($result1->num_rows > 0) {
                while ($row1 = mysqli_fetch_assoc($result1)) {
                    $this->username = $row1["username"];
                }
            } else {
                echo "No record found on display";
                return;
            }
        } else {
            echo "Database error";
            return;
        }

        $rand = substr(md5(microtime()), rand(0, 26), 5);
        $avatar_name = $this->username . $rand . ".jpg";

        $sql = "UPDATE users SET avatar = '$avatar_name' where id = '$this->user_id'";
        $result = mysqli_query($this->connect(), $sql);

        // Create directory if it doesn't exist
        $upload_dir = "../../assets/images/";
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }

        // Handle base64 image data
        if (strpos($profile_picture, 'data:image') !== false) {
            $data = explode(',', $profile_picture);
            $image_data = base64_decode($data[1]);
            $filepath = $upload_dir . $avatar_name;

            if (file_put_contents($filepath, $image_data)) {
                if ($result) {
                    $responseBody = array(
                        "message" => "updated",
                        "status" => 1,
                        "avatar" => $avatar_name
                    );
                    echo json_encode($responseBody);
                    return;
                } else {
                    echo json_encode(array("message" => "Database error", "status" => 0));
                    return;
                }
            } else {
                echo json_encode(array("message" => "Failed to save image", "status" => 0));
                return;
            }
        } else {
            echo json_encode(array("message" => "Invalid image data", "status" => 0));
            return;
        }
    }
}
