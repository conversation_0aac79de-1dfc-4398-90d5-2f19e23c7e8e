<?php
// session_start();
// include 'resources/db_config/db_connection.php';

// echo "<h2>Manual Purchase Insert Test</h2>";

// if (!isset($_SESSION['id'])) {
//     echo "<p>❌ Please login first</p>";
//     exit;
// }

// $user_id = $_SESSION['id'];

// if (isset($_POST['insert_test'])) {
//     echo "<h3>Attempting Manual Insert</h3>";

//     $test_data = [
//         'user_id' => $user_id,
//         'img_id' => 999,
//         'amount' => 100,
//         'payment_ref' => 'TEST_' . time(),
//         'Date_created' => date('Y-m-d H:i:s'),
//         'payment_status' => 1
//     ];

//     $sql = "INSERT INTO puchase_history (user_id, img_id, amount, payment_ref, Date_created, payment_status) 
//             VALUES (?, ?, ?, ?, ?, ?)";

//     echo "<p>SQL: $sql</p>";
//     echo "<p>Data: " . json_encode($test_data) . "</p>";

//     $stmt = $db->prepare($sql);
//     if ($stmt) {
//         $stmt->bind_param("iidssi", 
//             $test_data['user_id'], 
//             $test_data['img_id'], 
//             $test_data['amount'], 
//             $test_data['payment_ref'], 
//             $test_data['Date_created'], 
//             $test_data['payment_status']
//         );

//         if ($stmt->execute()) {
//             echo "<p>✅ Manual insert successful! Insert ID: " . $db->insert_id . "</p>";

//             // Verify the insert
//             $verify_sql = "SELECT * FROM puchase_history WHERE user_id = ? ORDER BY Date_created DESC LIMIT 1";
//             $verify_stmt = $db->prepare($verify_sql);
//             $verify_stmt->bind_param("i", $user_id);
//             $verify_stmt->execute();
//             $result = $verify_stmt->get_result();

//             if ($result->num_rows > 0) {
//                 echo "<p>✅ Verification successful. Latest record:</p>";
//                 $row = $result->fetch_assoc();
//                 echo "<table border='1' style='border-collapse: collapse;'>";
//                 foreach ($row as $key => $value) {
//                     echo "<tr><td><strong>$key</strong></td><td>" . htmlspecialchars($value) . "</td></tr>";
//                 }
//                 echo "</table>";
//             }
//         } else {
//             echo "<p>❌ Manual insert failed: " . $stmt->error . "</p>";
//         }
//         $stmt->close();
//     } else {
//         echo "<p>❌ Prepare failed: " . $db->error . "</p>";
//     }
// }

// // Show table structure
// echo "<h3>Table Structure</h3>";
// $structure = $db->query("DESCRIBE puchase_history");
// if ($structure) {
//     echo "<table border='1' style='border-collapse: collapse;'>";
//     echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
//     while ($row = $structure->fetch_assoc()) {
//         echo "<tr>";
//         echo "<td>" . $row['Field'] . "</td>";
//         echo "<td>" . $row['Type'] . "</td>";
//         echo "<td>" . $row['Null'] . "</td>";
//         echo "<td>" . $row['Key'] . "</td>";
//         echo "<td>" . $row['Default'] . "</td>";
//         echo "<td>" . $row['Extra'] . "</td>";
//         echo "</tr>";
//     }
//     echo "</table>";
// } else {
//     echo "<p>❌ Could not get table structure: " . $db->error . "</p>";
// }

// // Show current records
// echo "<h3>Current Records for User $user_id</h3>";
// $current = $db->query("SELECT * FROM puchase_history WHERE user_id = $user_id ORDER BY Date_created DESC");
// if ($current && $current->num_rows > 0) {
//     echo "<table border='1' style='border-collapse: collapse;'>";

//     // Headers
//     $fields = $current->fetch_fields();
//     echo "<tr>";
//     foreach ($fields as $field) {
//         echo "<th>" . $field->name . "</th>";
//     }
//     echo "</tr>";

//     // Data
//     $current->data_seek(0);
//     while ($row = $current->fetch_assoc()) {
//         echo "<tr>";
//         foreach ($row as $value) {
//             echo "<td>" . htmlspecialchars($value) . "</td>";
//         }
//         echo "</tr>";
//     }
//     echo "</table>";
// } else {
//     echo "<p>No records found for user $user_id</p>";
// }

?>

<!-- <form method="post">
    <button type="submit" name="insert_test" style="padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer;">
        Test Manual Insert
    </button>
</form>

<hr>
<p><a href="check_purchases.php">Check Purchases</a> | <a href="account-settings.php">Account Settings</a></p> -->


<?php
session_start();
include 'resources/db_config/db_connection.php';

echo "<h2>Manual Purchase Insert Test</h2>";

if (!isset($_SESSION['id'])) {
    echo "<p>❌ Please login first</p>";
    exit;
}

$user_id = $_SESSION['id'];

if (isset($_POST['insert_test'])) {
    echo "<h3>Attempting Manual Insert</h3>";

    $test_data = [
        'user_id' => $user_id,
        'img_id' => 999,
        'amount' => 100,
        'payment_ref' => 'TEST_' . time(),
        'Date_created' => date('Y-m-d H:i:s'),
        'payment_status' => 1
    ];

    $sql = "INSERT INTO puchase_history (user_id, img_id, amount, payment_ref, Date_created, payment_status) 
            VALUES (?, ?, ?, ?, ?, ?)";

    echo "<p>SQL: $sql</p>";
    echo "<p>Data: " . json_encode($test_data) . "</p>";

    $stmt = $db->prepare($sql);
    if ($stmt) {
        $stmt->bind_param(
            "iidssi",
            $test_data['user_id'],
            $test_data['img_id'],
            $test_data['amount'],
            $test_data['payment_ref'],
            $test_data['Date_created'],
            $test_data['payment_status']
        );

        if ($stmt->execute()) {
            echo "<p>✅ Manual insert successful! Insert ID: " . $db->insert_id . "</p>";

            // Verify the insert
            $verify_sql = "SELECT * FROM puchase_history WHERE user_id = ? ORDER BY Date_created DESC LIMIT 1";
            $verify_stmt = $db->prepare($verify_sql);
            $verify_stmt->bind_param("i", $user_id);
            $verify_stmt->execute();
            $result = $verify_stmt->get_result();

            if ($result->num_rows > 0) {
                echo "<p>✅ Verification successful. Latest record:</p>";
                $row = $result->fetch_assoc();
                echo "<table border='1' style='border-collapse: collapse;'>";
                foreach ($row as $key => $value) {
                    echo "<tr><td><strong>$key</strong></td><td>" . htmlspecialchars($value) . "</td></tr>";
                }
                echo "</table>";
            }
        } else {
            echo "<p>❌ Manual insert failed: " . $stmt->error . "</p>";
        }
        $stmt->close();
    } else {
        echo "<p>❌ Prepare failed: " . $db->error . "</p>";
    }
}

// Show table structure
echo "<h3>Table Structure</h3>";
$structure = $db->query("DESCRIBE puchase_history");
if ($structure) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $structure->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>❌ Could not get table structure: " . $db->error . "</p>";
}

// Show current records
echo "<h3>Current Records for User $user_id</h3>";
$current = $db->query("SELECT * FROM puchase_history WHERE user_id = $user_id ORDER BY Date_created DESC");
if ($current && $current->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";

    // Headers
    $fields = $current->fetch_fields();
    echo "<tr>";
    foreach ($fields as $field) {
        echo "<th>" . $field->name . "</th>";
    }
    echo "</tr>";

    // Data
    $current->data_seek(0);
    while ($row = $current->fetch_assoc()) {
        echo "<tr>";
        foreach ($row as $value) {
            echo "<td>" . htmlspecialchars($value) . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No records found for user $user_id</p>";
}

?>

<form method="post">
    <button type="submit" name="insert_test" style="padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer;">
        Test Manual Insert
    </button>
</form>

<hr>
<p><a href="check_purchases.php">Check Purchases</a> | <a href="account-settings.php">Account Settings</a></p>