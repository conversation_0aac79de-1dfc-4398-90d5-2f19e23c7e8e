<!-- Header section-->
    <?php require_once(__DIR__ . '/resources/header.php'); ?>

    <link rel="stylesheet" href="assets/css/collections.css">
</head>

<body>

     <!-- Loader section-->
    <?php require_once(__DIR__ . '/resources/loader.php'); ?>

    <header id="#top">
		<!-- navbar section-->
      <?php require_once(__DIR__.'/resources/navbar.php'); ?>

    </header>

   <div class="page-banner change-name">
        <div class="container">
            <div class="row">
                <div class="col-lg-6 offset-lg-3">
                    <div class="header-text">
                        <h2>Collect<em>ions</em> (5)</h2>
                        <!-- <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod keoi tempor incididunt ut labore et dolore magna aliqua.</p> -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="error-message" class="alert"></div>
    <!------------------------MEMBER SECTION---------------------->
    <div class="collections-wrapper">
        <!-- Private Collections -->
        <div class="container mb-5">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3 class="section-title mb-0">My Private Collections</h3>
                <button id="createCollectionBtn" class="btn btn-primary" style="background-color: #ff565b; border-color: #ff565b;">
                    <i class="fas fa-plus"></i> Create Collection
                </button>
            </div>
            <div class="row" id="private-collections"></div>
        </div>

        <!-- Public Collections -->
        <div class="container">
            <h3 class="section-title">Public Collections</h3>
            <div class="row" id="public-collections"></div>
        </div>
    </div>
      

      <!-- Loader section-->
		  <?php require_once(__DIR__.'/resources/footer.php'); ?>
  

    <!-- Scripts -->
	<!-- Scripts section -->
  <?php require_once(__DIR__ . '/resources/scripts.php'); ?>
  <script src="scripts/view-scripts/collections.js"></script>
  



	<!-- Utility scripts section -->
    <?php require_once(__DIR__ . '/resources/utility_scripts.php'); ?>


</body>

</html>

<!-- Create Collection Modal -->
<div class="modal fade" id="createCollectionModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Collection</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="createCollectionForm">
                    <div class="mb-3">
                        <label for="collectionTitle" class="form-label">Collection Title</label>
                        <input type="text" class="form-control" id="collectionTitle" required>
                    </div>
                    <div class="mb-3">
                        <label for="collectionType" class="form-label">Collection Type</label>
                        <select class="form-select" id="collectionType" required>
                            <option value="public">Public</option>
                            <option value="private">Private</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveCollection" style="background-color: #ff565b; border-color: #ff565b;">Create Collection</button>
            </div>
        </div>
    </div>
</div>