<div class="plans noshow">
    <h1>Your Plans</h1>

    <!-- Current Plan Summary -->
    <div class="row mb-4" id="current-plan-summary" style="display: none;">
        <div class="col-12">
            <div class="alert alert-success" role="alert">
                <h4 class="alert-heading">
                    <i class="fas fa-check-circle"></i> Current Active Plan
                </h4>
                <p class="mb-1">
                    <strong id="current-plan-name">-</strong> -
                    <span id="current-plan-amount">₦0</span>
                </p>
                <hr>
                <p class="mb-0">
                    <small>
                        Expires on: <strong id="current-plan-expiry">-</strong>
                    </small>
                </p>
            </div>
        </div>
    </div>

    <!-- No Active Plan Message -->
    <div class="row mb-4" id="no-active-plan-message" style="display: none;">
        <div class="col-12">
            <div class="alert alert-warning" role="alert">
                <h4 class="alert-heading">
                    <i class="fas fa-exclamation-triangle"></i> No Active Plan
                </h4>
                <p class="mb-0">
                    You don't have any active subscription plans.
                    <a href="plan.php" class="alert-link">Browse available plans</a> to get started.
                </p>
            </div>
        </div>
    </div>

    <div class="row fi-lter">
        <!--Grid column-->

        <div class="col-md-3 mb-4">

            <div class="md-form peco-table-filter">

                <input placeholder="Search plans..." type="text" id="plans-search-filter" class="form-control datepicker">
                <button type="button" id="plans-filter-btn" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                </button>
            </div>

        </div>

        <div class="col-md-3 mb-4">

            <div class="md-form">
                <label for="statusFilter">Status:</label>
                <select id="plans-status-filter" class="form-control">
                    <option value="">All Plans</option>
                    <option value="Active">Active Only</option>
                    <option value="Expired">Expired Only</option>
                </select>
            </div>

        </div>

        <div class="col-md-3 mb-4">

            <div class="md-form">
                <!--The "from" Date Picker -->
                <label for="startingDate">Start date:</label>
                <input placeholder="Select start date" type="date" id="plans-starting-date-filter" class="form-control datepicker">
            </div>

        </div>
        <!--Grid column-->

        <!--Grid column-->
        <div class="col-md-3 mb-4">

            <div class="md-form">
                <!--The "to" Date Picker -->
                <label for="endingDate">End date:</label>
                <input placeholder="Select end date" type="date" id="plans-ending-date-filter" class="form-control datepicker">

            </div>

        </div>
        <!--Grid column-->


    </div>


    <div class="table-responsive peco-table scrollable-element" id="user-plans-table">
        <table class="styled-table table table-sm">
            <thead>
                <tr>
                    <th>Plan</th>
                    <th>Charge(Naira)</th>
                    <th>Expiry Date</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody id="user-plans-view">
            </tbody>
        </table>
    </div>



    <div class="text-center" style="display: none;" id="plans-no-results-msg">
        <span style="font-size: 16px; margin-bottom: 60px;">No results</span>
        <br /><br />
        <div class="white-button" id="plans-clear-search">
            <a href="#">clear search</a>
        </div>
    </div>

    <span id="user-plans-loader" class="text-center" style="display: none; font-size: 16px; margin-bottom: 30px;">Loading data, please wait...</span>


    <nav aria-label="Page navigation example" class="table-pagination">
        <ul class="pagination" id="plans-table-pagination">
        </ul>
    </nav>

</div>