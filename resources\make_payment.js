// function make_payment(amount, email, save, payment){
//     //save status = 1 ? save to DB, if 0 dont save

//     var reference = Date.now().toString(36) + Math.random().toString(36).substr(2);
//     console.log(reference);

//     var handler = PaystackPop.setup({
//         key: 'pk_test_d76270d9165f89173a1626528afbe3d151fd10c7', // Replace with your public key
//         email: email,
//         amount: amount * 100, // the amount value is multiplied by 100 to convert to the lowest currency unit
//         currency: 'NGN', // Use GHS for Ghana Cedis or USD for US Dollars
//         ref: reference, // Replace with a reference you generated
//         channels : ['card'],
//         callback: function(response) {
//         //this happens after the payment is completed successfully
//         var reference = response.reference;
//         // alert('Payment complete! Reference: ' + reference);
//         // console.log(`./resources/verify_payment.php?reference=${reference}&save=${save}&email=${email}&payment=${payment}`);
//         // $.get({
//         //     url: `verify_payment.php?reference=${reference}&save=${save}&email=${email}&payment=${payment}`,
//         //     success: function(data){
//         //         console.log(data);
//         //     }
//         // })
//         $.get(`resources/verify_payment.php?reference=${reference}&save=${save}&email=${email}&payment=${payment}`, function(data) {
//             console.log(data);

//             // refresh target page
//             if(window.location.href.includes('/account-settings.php') || window.location.href.includes('/cart.php')) {
//                 window.location.reload();
//             }
//         });
//         // Make an AJAX call to your server with the reference to verify the transaction

//         },
//         onClose: function() {
//         alert('Transaction was not completed, window closed.');
//         },
//     });
//     handler.openIframe();

// }

// function plan_payment(amount, plan_id, user_id, email, monthCount, button) {
//     var reference = Date.now().toString(36) + Math.random().toString(36).substr(2);

//     var handler = PaystackPop.setup({
//         key: 'pk_test_d76270d9165f89173a1626528afbe3d151fd10c7', // Replace with your public key
//         email: email,
//         amount: amount * 100, // Convert to lowest currency unit
//         currency: 'NGN',
//         ref: reference,
//         channels: ['card'],
//         callback: function(response) {
//             var reference = response.reference;

//             $("#paymentReference").text(reference);
//             $("#paymentSuccessModal").modal("show");

//             // Disable only the clicked button
//             $(button).prop("disabled", true).text("Subscribed").addClass("disabled-btn");

//             // Verify payment
//             $.get(`resources/verify_plan_payment.php?reference=${reference}&plan_id=${plan_id}&email=${email}&user_id=${user_id}&month_count=${monthCount}`, function(data) {
//                 console.log(data);
//                 // if(data === "successful") {
//                 //     $("#subscription_plans").css("display", "none");
//                 // }
//             });
//         },
//         onClose: function() {
//             alert('Transaction was not completed, window closed.');
//         }
//     });
//     handler.openIframe();
// }

// function plan_payment2(amount, plan_id, user_id, email, monthCount) {
//     var reference = Date.now().toString(36) + Math.random().toString(36).substr(2);

//     var handler = PaystackPop.setup({
//         key: 'pk_test_d76270d9165f89173a1626528afbe3d151fd10c7', // Replace with your public key
//         email: email,
//         amount: amount * 100, // Convert to lowest currency unit
//         currency: 'NGN',
//         ref: reference,
//         channels: ['card'],
//         callback: function(response) {
//             var reference = response.reference;

//             $("#paymentReference").text(reference);
//             $("#paymentSuccessModal").modal("show");

//             // Verify payment
//             $.get(`resources/update_plan_payment.php?reference=${reference}&plan_id=${plan_id}&email=${email}&user_id=${user_id}&month_count=${monthCount}`, function(data) {
//                 console.log(data);

//                 // if(data === "successful") {
//                 //     $("#subscription_plans").css("display", "none");
//                 // }

//             });

//             //refresh page after 2 sec
//             setTimeout(function() {
//                 window.location.reload();
//             }, 2000);
//         },
//         onClose: function() {
//             alert('Transaction was not completed, window closed.');
//         }
//     });
//     handler.openIframe();
// }

function plan_payment(amount, plan_id, user_id, email, monthCount, button) {
  var reference =
    Date.now().toString(36) + Math.random().toString(36).substr(2);

  var handler = PaystackPop.setup({
    key: "pk_test_d76270d9165f89173a1626528afbe3d151fd10c7", // Replace with your public key
    email: email,
    amount: amount * 100, // Convert to lowest currency unit
    currency: "NGN",
    ref: reference,
    channels: ["card"],
    callback: function (response) {
      var reference = response.reference;

      $("#paymentReference").text(reference);
      $("#paymentSuccessModal").modal("show");

      // Determine which button was clicked (if any)
      if (button) {
        // Disable only the clicked button
        $(button)
          .prop("disabled", true)
          .text("Subscribed")
          .addClass("disabled-btn");
      } else {
        // If no specific button, disable all buttons for this plan
        $(`button[data-plan-id="${plan_id}"]`)
          .prop("disabled", true)
          .text("Subscribed")
          .addClass("disabled-btn");
      }

      // Verify payment
      $.get(
        `resources/verify_plan_payment.php?reference=${reference}&plan_id=${plan_id}&email=${email}&user_id=${user_id}&month_count=${monthCount}`,
        function (data) {
          console.log(data);
          if (data === "successful") {
            // Reload the page after a short delay to show updated plan status
            setTimeout(function () {
              window.location.reload();
            }, 3000);
          }
        }
      );
    },
    onClose: function () {
      alert("Transaction was not completed, window closed.");
    },
  });
  handler.openIframe();
}

function plan_payment2(amount, plan_id, user_id, email, monthCount) {
  var reference =
    Date.now().toString(36) + Math.random().toString(36).substr(2);

  var handler = PaystackPop.setup({
    key: "pk_test_d76270d9165f89173a1626528afbe3d151fd10c7", // Replace with your public key
    email: email,
    amount: amount * 100, // Convert to lowest currency unit
    currency: "NGN",
    ref: reference,
    channels: ["card"],
    callback: function (response) {
      var reference = response.reference;

      $("#paymentReference").text(reference);
      $("#paymentSuccessModal").modal("show");

      // Verify payment
      $.get(
        `resources/update_plan_payment.php?reference=${reference}&plan_id=${plan_id}&email=${email}&user_id=${user_id}&month_count=${monthCount}`,
        function (data) {
          console.log(data);
          if (data === "successful") {
            // Reload the page after a short delay to show updated plan status
            setTimeout(function () {
              window.location.reload();
            }, 3000);
          }
        }
      );
    },
    onClose: function () {
      alert("Transaction was not completed, window closed.");
    },
  });
  handler.openIframe();
}
