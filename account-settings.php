<?php require_once(__DIR__ . '/resources/header.php'); ?>

<link rel="stylesheet" href="assets/css/account-settings.css">


</head>

<body>

    <?php require_once(__DIR__ . '/resources/loader.php'); ?>


    <?php require_once(__DIR__ . '/resources/navbar.php'); ?>



    <div class="page-banner change-name">
        <div class="container">
            <div class="row">
                <div class="col-lg-6 offset-lg-3">
                    <div class="header-text">
                        <h2><em>Account</em> Settings</h2>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="container acct-setting-drpdwn">
        <div class="row">
            <div class="setting-dropdown">
                <button onclick="myFunction()" class="setting-dropbtn">Account Options</button>
                <div class="set_Dropdown">
                    <div id="setting-myDropdown" class="setting-dropdown-content acct-setting">
                        <a id="profile" class="active">User Profile</a>
                        <a id="payment">Payment</a>
                        <a id="plans">Plans</a>
                        <a id="purchaseHistory">Purchase History</a>
                        <a id="settings">Settings</a>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <div class="userProfile">
        <div class="row">
            <div class="column">
                <div class="leftbox ">
                    <figure class="acct-setting">
                        <a id="profile" class="active">User Profile</a>
                        <a id="payment">Payment</a>
                        <a id="plans">Plans</a>
                        <a id="purchaseHistory">Purchase History</a>
                        <a id="settings">Settings</a>
                    </figure>
                </div>
            </div>

            <div class="double-column">
                <div class="rightbox ">

                    <div class="profile">
                        <?php require(__DIR__ . '/resources/personal-info.php'); ?>
                    </div>

                    <div class="payment noshow">
                        <?php require(__DIR__ . '/resources/payment-information.php'); ?>
                    </div>

                    <div class="plans noshow">
                        <?php require(__DIR__ . '/resources/profile-plans.php'); ?>
                    </div>

                    <div class="purchaseHistory noshow">
                        <?php require(__DIR__ . '/resources/purchase-history.php'); ?>
                    </div>

                    <div class="settings noshow">
                        <?php require(__DIR__ . '/resources/user-settings.php'); ?>
                    </div>

                </div>

            </div>
        </div>
    </div>


    <?php require_once(__DIR__ . '/resources/footer.php'); ?>

    <script src="vendor/jquery/jquery.min.js"></script>
    <script src="vendor/bootstrap/js/popper.min.js"></script>
    <script src="new-js/account-settings.js"></script>
    <script src="scripts/view-scripts/account-settings.js"></script>

    <script src="vendor/swiper/swiper-bundle.min.js"></script>
    <script src="vendor/glightbox/js/glightbox.min.js"></script>
    <script src="vendor/aos/aos.js"></script>
    <script src="assets/js/isotope.min.js"></script>
    <script src="assets/js/owl-carousel.js"></script>
    <script src="assets/js/mo.js"></script>
    <script src="assets/js/typed.min.js"></script>

    <script src="assets/js/tabs.js"></script>
    <script src="assets/js/custom.js"></script>

    <script src="https://js.paystack.co/v1/inline.js"></script>

    <script src="resources/make_payment.js"></script>

    <script src="scripts/view-scripts/purchase-history-view.js"></script>
    <script src="scripts/view-scripts/payment-information-view.js"></script>
    <script src="scripts/view-scripts/user-plans-view.js"></script>
    <?php require_once(__DIR__ . '/resources/utility_scripts.php'); ?>
    <script src="scripts/view-scripts/profile-view.js"></script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        $(document).ready(function() {
            set_interval();
        });
        $('body').click(function() {
            reset_interval();
        });
        $('body').mousemove(function() {
            reset_interval();
        });
        $('body').keypress(function() {
            reset_interval();
        });

        // setTimeout(function() {
        //     $('.loader').fadeToggle();
        // }, 1500);

        $("a[href='#top']").click(function() {
            $("html, body").animate({
                scrollTop: 0
            }, "slow");
            return false;
        });

        let logout_link = document.getElementsByClassName("logged-out");
        let login_link = document.getElementsByClassName("logged-in");

        $.post("scripts/data-scripts/auth-status.data.php", {
            request_type: 'auth-session-check'
        }, function(data) {
            if (data["message"] == "success") {
                // authed
                $(logout_link).addClass("hide");
                $(login_link).removeClass("hide");

                $("#authenticated-username").html(data["username"]);
            } else {
                // not authed
                $(logout_link).removeClass("hide");
                $(login_link).addClass("hide");
            }
        });

        $.ajax({
            type: 'get',
            url: 'scripts/data-scripts/user_profile.data.php',
            data: {
                user_id: 0
            },
            dataType: 'json',
            success: function(result) {
                if (result[0].avatar == "") {
                    return;
                }
                $("#auth-imgg").attr("src", `assets/images/${result[0].avatar}`);
                $("#auth-img").attr("src", `assets/images/${result[0].avatar} `);
            }
        });



        $("#signout-btn").click(function() {
            logout_call();
        });



        function set_interval() {
            timer = setInterval(() => {
                auto_logout();
            }, 3600000);
        }

        function reset_interval() {
            clearInterval(timer);
            timer = setInterval(() => {
                auto_logout();
            }, 3600000);
        }

        function auto_logout() {
            logout_call();
        }

        function logout_call() {
            console.log("auto logout called");
            $.post("scripts/data-scripts/auth-status.data.php", {
                request_type: 'auth-session-end'
            }, function(data) {
                if (data["message"] == "success") {
                    // signed out
                    window.location.href = "signup.php?login";
                }
            });
        }
    </script>
    <script>
        setTimeout(function() {
            $('.loader').fadeToggle();
        }, 2000);

        $("a[href='#top']").click(function() {
            $("html, body").animate({
                scrollTop: 0
            }, "slow");
            return false;
        });
    </script>
</body>

</html>