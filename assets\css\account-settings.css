@import url("https://fonts.googleapis.com/css?family=Nunito:400,900|Montserrat|Roboto");
/* body {
  background: linear-gradient(to right, #3FB6A8, #7ED386);
} */
.userProfile {
  background: #ffffff;
  width: 78%;
  height: auto;
  margin: 10px auto !important;
  position: relative;
  margin-top: 1% !important;
  box-shadow: 2px 5px 20px rgba(119, 119, 119, 0.5);
}
.backUserPr {
  background: #ffffff;
  width: 1000px;
  height: 900px;
  margin: 0 auto;
  position: relative;
  box-shadow: 2px 5px 20px rgba(119, 119, 119, 0.5);
}
.logo {
  float: right;
  margin-right: 12px;
  margin-top: 12px;
  font-family: "Nunito Sans", sans-serif;
  color: #ff565b;
  font-weight: 900;
  font-size: 1.5em;
  letter-spacing: 1px;
}
.acct-setting-drpdwn {
  display: none;
}

.editCard {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 30px;
}

.first-card-info {
  width: 70%;
}

.first-card-info h1 {
  margin-top: 0 !important;
  font-size: 1.5em;
}

.editCard h3 {
  font-size: 1.2em;
}

.peco-table-filter {
  display: flex;
  margin-top: 20px;
}

.peco-table-filter button {
  width: 30% !important;
}

.choose-default-card {
  display: inline;
}
/* .editCard h3{
  font-size: large;
  margin-top: 45px;
} */
.activated-default-card {
  height: 20%;
  width: 70% !important;
}

.show-default-card {
  background-color: #0d6efd !important;
}

@media screen and (max-width: 780px) {
  .column,
  .leftbox {
    display: none !important;
  }
  .column {
    flex: 0;
  }
  .double-column {
    /*   margin-left: -140px;  */
    justify-content: center;
    float: none;
    margin-left: 1.3em;
  }
  .rightbox {
    justify-content: center;
    float: none;
  }
  .acct-setting-drpdwn {
    display: block;
  }

  .peco-table {
    overflow-x: scroll !important;
  }
  .passcodeModal {
    height: 50%;
    width: 370px !important;
  }
  .card-details {
    flex-basis: content;
  }
  .passcodeModal p {
    margin-left: 12px;
  }
}

@media screen and (max-width: 735px) {
  .editCard h3 {
    margin-top: 0 !important;
  }
}
/* *, ::after, ::before {
  padding-right: inherit;
} */
.CTA {
  width: 80px;
  height: 40px;
  right: -20px;
  bottom: 0;
  margin-bottom: 90px;
  position: absolute;
  z-index: 1;
  background: #ff565b;
  font-size: 1em;
  transform: rotate(-90deg);
  transition: all 0.5s ease-in-out;
  cursor: pointer;
}
.CTA h1 {
  color: #ffffff;
  margin-top: 10px;
  margin-left: 9px;
}
.CTA:hover {
  background: #ff565b;
  transform: scale(1.1);
}

.leftbox {
  float: left;
  top: 15%;
  left: 25%;
  /* position: absolute; */
  width: auto;
  height: 110%;
  background: #ff565b;
  box-shadow: 3px 3px 10px rgba(139, 33, 26, 0.5);
}

.upbox {
  float: top;
  bottom: 15%;
  /* position: absolute; */
  width: auto;
  height: 60%;
  background: #ff565b;
  box-shadow: 3px 3px 10px rgba(139, 33, 26, 0.5);
}

.userProfile .row {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
}

.column {
  display: flex;
  flex-direction: column;
  flex-basis: 100%;
  flex: 1;
  flex-grow: inherit;
}

.double-column {
  display: flex;
  flex-direction: column;
  flex-basis: 100%;
  flex: 2;
}

figure {
  position: relative;
}

figure a {
  list-style: none;
  padding: 25px;
  color: #ffffff !important;
  font-size: 1.5em;
  display: block;
  transition: all 0.3s ease-in-out;
  font-family: "Times New Roman";
}
figure a:hover {
  color: #6d1e77 !important;
  transform: scale(0.8);
  cursor: pointer;
}
figure a:first-child {
  margin-top: 60px;
}

.acct-setting .active {
  color: #000 !important;
  font-weight: 500;
  font-size: x-large;
  border: #000;
  border-radius: 6%;
}

.rightbox {
  float: right;
  width: auto;
  height: 100%;
}

.profile,
.payment,
.plans,
.purchaseHistory,
.settings {
  transition: opacity 0.5s ease-in;
  position: relative;
  margin-bottom: 30px;
}

h1 {
  font-family: "Montserrat", sans-serif;
  color: #ff565b;
  font-size: 1.8em;
  margin-top: 40px;
  margin-bottom: 35px;
  font-weight: bold;
}

h2 {
  color: #777777;
  font-family: "Roboto", sans-serif;
  width: 100%;
  text-transform: uppercase;
  font-size: 15px;
  letter-spacing: 1px;
  margin-left: 2px;
}

.rightbox p {
  border-width: 1px;
  border-style: solid;
  border-image: linear-gradient(to right, #ff565b, rgba(219, 52, 30, 0.5)) 1 0%;
  border-top: 0;
  /* width: 80%; */
  font-family: "Montserrat", sans-serif;
  /* font-size: 0.7em; */
  padding: 7px 0;
  /* color: #070707; */
}
@media screen and (max-width: 325px) {
  .rightbox p {
    font-size: 0.7em;
  }
  .passcodeModal {
    height: 75% !important;
    width: 200px !important;
  }

  .profile span {
    font-size: 2.5em !important;
  }
  #user_changes {
    font-size: 2.5em;
  }
  #change_text {
    font-size: 0.5em;
  }
}

.hmem {
  border-width: 1px;
  border-style: solid;
  border-image: linear-gradient(to right, #ff565b, rgba(219, 52, 30, 0.5)) 1 0%;
  border-top: 0;
  width: 80%;
  font-family: "Montserrat", sans-serif;
  font-size: 0.7em;
  padding: 7px 0;
  color: #070707;
}

span {
  font-size: 0.5em;
  color: #777777;
}

.bts {
  padding: 5px;
  float: right;
  font-family: "Roboto", sans-serif;
  text-transform: uppercase;
  font-size: 10px;
  border: none;
  color: #ff565b;
  /* cursor: auto; */
}

.btnAct {
  float: right;
  font-family: "Roboto", sans-serif;
  text-transform: uppercase;
  font-size: 10px;
  border: none;
  color: #ff565b;
}

.table-pagination {
  display: flex;
  justify-content: center;
}

.table-pagination ul {
  margin: 15px;
  --bs-pagination-color: var(--bs-accordion-active-bg);
  --bs-pagination-hover-color: #ff565b;
}

.bts:hover {
  text-decoration: underline;
  font-weight: 900;
}
.peco-table {
  overflow: scroll;
  height: 400px;
  overflow-x: hidden;
}
/* scrollbar styling*/
.peco-table::-webkit-scrollbar {
  width: 5px;
  height: 70%;
}
/* Handle */
.peco-table::-webkit-scrollbar-thumb {
  background: #c2bfbf;
}

input {
  font-family: "Roboto", sans-serif;
  padding: 2px;
  margin: 0;
}

.purchaseHistory h2 {
  margin-top: 25px;
}

.settings h2 {
  margin-top: 25px;
}

.noshow {
  opacity: 0;
  display: none;
}

/* .passcodeModal{
    position: fixed;
    display: inline-table;
    justify-content: center;
    top: 40%;
    left: 50%;
    bottom: 50%;
    min-height: 503px;
    transform: translate(-50%, -50%);
    box-shadow: 0px 0px 10px #cccccc;
    border-radius: 10px;
    z-index: 2800;
    width: 520px;
    height: 45%;
    background-color: #fff;
  } */

/* .passcodeModal input{
    width: 80%;
    height: 40%;
    text-align: center;

  } */
.paymentModal {
  /* display: block;  */ /* Hidden by default */
  position: absolute;
  display: inline-table;
  justify-content: center;
  top: 100%;
  left: 50%;
  bottom: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0px 0px 10px #cccccc;
  border-radius: 10px;
  z-index: 2800;
  overflow: scroll;
  max-height: 100%;
  width: 90%; /* Full width */
  height: fit-content; /* Full height */
  /* background-color: rgb(0,0,0); 
    background-color: rgba(0,0,0,0.4);  */
  background-color: #fff;
}
/* scrollbar styling*/
.paymentModal::-webkit-scrollbar {
  width: 5px;
  height: 70%;
}
/* Handle */
.paymentModal::-webkit-scrollbar-thumb {
  background: #c2bfbf;
}
.accordion {
  margin-top: 10px;
  margin-bottom: 10px;
  --bs-accordion-active-bg: #ff565b;
  --bs-accordion-active-color: #212529;
  padding-right: 15px;
}
.accordion-item {
  border: outset;
  border-bottom-color: #e6adaf;
}

.addCard {
  margin-top: 5px;
  display: flex;
  justify-content: center;
  margin-right: 30px;
}

/* The Close Button */
.close {
  color: #212529;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: black;
  text-decoration: none;
  cursor: pointer;
}
.hidden {
  display: none;
}
.editInput {
  text-align: center;
  margin-bottom: 15px;
  margin-top: 42px;
  border-image: linear-gradient(to right, #ff565b, rgba(219, 52, 30, 0.5)) 1 0%;
  border-color: #ff565b;
  color: #0d6efd;
  font-size: large;
}

/* .peco-btn{
  background-color: #fff;
  border-radius: 12px;
  transition-duration: 0.4s;
  color: white;
  background-color: #0d6efd;
} */
/* .peco-btn:hover {
  background-color: #5cff56;
  color: white;
} */
.styled-table {
  border-collapse: collapse;
  /* margin: 25px 0; */
  font-size: 0.9em;
  font-family: sans-serif;
  min-width: 400px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
  color: #777777;
  /* margin-right: 60px; */
}
.styled-table thead tr {
  background-color: #ff565b;
  color: #ffffff;
  text-align: left;
}
.styled-table th,
.styled-table td {
  padding: 12px 15px;
}
.styled-table tbody tr {
  border-bottom: 1px solid #dddddd;
}

.styled-table tbody tr:nth-of-type(even) {
  background-color: #f3f3f3;
}

.styled-table tbody tr:last-of-type {
  border-bottom: 2px solid #ff565b;
}
.styled-table tbody tr.active-row {
  font-weight: bold;
  color: #ff565b;
  background-color: rgba(255, 86, 91, 0.1);
}
.plan-row {
  color: forestgreen !important;
}

/* Badge styles for plan status */
.badge {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-success {
  background-color: #28a745;
  color: white;
}

.badge-secondary {
  background-color: #6c757d;
  color: white;
}

/* Highlight current active plan */
.styled-table tbody tr.active-row td {
  border-left: 4px solid #28a745;
}

/* Current plan summary styles */
#current-plan-summary .alert-success {
  border-left: 5px solid #28a745;
  background-color: rgba(40, 167, 69, 0.1);
}

#no-active-plan-message .alert-warning {
  border-left: 5px solid #ffc107;
  background-color: rgba(255, 193, 7, 0.1);
}

.alert-heading {
  color: inherit;
  font-weight: 600;
}

.alert-heading i {
  margin-right: 8px;
}
/* Dropdown Button */
.setting-dropbtn {
  background-color: #ff565b;
  color: white;
  padding: 16px;
  font-size: 16px;
  border: none;
  cursor: pointer;
  width: 92%;
  margin: 25px;
}

.set_Dropdown {
  justify-content: center;
  position: inherit;
  display: flex;
  flex: auto;
}
/* Dropdown button on hover & focus */
.setting-dropbtn:hover,
.setting-dropbtn:focus {
  background-color: #ff565b;
}

/* The container <div> - needed to position the dropdown content */
.setting-dropdown {
  position: relative;
  display: contents;
}

/* Dropdown Content (Hidden by Default) */
.setting-dropdown-content {
  display: none;
  /* position: absolute; */
  background-color: #f6f6f6;
  min-width: 230px;
  border: 1px solid #ddd;
  z-index: 1;
  /*  width: inherit; */
  /* margin-top: 85px; */
}

/* Links inside the dropdown */
.setting-dropdown-content a {
  color: black;
  padding: 12px 16px;
  text-decoration: none;
  display: block;
}
.modal {
  z-index: 1060 !important; /* Higher than other elements */
}

/* Change color of dropdown links on hover */
.setting-dropdown-content a:hover {
  background-color: #ff565b;
}

/* Show the dropdown menu (use JS to add this class to the .dropdown-content container when the user clicks on the dropdown button) */
.show {
  display: none;
}

.billing {
  padding: 20px;
  /* display: flex; */
}
.needs-validation {
  padding: 20px;
}
.modal-backdrop.fade {
  opacity: 0;
}

/*--------------------
Credit Card
--------------------*/

@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap");

.container-payment-card {
  margin: 30px auto;
}

.container-payment-card .card {
  width: 100%;
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  background: #fff;
  border-radius: 0px;
  margin-top: 10px;
}

.btn.btn-primary {
  background-color: #ff565b;
}

.btn.btn-primary:focus {
  box-shadow: none;
}

.container-payment-card .card .img-box {
  width: 100%;
  height: 50px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.container-payment-card .card img {
  width: 5em;
  object-fit: fill;
}

.container-payment-card .card .number {
  font-size: 20px;
}

.container-payment-card .card-body .btn.btn-primary .fab.fa-cc-paypal {
  font-size: 32px;
  color: #3333f7;
}

.fab.fa-cc-amex {
  color: #1c6acf;
  font-size: 32px;
}

.fab.fa-cc-mastercard {
  font-size: 32px;
  color: red;
}

.fab.fa-cc-discover {
  font-size: 32px;
  color: orange;
}

.c-green {
  color: green;
}

.box {
  height: 40px;
  width: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ddd;
}

.btn.btn-primary.payment {
  background-color: #1c6acf;
  color: white;
  border-radius: 0px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 24px;
}

.form__div {
  height: 50px;
  position: relative;
  margin-bottom: 24px;
}

.form-control {
  width: 100%;
  height: 45px;
  font-size: 14px;
  border: 1px solid #dadce0;
  border-radius: 0;
  outline: none;
  padding: 2px;
  background: none;
  z-index: 1;
  box-shadow: none;
}

.form__label {
  position: absolute;
  left: 16px;
  top: 10px;
  background-color: #fff;
  color: #80868b;
  font-size: 16px;
  transition: 0.3s;
  text-transform: uppercase;
}

.form-control:focus + .form__label {
  top: -8px;
  left: 12px;
  color: #1a73e8;
  font-size: 12px;
  font-weight: 500;
  z-index: 10;
}

.form-control:not(:placeholder-shown).form-control:not(:focus) + .form__label {
  top: -8px;
  left: 12px;
  font-size: 12px;
  font-weight: 500;
  z-index: 10;
}

.form-control:focus {
  border: 1.5px solid #1a73e8;
  box-shadow: none;
}
.container-payment-card small {
  font-size: 1.7em !important;
}

.container-payment-card .card-user {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-left: 10px;
}

.container-payment-card .img-box .badge-danger {
  background-color: #ff565b;
  height: fit-content;
}
.container-payment-card .row {
  max-height: 350px;
  overflow-y: scroll;
}

.container-payment-card .row::-webkit-scrollbar {
  width: 5px;
  height: 70%;
}
/* Handle */
.container-payment-card .row::-webkit-scrollbar-thumb {
  background: #c2bfbf;
}
.profile span {
  font-size: 0.9em;
}
.update_label {
  color: green;
  text-align: center !important;
}
.incorrect_label {
  color: red;
  text-align: center !important;
}
.passcodeModal p {
  display: block;
  color: #0d6efd;
  float: left;
  width: 50%;
  margin-bottom: 0px;
}
.page-banner {
  padding-top: 80px;
  padding-bottom: 40px;
}
.profile p {
  font-size: 18px;
}
/* .profile h2{
  color: #000;
} */

/* Modal styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1060 !important;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.4);
}

.modal-dialog {
  margin: 10% auto;
  width: 80%;
  max-width: 500px;
}

.modal-content {
  background-color: #fefefe;
  margin: auto;
  padding: 20px;
  border: 1px solid #888;
  border-radius: 5px;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ddd;
  padding-bottom: 10px;
  margin-bottom: 15px;
}

.modal-title {
  margin: 0;
  color: #333;
}

.modal-footer {
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px solid #ddd;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.hidden {
  display: none;
}
