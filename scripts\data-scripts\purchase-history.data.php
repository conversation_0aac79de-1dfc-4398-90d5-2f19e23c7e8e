<?php

header("Content-Type: application/json");
session_start();

include '../class-scripts/db-connection.class.php';
include '../class-scripts/user.class.php';

$sessionUserID = $_SESSION['id'];

// Add debugging
error_log("Purchase history request for user ID: " . $sessionUserID);

if (isset($_POST['sql_offset'])) {
    $SqlOffset = $_POST['sql_offset'];
    $searchPhrase = $_POST['search_phrase'];
    $startDate = $_POST['start_date'];
    $endDate = $_POST['end_date'];

    error_log("Purchase history parameters: offset=$SqlOffset, search=$searchPhrase, start=$startDate, end=$endDate");

    $userObj = new User();
    $userObj->fetchPurchaseHistories($sessionUserID, $SqlOffset, $searchPhrase, $startDate, $endDate);
}
