<!DOCTYPE html>
<html lang="en">

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;300;400;500;700;900&display=swap" rel="stylesheet">


    <title>Cart</title>

    <?php session_start();
    // include '../class-scripts/db-connection.class.php';
    //var_dump($_SESSION["cart"]); 
    ?>

    <!-- Bootstrap core CSS -->
    <link href="vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">


    <!-- Additional CSS Files -->
    <link rel="stylesheet" href="assets/css/cart.css">
    <link rel="stylesheet" href="assets/css/fontawesome.css">
    <!-- <link rel="stylesheet" href="assets/css/custom.css"> -->
    <link rel="stylesheet" href="assets/css/owl.css">
    <link rel="stylesheet" href="assets/css/animate.css">
    <link rel="stylesheet" href="https://unpkg.com/swiper@7/swiper-bundle.min.css" />
</head>

<body>

    <!-- Loader Start-->
    <?php require_once(__DIR__ . '/resources/loader.php'); ?>
    <!-- Loader End-->

    <?php require_once(__DIR__ . '/resources/navbar.php'); ?>

    <!-- Header Start-->
    <?php require_once(__DIR__ . '/resources/header.php'); ?>
    <!-- Header End-->

    <div class="page-banner change-name">
        <div class="container">
            <div class="row">
                <div class="col-lg-6 offset-lg-3">
                    <div class="header-text">
                        <h2><em>Cart List</em></h2>
                    </div>
                </div>
            </div>
        </div>
    </div>

    </header>

    <!-- cart + summary Start-->
    <!-- Cart Start-->
    <?php require_once(__DIR__ . '/resources/cart-me.php'); ?>
    <!-- Cart End-->
    <!-- cart End -->


    <section>
        <!-- Recomendend Items Start-->
        <?php require_once(__DIR__ . '/resources/recomended-item.php'); ?>
        <!-- Recomendend Items End-->
        <!-- Recomendend Items End -->
    </section>
    <!-- Recommended -->
    </section>




    <!-- Payment Success Modal -->
    <div class="modal fade" id="paymentSuccessModal" tabindex="-1" role="dialog" aria-labelledby="paymentSuccessModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="paymentSuccessModalLabel">
                        <i class="fas fa-check-circle"></i> Payment Successful!
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-check-circle text-success" style="font-size: 3rem;"></i>
                    </div>
                    <h4>Thank you for your purchase!</h4>
                    <p class="mb-3">Your payment has been processed successfully.</p>
                    <p><strong>Transaction Reference:</strong> <span id="paymentReference">-</span></p>
                    <p class="text-muted">You can view your purchase history in your account settings.</p>
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="button" class="btn btn-success" onclick="window.location.href='account-settings.php?tab=purchaseHistory'">
                        View Purchase History
                    </button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer Start-->
    <?php require_once(__DIR__ . '/resources/footer.php'); ?>
    <!-- Footer End-->


    <!-- Scripts -->
    <!-- Bootstrap core JavaScript -->
    <script src="vendor/jquery/jquery.min.js"></script>
    <!-- <script src="vendor/bootstrap/js/bootstrap.min.js"></script> -->
    <script src="vendor/bootstrap/js/popper.min.js"></script>
    <script src="vendor/bootstrap/js/bootstrap.5.2.3.min.js"></script>

    <script src="vendor/swiper/swiper-bundle.min.js"></script>
    <script src="vendor/glightbox/js/glightbox.min.js"></script>
    <script src="vendor/aos/aos.js"></script>
    <script src="assets/js/isotope.min.js"></script>
    <script src="assets/js/owl-carousel.js"></script>
    <script src="assets/js/mo.js"></script>
    <script src="assets/js/typed.min.js"></script>

    <script src="assets/js/tabs.js"></script>
    <script src="assets/js/popup.js"></script>
    <script src="assets/js/custom.js"></script>

    <!--  <script src="scripts/view-scripts/check_out-view.js"></script> -->
    <script src="https://js.paystack.co/v1/inline.js"></script>
    <script src="scripts/view-scripts/view-carts.js"></script>
    <script src="scripts/view-scripts/get-cart-total.js"></script>
    <script src="resources/make_payment.js"></script>
    <script src="scripts/view-scripts/cart-purchase.js"></script>
    <!-- <?php require_once(__DIR__ . '/resources/utility_scripts.php'); ?>
     -->

    <script>
        $(document).ready(function() {
            set_interval();
        });
        $('body').click(function() {
            reset_interval();
        });
        $('body').mousemove(function() {
            reset_interval();
        });
        $('body').keypress(function() {
            reset_interval();
        });

        // setTimeout(function() {
        //     $('.loader').fadeToggle();
        // }, 1500);

        $("a[href='#top']").click(function() {
            $("html, body").animate({
                scrollTop: 0
            }, "slow");
            return false;
        });

        let logout_link = document.getElementsByClassName("logged-out");
        let login_link = document.getElementsByClassName("logged-in");

        $.post("scripts/data-scripts/auth-status.data.php", {
            request_type: 'auth-session-check'
        }, function(data) {
            if (data["message"] == "success") {
                // authed
                $(logout_link).addClass("hide");
                $(login_link).removeClass("hide");

                $("#authenticated-username").html(data["username"]);
            } else {
                // not authed
                $(logout_link).removeClass("hide");
                $(login_link).addClass("hide");
            }
        });

        $.ajax({
            type: 'get',
            url: 'scripts/data-scripts/user_profile.data.php',
            data: {
                user_id: 0
            },
            dataType: 'json',
            success: function(result) {
                if (result[0].avatar == "") {
                    return;
                }
                $("#auth-imgg").attr("src", `assets/images/${result[0].avatar}`);
                $("#auth-img").attr("src", `assets/images/${result[0].avatar} `);
            }
        });



        $("#signout-btn").click(function() {
            logout_call();
        });



        function set_interval() {
            timer = setInterval(() => {
                auto_logout();
            }, 3600000);
        }

        function reset_interval() {
            clearInterval(timer);
            timer = setInterval(() => {
                auto_logout();
            }, 3600000);
        }

        function auto_logout() {
            logout_call();
        }

        function logout_call() {
            console.log("auto logout called");
            $.post("scripts/data-scripts/auth-status.data.php", {
                request_type: 'auth-session-end'
            }, function(data) {
                if (data["message"] == "success") {
                    // signed out
                    window.location.href = "signup.php?login";
                }
            });
        }
    </script>

    <script>
        setTimeout(function() {
            $('.loader').fadeToggle();
        }, 1500);

        $("a[href='#top']").click(function() {
            $("html, body").animate({
                scrollTop: 0
            }, "slow");
            return false;
        });
    </script>

</body>

</html>