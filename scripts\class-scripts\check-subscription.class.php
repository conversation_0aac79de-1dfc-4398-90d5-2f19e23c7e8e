<?php

class CheckSub extends DBconnection
{
    public function checkSubscription($user_id)
    {
        $sql = "SELECT * FROM user_plans WHERE user_id = $user_id AND date_end >= NOW()";
        $runSQL = mysqli_query($this->connect(), $sql);

        if (!$runSQL) {
            echo json_encode(["message" => "error"]);
            return;
        }

        if (mysqli_num_rows($runSQL) > 0) {
            // User has an active subscription
            echo json_encode(["has_subscription" => true]);
        } else {
            // User has no active subscription
            echo json_encode(["has_subscription" => false]);
        }
    }

    public function checkPlanSubscription($user_id, $plan_id)
    {
        // Check for active subscription to this specific plan
        $sql = "SELECT * FROM user_plans WHERE user_id = $user_id AND plan_id = $plan_id AND date_end >= NOW() AND status = 1";
        $runSQL = mysqli_query($this->connect(), $sql);

        if (!$runSQL) {
            echo json_encode(["message" => "error"]);
            return;
        }

        if (mysqli_num_rows($runSQL) > 0) {
            // User has an active subscription to this plan
            echo json_encode([
                "has_subscription" => true,
                "plan_id" => $plan_id,
                "is_current_plan" => true
            ]);
        } else {
            // Check if user was ever subscribed to this plan
            $pastSql = "SELECT * FROM user_plans WHERE user_id = $user_id AND plan_id = $plan_id AND status = 1";
            $pastRunSQL = mysqli_query($this->connect(), $pastSql);

            if ($pastRunSQL && mysqli_num_rows($pastRunSQL) > 0) {
                // User was previously subscribed to this plan
                echo json_encode([
                    "has_subscription" => false,
                    "plan_id" => $plan_id,
                    "was_subscribed" => true
                ]);
            } else {
                // User has never subscribed to this plan
                echo json_encode([
                    "has_subscription" => false,
                    "plan_id" => $plan_id,
                    "was_subscribed" => false
                ]);
            }
        }
    }
}
