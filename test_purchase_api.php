<?php
// session_start();

// // Set session to user 167 for testing
// $_SESSION['id'] = 167;
// $_SESSION['username'] = 'test_user_167';

// echo "<h2>Testing Purchase History API for User 167</h2>";

// // Simulate the AJAX call
// $_POST['sql_offset'] = 0;
// $_POST['search_phrase'] = '';
// $_POST['start_date'] = '';
// $_POST['end_date'] = '';

// echo "<p>Simulating API call with parameters:</p>";
// echo "<ul>";
// echo "<li>User ID: " . $_SESSION['id'] . "</li>";
// echo "<li>Offset: " . $_POST['sql_offset'] . "</li>";
// echo "<li>Search: '" . $_POST['search_phrase'] . "'</li>";
// echo "<li>Start Date: '" . $_POST['start_date'] . "'</li>";
// echo "<li>End Date: '" . $_POST['end_date'] . "'</li>";
// echo "</ul>";

// echo "<h3>API Response:</h3>";

// // Capture the output from the API
// ob_start();
// include 'scripts/data-scripts/purchase-history.data.php';
// $api_output = ob_get_clean();

// echo "<pre style='background: #f0f0f0; padding: 10px; max-height: 400px; overflow-y: scroll;'>";
// echo htmlspecialchars($api_output);
// echo "</pre>";

// // Try to parse the JSON
// $data = json_decode($api_output, true);
// if ($data) {
//     echo "<h3>Parsed JSON:</h3>";
//     echo "<pre>" . print_r($data, true) . "</pre>";

//     if (isset($data['message'])) {
//         echo "<p><strong>Message:</strong> " . $data['message'] . "</p>";
//     }

//     if (isset($data['purchase_histories'])) {
//         echo "<p><strong>Purchase Records Found:</strong> " . count($data['purchase_histories']) . "</p>";

//         if (!empty($data['purchase_histories'])) {
//             echo "<table border='1' style='border-collapse: collapse;'>";
//             echo "<tr><th>Type</th><th>Item Name</th><th>Item ID</th><th>Amount</th><th>Date</th><th>Status</th></tr>";

//             foreach ($data['purchase_histories'] as $purchase) {
//                 echo "<tr>";
//                 echo "<td>" . ($purchase['purchase_type'] ?? 'N/A') . "</td>";
//                 echo "<td>" . ($purchase['item_name'] ?? 'N/A') . "</td>";
//                 echo "<td>" . ($purchase['item_id'] ?? 'N/A') . "</td>";
//                 echo "<td>₦" . ($purchase['amount'] ?? '0') . "</td>";
//                 echo "<td>" . ($purchase['purchase_date'] ?? 'N/A') . "</td>";
//                 echo "<td>" . ($purchase['status'] ?? 'N/A') . "</td>";
//                 echo "</tr>";
//             }
//             echo "</table>";
//         }
//     }

//     if (isset($data['error_message'])) {
//         echo "<p><strong>Error:</strong> " . $data['error_message'] . "</p>";
//     }
// } else {
//     echo "<p>❌ Failed to parse JSON response</p>";
//     echo "<p>JSON Error: " . json_last_error_msg() . "</p>";
// }

// // Check error logs
// echo "<h3>Recent Error Logs</h3>";
// $log_file = ini_get('error_log');
// if ($log_file && file_exists($log_file)) {
//     $logs = file_get_contents($log_file);
//     $lines = explode("\n", $logs);
//     $recent_lines = array_slice($lines, -30);

//     $relevant_logs = [];
//     foreach ($recent_lines as $line) {
//         if (stripos($line, 'Purchase history') !== false || 
//             stripos($line, 'SQL') !== false ||
//             stripos($line, '167') !== false) {
//             $relevant_logs[] = $line;
//         }
//     }

//     if (!empty($relevant_logs)) {
//         echo "<pre style='background: #f0f0f0; padding: 10px; max-height: 300px; overflow-y: scroll;'>";
//         foreach ($relevant_logs as $log) {
//             echo htmlspecialchars($log) . "\n";
//         }
//         echo "</pre>";
//     } else {
//         echo "<p>No relevant error logs found</p>";
//     }
// }

?>
<!-- 
<p><a href="account-settings.php">Go to Account Settings</a></p>
<p><a href="test_user_167.php">User 167 Test</a></p> -->


<?php
session_start();

// Set session to user 167 for testing
$_SESSION['id'] = 167;
$_SESSION['username'] = 'test_user_167';

echo "<h2>Testing Purchase History API for User 167</h2>";

// Simulate the AJAX call
$_POST['sql_offset'] = 0;
$_POST['search_phrase'] = '';
$_POST['start_date'] = '';
$_POST['end_date'] = '';

echo "<p>Simulating API call with parameters:</p>";
echo "<ul>";
echo "<li>User ID: " . $_SESSION['id'] . "</li>";
echo "<li>Offset: " . $_POST['sql_offset'] . "</li>";
echo "<li>Search: '" . $_POST['search_phrase'] . "'</li>";
echo "<li>Start Date: '" . $_POST['start_date'] . "'</li>";
echo "<li>End Date: '" . $_POST['end_date'] . "'</li>";
echo "</ul>";

echo "<h3>API Response:</h3>";

// Capture the output from the API
ob_start();
include 'scripts/data-scripts/purchase-history.data.php';
$api_output = ob_get_clean();

echo "<pre style='background: #f0f0f0; padding: 10px; max-height: 400px; overflow-y: scroll;'>";
echo htmlspecialchars($api_output);
echo "</pre>";

// Try to parse the JSON
$data = json_decode($api_output, true);
if ($data) {
    echo "<h3>Parsed JSON:</h3>";
    echo "<pre>" . print_r($data, true) . "</pre>";

    if (isset($data['message'])) {
        echo "<p><strong>Message:</strong> " . $data['message'] . "</p>";
    }

    if (isset($data['purchase_histories'])) {
        echo "<p><strong>Purchase Records Found:</strong> " . count($data['purchase_histories']) . "</p>";

        if (!empty($data['purchase_histories'])) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Type</th><th>Item Name</th><th>Item ID</th><th>Amount</th><th>Date</th><th>Status</th></tr>";

            foreach ($data['purchase_histories'] as $purchase) {
                echo "<tr>";
                echo "<td>" . ($purchase['purchase_type'] ?? 'N/A') . "</td>";
                echo "<td>" . ($purchase['item_name'] ?? 'N/A') . "</td>";
                echo "<td>" . ($purchase['item_id'] ?? 'N/A') . "</td>";
                echo "<td>₦" . ($purchase['amount'] ?? '0') . "</td>";
                echo "<td>" . ($purchase['purchase_date'] ?? 'N/A') . "</td>";
                echo "<td>" . ($purchase['status'] ?? 'N/A') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }

    if (isset($data['error_message'])) {
        echo "<p><strong>Error:</strong> " . $data['error_message'] . "</p>";
    }
} else {
    echo "<p>❌ Failed to parse JSON response</p>";
    echo "<p>JSON Error: " . json_last_error_msg() . "</p>";
}

// Check error logs
echo "<h3>Recent Error Logs</h3>";
$log_file = ini_get('error_log');
if ($log_file && file_exists($log_file)) {
    $logs = file_get_contents($log_file);
    $lines = explode("\n", $logs);
    $recent_lines = array_slice($lines, -30);

    $relevant_logs = [];
    foreach ($recent_lines as $line) {
        if (
            stripos($line, 'Purchase history') !== false ||
            stripos($line, 'SQL') !== false ||
            stripos($line, '167') !== false
        ) {
            $relevant_logs[] = $line;
        }
    }

    if (!empty($relevant_logs)) {
        echo "<pre style='background: #f0f0f0; padding: 10px; max-height: 300px; overflow-y: scroll;'>";
        foreach ($relevant_logs as $log) {
            echo htmlspecialchars($log) . "\n";
        }
        echo "</pre>";
    } else {
        echo "<p>No relevant error logs found</p>";
    }
}

?>

<p><a href="account-settings.php">Go to Account Settings</a></p>
<p><a href="test_user_167.php">User 167 Test</a></p>