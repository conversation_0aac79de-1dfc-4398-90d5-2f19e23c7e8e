<?php
session_start();
header("Content-Type: application/json");

include '../class-scripts/db-connection.class.php';
include '../class-scripts/collections.class.php';

$CollectionObj = new Collections();

// Handle different actions based on request type
$action = isset($_POST['action']) ? $_POST['action'] : 'fetch';

switch($action) {
    case 'create':
        // Validate required fields
        if (!isset($_POST['title'])) {
            echo json_encode([
                'success' => false,
                'message' => 'Missing required fields'
            ]);
            exit;
        }

        // Validate user session
        if (!isset($_SESSION['user_id']) || !isset($_POST['userId']) || $_SESSION['user_id'] != $_POST['userId']) {
            echo json_encode([
                'success' => false,
                'message' => 'Unauthorized access'
            ]);
            exit;
        }

        // Create collection (always public)
        $result = $CollectionObj->createCollection(
            $_POST['userId'],
            $_POST['title'],
            'public' // Force public type
        );
        
        echo json_encode($result);
        break;

    case 'fetch':
    default:
        $CollectionObj->fetchCollections();
        break;
}
?>