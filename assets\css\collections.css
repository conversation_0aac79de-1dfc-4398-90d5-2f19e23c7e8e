.collections-wrapper {
    padding: 40px 0;
    background-color: #f8f9fa;
}

.section-title {
    margin-bottom: 30px;
    font-size: 28px;
    font-weight: 700;
    color: #333;
    position: relative;
    padding-left: 15px;
}

.section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 25px;
    background-color: #ff565b;
    border-radius: 2px;
}

.collection-card {
    background: #fff;
    border: none;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.collection-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(255, 86, 91, 0.15);
}

.collection-card .card-img-top {
    height: 220px;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.collection-card:hover .card-img-top {
    transform: scale(1.05);
}

.collection-card .card-body {
    padding: 1.5rem;
}

.collection-card .card-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #333;
}

.collection-card .card-text {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.collection-card .card-footer {
    background-color: transparent;
    border-top: 1px solid #eee;
    padding: 1rem 1.5rem;
}

.collection-card .curator-badge {
    background-color: rgba(255, 86, 91, 0.1);
    color: #ff565b;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.collection-date {
    color: #888;
    font-size: 0.8rem;
}

.collection-stats {
    margin: 10px 0;
    color: #666;
    font-size: 0.9rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #666;
    font-size: 0.85rem;
}

.stat-item i {
    color: #ff565b;
}

.image-count {
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.image-count i {
    color: #ff565b;
}

#error-message {
    display: none;
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: #ff565b;
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    z-index: 1000;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.collection-images {
    position: relative;
    aspect-ratio: 16/9;
    overflow: hidden;
}

.image-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 2px;
    height: 100%;
}

.grid-item {
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 100%;
}

.grid-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.collection-card:hover .grid-item img {
    transform: scale(1.05);
}

.collection-images img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}