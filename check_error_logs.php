<?php
// echo "<h2>Error Log Analysis</h2>";

// // Check PHP error log
// $error_log_paths = [
//     ini_get('error_log'),
//     'C:\xampp\apache\logs\error.log',
//     'C:\xampp\php\logs\php_error_log',
//     '/var/log/apache2/error.log',
//     '/var/log/php_errors.log'
// ];

// echo "<h3>Checking Error Logs</h3>";

// foreach ($error_log_paths as $log_path) {
//     if ($log_path && file_exists($log_path)) {
//         echo "<h4>Log file: $log_path</h4>";

//         $logs = file_get_contents($log_path);
//         $lines = explode("\n", $logs);

//         // Get recent lines (last 50)
//         $recent_lines = array_slice($lines, -50);

//         $relevant_logs = [];
//         foreach ($recent_lines as $line) {
//             if (stripos($line, 'cart') !== false || 
//                 stripos($line, 'purchase') !== false || 
//                 stripos($line, 'payment') !== false ||
//                 stripos($line, 'verify_cart_payment') !== false ||
//                 stripos($line, 'puchase_history') !== false ||
//                 stripos($line, 'purchase_history') !== false) {
//                 $relevant_logs[] = $line;
//             }
//         }

//         if (!empty($relevant_logs)) {
//             echo "<div style='background: #f0f0f0; padding: 10px; max-height: 400px; overflow-y: scroll;'>";
//             echo "<pre>";
//             foreach ($relevant_logs as $log) {
//                 echo htmlspecialchars($log) . "\n";
//             }
//             echo "</pre>";
//             echo "</div>";
//         } else {
//             echo "<p>No relevant logs found in this file</p>";
//         }

//         break; // Use the first available log file
//     }
// }

// // Check Apache access logs for payment verification requests
// $access_log_paths = [
//     'C:\xampp\apache\logs\access.log',
//     '/var/log/apache2/access.log'
// ];

// echo "<h3>Checking Access Logs for Payment Verification</h3>";

// foreach ($access_log_paths as $log_path) {
//     if (file_exists($log_path)) {
//         echo "<h4>Access log: $log_path</h4>";

//         $logs = file_get_contents($log_path);
//         $lines = explode("\n", $logs);

//         // Get recent lines (last 100)
//         $recent_lines = array_slice($lines, -100);

//         $payment_requests = [];
//         foreach ($recent_lines as $line) {
//             if (stripos($line, 'verify_cart_payment') !== false || 
//                 stripos($line, 'verify_payment') !== false) {
//                 $payment_requests[] = $line;
//             }
//         }

//         if (!empty($payment_requests)) {
//             echo "<div style='background: #f0f0f0; padding: 10px; max-height: 300px; overflow-y: scroll;'>";
//             echo "<pre>";
//             foreach ($payment_requests as $request) {
//                 echo htmlspecialchars($request) . "\n";
//             }
//             echo "</pre>";
//             echo "</div>";
//         } else {
//             echo "<p>No payment verification requests found in access log</p>";
//         }

//         break; // Use the first available log file
//     }
// }

// // Check session data
// session_start();
// echo "<h3>Current Session Data</h3>";
// echo "<pre>";
// print_r($_SESSION);
// echo "</pre>";

// // Test database connection
// echo "<h3>Database Connection Test</h3>";
// include 'resources/db_config/db_connection.php';

// if ($db->connect_error) {
//     echo "<p>❌ Database connection failed: " . $db->connect_error . "</p>";
// } else {
//     echo "<p>✅ Database connection successful</p>";

//     // Test a simple query
//     $result = $db->query("SELECT COUNT(*) as total FROM puchase_history");
//     if ($result) {
//         $row = $result->fetch_assoc();
//         echo "<p>Total records in puchase_history: " . $row['total'] . "</p>";
//     } else {
//         echo "<p>❌ Query failed: " . $db->error . "</p>";
//     }
// }

?>

<!-- <p><a href="check_purchases.php">Check Purchases</a></p>
<p><a href="test_purchase_flow.php">Purchase Flow Test</a></p> -->


<?php
echo "<h2>Error Log Analysis</h2>";

// Check PHP error log
$error_log_paths = [
    ini_get('error_log'),
    'C:\xampp\apache\logs\error.log',
    'C:\xampp\php\logs\php_error_log',
    '/var/log/apache2/error.log',
    '/var/log/php_errors.log'
];

echo "<h3>Checking Error Logs</h3>";

foreach ($error_log_paths as $log_path) {
    if ($log_path && file_exists($log_path)) {
        echo "<h4>Log file: $log_path</h4>";

        $logs = file_get_contents($log_path);
        $lines = explode("\n", $logs);

        // Get recent lines (last 50)
        $recent_lines = array_slice($lines, -50);

        $relevant_logs = [];
        foreach ($recent_lines as $line) {
            if (
                stripos($line, 'cart') !== false ||
                stripos($line, 'purchase') !== false ||
                stripos($line, 'payment') !== false ||
                stripos($line, 'verify_cart_payment') !== false ||
                stripos($line, 'puchase_history') !== false ||
                stripos($line, 'purchase_history') !== false
            ) {
                $relevant_logs[] = $line;
            }
        }

        if (!empty($relevant_logs)) {
            echo "<div style='background: #f0f0f0; padding: 10px; max-height: 400px; overflow-y: scroll;'>";
            echo "<pre>";
            foreach ($relevant_logs as $log) {
                echo htmlspecialchars($log) . "\n";
            }
            echo "</pre>";
            echo "</div>";
        } else {
            echo "<p>No relevant logs found in this file</p>";
        }

        break; // Use the first available log file
    }
}

// Check Apache access logs for payment verification requests
$access_log_paths = [
    'C:\xampp\apache\logs\access.log',
    '/var/log/apache2/access.log'
];

echo "<h3>Checking Access Logs for Payment Verification</h3>";

foreach ($access_log_paths as $log_path) {
    if (file_exists($log_path)) {
        echo "<h4>Access log: $log_path</h4>";

        $logs = file_get_contents($log_path);
        $lines = explode("\n", $logs);

        // Get recent lines (last 100)
        $recent_lines = array_slice($lines, -100);

        $payment_requests = [];
        foreach ($recent_lines as $line) {
            if (
                stripos($line, 'verify_cart_payment') !== false ||
                stripos($line, 'verify_payment') !== false
            ) {
                $payment_requests[] = $line;
            }
        }

        if (!empty($payment_requests)) {
            echo "<div style='background: #f0f0f0; padding: 10px; max-height: 300px; overflow-y: scroll;'>";
            echo "<pre>";
            foreach ($payment_requests as $request) {
                echo htmlspecialchars($request) . "\n";
            }
            echo "</pre>";
            echo "</div>";
        } else {
            echo "<p>No payment verification requests found in access log</p>";
        }

        break; // Use the first available log file
    }
}

// Check session data
session_start();
echo "<h3>Current Session Data</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

// Test database connection
echo "<h3>Database Connection Test</h3>";
include 'resources/db_config/db_connection.php';

if ($db->connect_error) {
    echo "<p>❌ Database connection failed: " . $db->connect_error . "</p>";
} else {
    echo "<p>✅ Database connection successful</p>";

    // Test a simple query
    $result = $db->query("SELECT COUNT(*) as total FROM puchase_history");
    if ($result) {
        $row = $result->fetch_assoc();
        echo "<p>Total records in puchase_history: " . $row['total'] . "</p>";
    } else {
        echo "<p>❌ Query failed: " . $db->error . "</p>";
    }
}

?>

<p><a href="check_purchases.php">Check Purchases</a></p>
<p><a href="test_purchase_flow.php">Purchase Flow Test</a></p>