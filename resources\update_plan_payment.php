<?php
//CODE WRITTEN BY EMMANUEL DO NOT TOUCH
include 'db_config/db_connection.php';
include "../scripts/config/encryption-handlers.php";
session_start();

$curl = curl_init();

$ref = $_GET["reference"];
$plan_id = $_GET["plan_id"];
$user_id = $_GET["user_id"];
$email = $_GET["email"];
$monthCount = $_GET["month_count"];
// echo $monthCount.'<br/>';

curl_setopt_array($curl, array(
    CURLOPT_URL => "https://api.paystack.co/transaction/verify/$ref",
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => "",
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => "GET",
    CURLOPT_HTTPHEADER => array(
        "Authorization: Bearer sk_test_da3e0e887b69f7342464f89a6656150a09d676a0",
        "Cache-Control: no-cache",
    ),
));

$response = curl_exec($curl);
$err = curl_error($curl);

curl_close($curl);

if ($err) {
    echo "cURL Error #:" . $err;
} else {
    // echo $response;
    $response = json_decode($response, true);
    if ($response["status"] == true) {
        $amount = $response["data"]["amount"] / 100;

        $query = "SELECT date_end FROM user_plans WHERE user_id = $user_id AND status = 1";
        $result = mysqli_query($db, $query);
        $row = mysqli_fetch_assoc($result);

        if ($row) {
            $currentPlanEndDate = $row['date_end'];

            $date = new DateTime($currentPlanEndDate);
            $date->modify('+' . $monthCount . ' months');
            $planDateEnd = $date->format('Y-m-d');
        } else {
            echo "Plan not found.";
            return;
        }

        //update user_plans table
        $query = "UPDATE user_plans SET state_date = '$currentDate', date_end = '$planDateEnd', amount = '$amount', plan_id = '$plan_id' WHERE user_id = $user_id AND status = 1";

        $result = mysqli_query($db, $query);

        if ($result) {
            echo "successful";
        } else {
            echo mysqli_error($db);
        }
        return;
    }
}

echo "unsuccessful";
