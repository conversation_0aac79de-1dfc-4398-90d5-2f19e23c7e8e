<?php

// header("Content-Type: application/json");
// session_start();

// include '../class-scripts/db-connection.class.php';
// include '../class-scripts/plans.class.php';



// if(isset($_POST['request_type'])){
//     // if(isset($_POST['request_type'] == "check-logged")){
//     //     if (isset($_SESSION["id"])) {
//     //         echo $user =  $_SESSION["id"];
//     //     } else {
//     //         $user = 0;
//     //     }
//     // }
//     if($_POST['request_type'] == "get-plans"){
//         $PlansObj = new Plans();
//         $PlansObj->fetchPlans();
//     }
// }


header("Content-Type: application/json");
session_start();

include '../class-scripts/db-connection.class.php';
include '../class-scripts/plans.class.php';

if (isset($_POST['request_type'])) {
    if ($_POST['request_type'] == "get-plans") {
        $plansObj = new Plans();
        $plansObj->fetchPlans();
    } else if ($_POST['request_type'] == "get-user-plans") {
        $user_id = isset($_SESSION["id"]) ? $_SESSION["id"] : 0;
        getUserPlans($user_id);
    }
}

function getUserPlans($user_id)
{
    $db = new DBconnection();
    $conn = $db->connect();

    if ($user_id == 0) {
        echo json_encode(["message" => "not logged in"]);
        return;
    }

    $sql = "SELECT up.*, pp.plan_name, pp.amount, pp.duration 
            FROM user_plans up 
            JOIN payment_plan pp ON up.plan_id = pp.id 
            WHERE up.user_id = $user_id 
            ORDER BY up.date_end DESC";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        echo json_encode(["message" => "error"]);
        return;
    }

    if (mysqli_num_rows($result) == 0) {
        echo json_encode(["message" => "no plans"]);
        return;
    }

    $plans = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $status = 'Expired';
        if ($row['status'] == 1 && strtotime($row['date_end']) >= time()) {
            $status = 'Active';
        }

        $plans[] = [
            'id' => $row['id'],
            'plan_id' => $row['plan_id'],
            'plan_name' => $row['plan_name'],
            'amount' => $row['amount'],
            'duration' => $row['duration'],
            'start_date' => $row['state_date'],
            'expiry_date' => $row['date_end'],
            'status' => $status
        ];
    }

    echo json_encode([
        "message" => "success",
        "plans" => $plans
    ]);
}
