// $(document).ready(function () {
//   console.log("User Settings script loaded");
//   $(".remove-notification").on("click", function () {
//     $("#settings_confirmation").show();
//   });

//   $(".cancel-notification").on("click", function () {
//     $("#cancel_confirmation").show();
//   });

//   $(".close-modal").on("click", function () {
//     $("#cancel_confirmation").hide();
//     $("#settings_confirmation").hide();
//   });
// });

$(document).ready(function () {
  console.log("User Settings script loaded");

  // Handle notification removal button click
  $(".remove-notification").on("click", function () {
    $("#confirmation-message").text(
      "Are you sure you want to disable notifications when someone orders your image?"
    );
    $("#settings_confirmation").removeClass("hidden").show();

    // Set the action type for the confirm button
    $(".confirm-action").data("action", "remove-notification");
  });

  // Handle subscription cancellation button click
  $(".cancel-notification").on("click", function () {
    $("#confirmation-message").text(
      "Are you sure you want to cancel your subscription?"
    );
    $("#settings_confirmation").removeClass("hidden").show();

    // Set the action type for the confirm button
    $(".confirm-action").data("action", "cancel-subscription");
  });

  // Handle confirmation modal close buttons
  $(".close-modal").on("click", function () {
    $(".modal").hide();
  });

  // Handle confirmation action button
  $(".confirm-action").on("click", function () {
    const action = $(this).data("action");

    // Hide the confirmation modal
    $("#settings_confirmation").hide();

    // Show loading indicator
    $("#result-message").text("Processing your request...");
    $("#settings_result").show();

    // Perform the appropriate action based on the button clicked
    if (action === "remove-notification") {
      $.ajax({
        url: "scripts/data-scripts/user-settings.data.php",
        type: "POST",
        data: { action: "disable_notifications" },
        dataType: "json",
        success: function (response) {
          console.log("Response:", response);
          if (response.status === "success") {
            $("#result-message").text(
              "Notifications have been disabled successfully."
            );
          } else {
            $("#result-message").text(
              "Failed to disable notifications: " + response.message
            );
          }
        },
        error: function (xhr, status, error) {
          console.error("Error:", xhr.responseText);
          $("#result-message").text(
            "An error occurred. Please try again later."
          );
        },
      });
    } else if (action === "cancel-subscription") {
      $.ajax({
        url: "scripts/data-scripts/user-settings.data.php",
        type: "POST",
        data: { action: "cancel_subscription" },
        dataType: "json",
        success: function (response) {
          console.log("Response:", response);
          if (response.status === "success") {
            $("#result-message").text(
              "Your subscription has been cancelled successfully."
            );
            // Reload the page after a short delay to reflect changes
            setTimeout(function () {
              window.location.reload();
            }, 2000);
          } else {
            $("#result-message").text(
              "Failed to cancel subscription: " + response.message
            );
          }
        },
        error: function (xhr, status, error) {
          console.error("Error:", xhr.responseText);
          $("#result-message").text(
            "An error occurred. Please try again later."
          );
        },
      });
    }
  });
});
