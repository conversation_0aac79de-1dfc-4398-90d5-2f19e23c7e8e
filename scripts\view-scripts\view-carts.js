//import {CartCount} from './check_out-view.js';
$(document).ready(function () {
  const elem = document.querySelector("#carts-items");

  $.get("scripts/data-scripts/view-carts.data.php", function (return_data) {
    var return_data_value = return_data;

    if (return_data_value == "") {
      $("#carts-items").html("You have not added any items to cart");
    }

    return_data_value.reverse();

    for (var i in return_data_value) {
      text = `<section class="row gy-3 mb-4">
                <div class="col-lg-5">
                <div class="me-lg-5">
                  <div class="d-flex">
                    <img src="${`peco_image_store/thumbnail/${return_data_value[i].thumbnail}`}" class="border rounded me-3" style="width: 96px; height: 96px; min-width: 96px;" />
                    <div class="">
                      <a href="photos.php?${
                        return_data_value[i].id
                      }" class="nav-link" image_id="${
        return_data_value[i].id
      }">${return_data_value[i].title}</a>
                      <p class="text-muted">${
                        return_data_value[i].image_resolution
                      }</p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-lg-2 col-sm-6 col-6 d-flex flex-row flex-lg-column flex-xl-row text-nowrap">
                <!-- <div class="">
                  <select style="width: 100px;" class="form-select me-4">
                    <option>Small</option>
                    <option>Large</option>
                    <option>Xtra Large</option>
                  </select>
                </div> -->
                <div class="">
                  <text class="h6">₦${
                    return_data_value[i].image_price
                  }</text> <br />
                </div>
              </div> 
              <div class="col-lg col-sm-6 d-flex justify-content-sm-center justify-content-md-start justify-content-lg-center justify-content-xl-end mb-2">
                <div class="d-flex align-items-center gap-2">
                    

                    <!-- Remove Button -->
                    <a href="#" remove_image_id="${return_data_value[i].id}" 
                        class="btn btn-light remove_item border text-danger icon-hover-danger">
                        Remove
                    </a>
                </div>
            </div>
                    </section>
              <!-- End Cart Item -->`;

      $("#carts-items").append(text);
    }
    // alert(document.querySelector("#carts-items").children.length);

    if (document.querySelector("#carts-items").children.length > 0) {
      $("#make_purchase_btn").removeClass("disabled");
    }

    $("body").on("click", ".remove_item", function (event) {
      event.preventDefault();
      var $wanted = $(this);
      const dataId = $(this).attr("remove_image_id");

      $.post(
        "scripts/data-scripts/view-carts.data.php",
        { dataId: dataId },
        function (return_data) {
          var return_data_value = return_data;

          if ((return_data_value.message = "removed")) {
            $($wanted).closest("section").remove(); // remove the closest section item row
            //CartCount();

            const elem_count = elem.children.length;
            if (elem_count == "0") {
              $("#carts-items").html("You have not added any items to cart");
              $("#make_purchase_btn").addClass("disabled");
            }

            // Trigger cart updated event
            $(document).trigger("cartUpdated");
          } else {
            alert("Please try again");
          }
        }
      );

      $.get(
        "scripts/data-scripts/get-cart-total.data.php",
        function (return_data) {
          var return_data_value = return_data;
          console.log(return_data_value);

          var total_price = return_data_value["image_price"];
          console.log(total_price);
          $("#cart_price").html(total_price);
        }
      );

      //const removedItemId = $('.remove_item').index(this);
      //const searchIndex = return_data_value.findIndex((result) => result.id == dataId);
      //console.log(searchIndex);
      // return_data_value.splice(searchIndex,1);
    });

    $("#promo_redeem_btn").click(function () {
      var promoCode = $("#promo_field").val();

      $("#feedback-text").css("color", "black");
      $("#feedback-text").html("redeeming....");
      $("#feedback-text").show();

      // alert(promoCode);
      $.post(
        "scripts/data-scripts/promo.data.php",
        { promocode: promoCode },
        function (return_data) {
          if (return_data.status == 1) {
            // alert(return_data.status);
            // calculate discount amount
            // console.log('total cart price: ', totalCartPrice);
            // console.log('total discount price: ', return_data['discount_price']);
            var discountAmount =
              Number(totalCartPrice) *
              (Number(return_data["discount_price"]) / 100);
            var newTotalCartPrice = Number(totalCartPrice) - discountAmount;

            $("#total_price").html("N" + newTotalCartPrice + "");

            // do
            $("#feedback-text").hide();

            if (hasPromoAppended == true) {
              $("#checkout-cart-list li:last").remove();
            }

            $("#checkout-cart-list").append(`
                  <li class="list-group-item d-flex justify-content-between bg-light">
                  <div class="text-success">
                      <h6 class="my-0">Promo code</h6>
                      <small>${return_data.promo_code}</small>
                  </div>
                  <span class="text-success">-${discountAmount}</span>
                  </li>
              `);
            hasPromoAppended = true;
          } else {
            // error
            $("#feedback-text").css("color", "red");
            $("#feedback-text").html("invalid promo code");
            $("#feedback-text").show();
          }
        }
      );
    });
  });
});
