<!DOCTYPE html>
<html lang="en">

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;300;400;500;700;900&display=swap" rel="stylesheet">

    <!-- displays site properly based on user's device -->
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Bootstrap core CSS -->
    <link rel="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.1.3/css/bootstrap.min.css">
    <link rel="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.11.2/css/all.min.css">
    <link rel="https://cdnjs.cloudflare.com/ajax/libs/font-awesome-animation/0.2.1/font-awesome-animation.min.css">



    <!-- <link rel="https://stackpath.bootstrapcdn.com/bootstrap/5.0.0-alpha1/css/bootstrap.min.css">
<link rel="https://stackpath.bootstrapcdn.com/bootstrap/5.0.0-alpha1/js/bootstrap.min.js">			
<link rel="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<link rel="Jquery	https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"> -->



    <link href="vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">


    <!-- Additional CSS Files -->
    <link rel="stylesheet" href="assets/css/notification.css">
    <link rel="stylesheet" href="assets/css/fontawesome.css">
    <link rel="stylesheet" href="assets/css/custom.css">
    <link rel="stylesheet" href="assets/css/owl.css">
    <link rel="stylesheet" href="assets/css/animate.css">
    <link rel="stylesheet" href="https://unpkg.com/swiper@7/swiper-bundle.min.css" />
    <!--

TemplateMo 572 Designer

https://templatemo.com/tm-572-designer

-->
</head>

<body>

    <!-- loader section-->
    <?php require_once(__DIR__ . '/resources/loader.php'); ?>

    <header id="#top">

        <!-- navbar section-->
        <?php require_once(__DIR__ . '/resources/navbar.php'); ?>

    </header>

    <div class="page-banner change-name">
        <div class="container">
            <div class="row">
                <div class="col-lg-6 offset-lg-3">
                    <div class="header-text">
                        <h2>Collect<em>ions</em> (5)</h2>
                        <!-- <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod keoi tempor incididunt ut labore et dolore magna aliqua.</p> -->
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- only for preview -->


    <!-- call_to_signup section-->
    <!-- <?php require_once(__DIR__ . '/resources/collectme.php'); ?> -->
    <?php require_once(__DIR__ . '/resources/collections.php'); ?>


    <!-- call_to_signup section-->
    <?php require_once(__DIR__ . '/resources/call_to_signup.php'); ?>


    <!-- footer section-->
    <?php require_once(__DIR__ . '/resources/footer.php'); ?>


    <!-- Scripts -->
    <!-- Bootstrap core JavaScript -->
    <script src="vendor/jquery/jquery.min.js"></script>
    <script src="vendor/bootstrap/js/popper.min.js"></script>
    <script src="vendor/bootstrap/js/bootstrap.min.js"></script>

    <script src="vendor/swiper/swiper-bundle.min.js"></script>
    <script src="vendor/glightbox/js/glightbox.min.js"></script>
    <script src="vendor/aos/aos.js"></script>
    <script src="assets/js/isotope.min.js"></script>
    <script src="assets/js/owl-carousel.js"></script>
    <script src="assets/js/mo.js"></script>
    <script src="assets/js/typed.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/5.0.0-alpha1/js/bootstrap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <script src="https://js.paystack.co/v1/inline.js"></script>


    <script src="assets/js/tabs.js"></script>
    <script src="assets/js/popup.js"></script>
    <script src="assets/js/custom.js"></script>
    <script src="https://unpkg.com/isotope-layout@3/dist/isotope.pkgd.min.js"></script>
    <script src="https://unpkg.com/imagesloaded@4/imagesloaded.pkgd.js"></script>
    <script src="assets/js/custom.js"></script>
    <script src="assets/js/checkout.js"></script>
    <script src="resources/make_payment.js"></script>
    <script src="scripts/view-scripts/check_out-view.js"></script>
    <script src="scripts/view-scripts/fetch-payment-cards-modal.js"></script>
    <!-- <script src="scripts/view-scripts/view-carts.js"></script> -->

    <?php require_once(__DIR__ . '/resources/utility_scripts.php'); ?>
    <script>
        $(document).ready(function() {
            set_interval();
        });
        $('body').click(function() {
            reset_interval();
        });
        $('body').mousemove(function() {
            reset_interval();
        });
        $('body').keypress(function() {
            reset_interval();
        });

        // setTimeout(function() {
        //     $('.loader').fadeToggle();
        // }, 1500);

        $("a[href='#top']").click(function() {
            $("html, body").animate({
                scrollTop: 0
            }, "slow");
            return false;
        });

        let logout_link = document.getElementsByClassName("logged-out");
        let login_link = document.getElementsByClassName("logged-in");

        $.post("scripts/data-scripts/auth-status.data.php", {
            request_type: 'auth-session-check'
        }, function(data) {
            if (data["message"] == "success") {
                // authed
                $(logout_link).addClass("hide");
                $(login_link).removeClass("hide");

                $("#authenticated-username").html(data["username"]);
            } else {
                // not authed
                $(logout_link).removeClass("hide");
                $(login_link).addClass("hide");
            }
        });

        $.ajax({
            type: 'get',
            url: 'scripts/data-scripts/user_profile.data.php',
            data: {
                user_id: 0
            },
            dataType: 'json',
            success: function(result) {
                if (result[0].avatar == "") {
                    return;
                }
                $("#auth-imgg").attr("src", `assets/images/${result[0].avatar}`);
                $("#auth-img").attr("src", `assets/images/${result[0].avatar} `);
            }
        });



        $("#signout-btn").click(function() {
            logout_call();
        });



        function set_interval() {
            timer = setInterval(() => {
                auto_logout();
            }, 3600000);
        }

        function reset_interval() {
            clearInterval(timer);
            timer = setInterval(() => {
                auto_logout();
            }, 3600000);
        }

        function auto_logout() {
            logout_call();
        }

        function logout_call() {
            console.log("auto logout called");
            $.post("scripts/data-scripts/auth-status.data.php", {
                request_type: 'auth-session-end'
            }, function(data) {
                if (data["message"] == "success") {
                    // signed out
                    window.location.href = "signup.php?login";
                }
            });
        }
    </script>

    <script>
        setTimeout(function() {
            $('.loader').fadeToggle();
        }, 1500);

        $("a[href='#top']").click(function() {
            $("html, body").animate({
                scrollTop: 0
            }, "slow");
            return false;
        });
    </script>

</body>

</html>