<?php
// session_start();
// include 'resources/db_config/db_connection.php';

// echo "<h2>Purchase Flow Test</h2>";

// // Check if user is logged in
// if (!isset($_SESSION['id'])) {
//     echo "<p>❌ User not logged in. Please login first.</p>";
//     echo "<a href='signup.php?login'>Login</a>";
//     exit;
// }

// $user_id = $_SESSION['id'];
// echo "<p>✅ User logged in with ID: $user_id</p>";

// // Check cart
// if (!isset($_SESSION['cart']) || empty($_SESSION['cart'])) {
//     echo "<p>❌ Cart is empty. Please add items to cart first.</p>";
//     echo "<a href='explore.php'>Browse Images</a>";
// } else {
//     echo "<p>✅ Cart has " . count($_SESSION['cart']) . " items</p>";

//     echo "<h3>Cart Contents:</h3>";
//     echo "<table border='1' style='border-collapse: collapse;'>";
//     echo "<tr><th>ID</th><th>Title</th><th>Price</th></tr>";

//     $total = 0;
//     foreach ($_SESSION['cart'] as $item) {
//         echo "<tr>";
//         echo "<td>" . $item['id'] . "</td>";
//         echo "<td>" . $item['title'] . "</td>";
//         echo "<td>₦" . $item['image_price'] . "</td>";
//         echo "</tr>";
//         $total += $item['image_price'];
//     }
//     echo "</table>";
//     echo "<p><strong>Total: ₦$total</strong></p>";
// }

// // Check purchase history
// echo "<h3>Current Purchase History:</h3>";

// // Try both table names
// $tables_to_check = ['puchase_history', 'purchase_history'];
// $found_purchases = false;

// foreach ($tables_to_check as $table_name) {
//     $result = $db->query("SHOW TABLES LIKE '$table_name'");
//     if ($result->num_rows > 0) {
//         echo "<h4>From table: $table_name</h4>";

//         $purchases = $db->query("SELECT * FROM $table_name WHERE user_id = $user_id ORDER BY Date_created DESC LIMIT 10");
//         if ($purchases && $purchases->num_rows > 0) {
//             echo "<table border='1' style='border-collapse: collapse;'>";

//             // Get column names
//             $fields = $purchases->fetch_fields();
//             echo "<tr>";
//             foreach ($fields as $field) {
//                 echo "<th>" . $field->name . "</th>";
//             }
//             echo "</tr>";

//             // Reset result pointer and show data
//             $purchases->data_seek(0);
//             while ($row = $purchases->fetch_assoc()) {
//                 echo "<tr>";
//                 foreach ($row as $value) {
//                     echo "<td>" . htmlspecialchars($value) . "</td>";
//                 }
//                 echo "</tr>";
//             }
//             echo "</table>";
//             $found_purchases = true;
//         } else {
//             echo "<p>No purchases found in $table_name for user $user_id</p>";
//         }
//     }
// }

// if (!$found_purchases) {
//     echo "<p>❌ No purchase history found</p>";
// }

// // Test manual insert
// if (isset($_POST['test_insert'])) {
//     echo "<h3>Testing Manual Insert:</h3>";

//     $test_sql = "INSERT INTO puchase_history (user_id, img_id, amount, payment_ref, Date_created, payment_status) 
//                  VALUES ($user_id, 999, 100, 'TEST_REF_" . time() . "', NOW(), 1)";

//     if ($db->query($test_sql)) {
//         echo "<p>✅ Test insert successful</p>";
//     } else {
//         echo "<p>❌ Test insert failed: " . $db->error . "</p>";
//     }
// }

// // Check error logs
// echo "<h3>Recent Error Logs:</h3>";
// $log_file = ini_get('error_log');
// if ($log_file && file_exists($log_file)) {
//     $logs = file_get_contents($log_file);
//     $recent_logs = array_slice(explode("\n", $logs), -20);
//     echo "<pre style='background: #f0f0f0; padding: 10px; max-height: 300px; overflow-y: scroll;'>";
//     foreach ($recent_logs as $log) {
//         if (strpos($log, 'Cart payment') !== false || strpos($log, 'purchase') !== false) {
//             echo htmlspecialchars($log) . "\n";
//         }
//     }
//     echo "</pre>";
// } else {
//     echo "<p>Error log not accessible</p>";
// }

?>

<!-- // <form method="post">
//     <button type="submit" name="test_insert">Test Manual Insert</button>
// </form>

// <hr>
// <p><a href="cart.php">Go to Cart</a> | <a href="account-settings.php">Account Settings</a></p> -->

session_start();
include 'resources/db_config/db_connection.php';

echo "<h2>Purchase Flow Test</h2>";

// Check if user is logged in
if (!isset($_SESSION['id'])) {
echo "<p>❌ User not logged in. Please login first.</p>";
echo "<a href='signup.php?login'>Login</a>";
exit;
}

$user_id = $_SESSION['id'];
echo "<p>✅ User logged in with ID: $user_id</p>";

// Check cart
if (!isset($_SESSION['cart']) || empty($_SESSION['cart'])) {
echo "<p>❌ Cart is empty. Please add items to cart first.</p>";
echo "<a href='explore.php'>Browse Images</a>";
} else {
echo "<p>✅ Cart has " . count($_SESSION['cart']) . " items</p>";

echo "<h3>Cart Contents:</h3>";
echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr>
        <th>ID</th>
        <th>Title</th>
        <th>Price</th>
    </tr>";

    $total = 0;
    foreach ($_SESSION['cart'] as $item) {
    echo "<tr>";
        echo "<td>" . $item['id'] . "</td>";
        echo "<td>" . $item['title'] . "</td>";
        echo "<td>₦" . $item['image_price'] . "</td>";
        echo "</tr>";
    $total += $item['image_price'];
    }
    echo "</table>";
echo "<p><strong>Total: ₦$total</strong></p>";
}

// Check purchase history
echo "<h3>Current Purchase History:</h3>";

// Try both table names
$tables_to_check = ['puchase_history', 'purchase_history'];
$found_purchases = false;

foreach ($tables_to_check as $table_name) {
$result = $db->query("SHOW TABLES LIKE '$table_name'");
if ($result->num_rows > 0) {
echo "<h4>From table: $table_name</h4>";

$purchases = $db->query("SELECT * FROM $table_name WHERE user_id = $user_id ORDER BY Date_created DESC LIMIT 10");
if ($purchases && $purchases->num_rows > 0) {
echo "<table border='1' style='border-collapse: collapse;'>";

    // Get column names
    $fields = $purchases->fetch_fields();
    echo "<tr>";
        foreach ($fields as $field) {
        echo "<th>" . $field->name . "</th>";
        }
        echo "</tr>";

    // Reset result pointer and show data
    $purchases->data_seek(0);
    while ($row = $purchases->fetch_assoc()) {
    echo "<tr>";
        foreach ($row as $value) {
        echo "<td>" . htmlspecialchars($value) . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
$found_purchases = true;
} else {
echo "<p>No purchases found in $table_name for user $user_id</p>";
}
}
}

if (!$found_purchases) {
echo "<p>❌ No purchase history found</p>";
}

// Test manual insert
if (isset($_POST['test_insert'])) {
echo "<h3>Testing Manual Insert:</h3>";

$test_sql = "INSERT INTO puchase_history (user_id, img_id, amount, payment_ref, Date_created, payment_status)
VALUES ($user_id, 999, 100, 'TEST_REF_" . time() . "', NOW(), 1)";

if ($db->query($test_sql)) {
echo "<p>✅ Test insert successful</p>";
} else {
echo "<p>❌ Test insert failed: " . $db->error . "</p>";
}
}

// Check error logs
echo "<h3>Recent Error Logs:</h3>";
$log_file = ini_get('error_log');
if ($log_file && file_exists($log_file)) {
$logs = file_get_contents($log_file);
$recent_logs = array_slice(explode("\n", $logs), -20);
echo "
<pre style='background: #f0f0f0; padding: 10px; max-height: 300px; overflow-y: scroll;'>";
    foreach ($recent_logs as $log) {
        if (strpos($log, 'Cart payment') !== false || strpos($log, 'purchase') !== false) {
            echo htmlspecialchars($log) . "\n";
        }
    }
    echo "</pre>";
} else {
echo "<p>Error log not accessible</p>";
}

?>

<form method="post">
    <button type="submit" name="test_insert">Test Manual Insert</button>
</form>

<hr>
<p><a href="cart.php">Go to Cart</a> | <a href="account-settings.php">Account Settings</a></p>