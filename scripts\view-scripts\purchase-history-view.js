$(document).ready(function () {
  //Run Codes When Document Loads Completely

  let rowsOffsetTrack = [];
  let initApiCallMade = false;
  let activePageIndex = 1;
  let searchKeyword = "";
  let startDateFilter = "";
  let endDateFilter = "";

  callPurchaseHistoriesApi(0, "", "", "");

  function callPurchaseHistoriesApi(
    offset,
    searchphrase,
    startdatefilter,
    enddatefilter
  ) {
    // ! show loader Icon
    enableLoader();

    const payload = {
      sql_offset: offset,
      search_phrase: searchphrase,
      start_date: startdatefilter,
      end_date: enddatefilter,
    };

    $.post(
      "scripts/data-scripts/purchase-history.data.php",
      payload,
      function (return_data) {
        // assign Callback Function Return Data to Variable for Usability
        let return_data_value = return_data;

        if (return_data_value["message"] != "error") {
          if (return_data_value["message"] != "no result") {
            $("#no-results-msg").css("display", "none");
            $("#purchase-histories-table").css("display", "block");
            $("#table-pagination").css("display", "block");

            createHistoryViewByLoop(return_data_value["purchase_histories"]);

            if (initApiCallMade == false) {
              setupPaginationControls(
                return_data_value["pagination_configurations"]["row_set"],
                return_data_value["pagination_configurations"]["page_divisions"]
              );
              initApiCallMade = true;
            }
          } else {
            // ! no results available
            $("#no-results-msg").css("display", "block");
            $("#purchase-histories-table").css("display", "none");
            $("#table-pagination").css("display", "none");
          }
        } else {
          // ! server error
        }

        disableLoader();
      }
    );
  }

  //   function createHistoryViewByLoop(data) {
  //     let historyItem = "";

  //     data.forEach((element) => {
  //       // <tr class="active-row"> // ! to highlight a row
  //       historyItem += `<tr>
  //                 <td>${element["image_name"]}</td>
  //                 <td>${element["image_id"]}</td>
  //                 <td>${element["image_tags"]}</td>
  //                 <td>${element["image_amount"]}</td>
  //             </tr>`;
  //     });

  //     $("#purchase-histories-view").html(historyItem);
  //   }

  function createHistoryViewByLoop(data) {
    let historyItem = "";

    data.forEach((element) => {
      // Determine badge class based on status
      let statusClass = "badge bg-success";
      if (element["status"] === "Failed" || element["status"] === "Expired") {
        statusClass = "badge bg-danger";
      } else if (element["status"] === "Pending") {
        statusClass = "badge bg-warning text-dark";
      }

      historyItem += `<tr>
            <td>${element["purchase_type"]}</td>
            <td>${element["item_name"]}</td>
            <td>${element["item_id"]}</td>
            <td>₦${element["amount"]}</td>
            <td>${element["purchase_date"]}</td>
            <td><span class="${statusClass}">${element["status"]}</span></td>
            <td>${
              element["purchase_type"] === "Subscription"
                ? element["expiry_date"]
                : "N/A"
            }</td>
        </tr>`;
    });

    $("#purchase-histories-view").html(historyItem);
  }

  function setupPaginationControls(rowsets, pagedivisions) {
    let paginationElement = "";

    rowsOffsetTrack.push(null);
    paginationElement += `<li class="page-item"><a class="page-link" style="cursor: pointer;">Previous</a></li>`;

    for (let i = 0; i < pagedivisions; i++) {
      rowsOffsetTrack.push(rowsets * i);
      paginationElement += `<li class="page-item"><a class="page-link" style="cursor: pointer;">${
        i + 1
      }</a></li>`;
    }

    rowsOffsetTrack.push(null);
    paginationElement += `<li class="page-item"><a class="page-link" style="cursor: pointer;">Next</a></li>`;

    $("#table-pagination").html(paginationElement);

    // console.log(rowsOffsetTrack);

    // ---- // ---- add event handlers for each dynamically generated elements
    const linkElements = document.querySelectorAll("#table-pagination li");

    linkElements.forEach((li, index) => {
      li.addEventListener("click", function () {
        if (this.textContent == "Previous") {
          if (activePageIndex - 1 >= 1) {
            callPurchaseHistoriesApi(
              rowsOffsetTrack[activePageIndex - 1],
              searchKeyword,
              startDateFilter,
              endDateFilter
            );
            console.log(activePageIndex - 1);
            activePageIndex = activePageIndex - 1;
          } else {
            // alert('no more previous results');
          }
        } else if (this.textContent == "Next") {
          if (activePageIndex + 1 <= pagedivisions) {
            callPurchaseHistoriesApi(
              rowsOffsetTrack[activePageIndex + 1],
              searchKeyword,
              startDateFilter,
              endDateFilter
            );
            console.log(activePageIndex + 1);
            activePageIndex = activePageIndex + 1;
          } else {
            // alert('no more result');
          }
        } else {
          callPurchaseHistoriesApi(
            rowsOffsetTrack[index],
            searchKeyword,
            startDateFilter,
            endDateFilter
          );
          activePageIndex = index;
        }
      });
    });
  }

  $("#filter-btn").on("click", function () {
    initApiCallMade = false;
    searchKeyword = $("#search-filter").val();
    startDateFilter = $("#starting-date-filter").val();
    endDateFilter = $("#ending-date-filter").val();
    console.log(startDateFilter, endDateFilter);
    callPurchaseHistoriesApi(0, searchKeyword, startDateFilter, endDateFilter);
  });

  $("#clear-search").on("click", function (e) {
    e.preventDefault();

    clearSearch();
  });

  // Reusable view functions
  function disableLoader() {
    $("#purchase-history-loader").css("display", "none");
  }
  function enableLoader() {
    $("#purchase-history-loader").css("display", "block");
  }
  function clearSearch() {
    $("#no-results-msg").css("display", "none");
    $("#purchase-histories-table").css("display", "block");
    $("#table-pagination").css("display", "block");
    $("#search-filter").val("");
  }
});
