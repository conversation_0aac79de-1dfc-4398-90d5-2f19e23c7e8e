// $(document).ready(function () {
//   var planidsKeep = [];

//   $.post(
//     "scripts/data-scripts/plans.data.php",
//     { request_type: "get-plans" },
//     function (return_data) {
//       // assign Callback Function Return Data to Variable for Usability
//       var return_data_value = return_data;

//       console.log(return_data_value);

//       if (return_data_value["message"] != "error") {
//         if (return_data_value["message"] != "no plans") {
//           // ! hide collections view
//           // disableLoader();
//           viewPlansItemsByLoop(
//             return_data_value["plans"],
//             return_data_value["user_id"],
//             return_data_value["email"]
//           );
//         } else {
//           // ! no collections available
//           // disableLoader();
//         }
//       } else {
//         // ! server error
//         // disableLoader();
//       }
//     }
//   );

//   //   function viewPlansItemsByLoop(data, userid, email) {
//   //     var Plans = "";

//   //     data.forEach((element) => {
//   //       planidsKeep.push(element["plan_id"]);
//   //       var user_id = element[""];
//   //       var desc = element["plan_value"].split("\\n");
//   //       var lis = "";
//   //       // alert(desc[0]);
//   //       for (var i = 0; i < desc.length; i++) {
//   //         lis += `<li>${desc[i]}</li>`;
//   //       }

//   //       let isSubscribed = element["is_subscribed"];

//   //       Plans += `<li class="pricingTable-firstTable_table">
//   //                 <h1 class="pricingTable-firstTable_table__header">${
//   //                   element["plan_name"]
//   //                 }</h1>
//   //                 <p class="pricingTable-firstTable_table__pricing"><span>N</span><span>${
//   //                   element["plan_amount"]
//   //                 }</span><span>/ ${element["plan_duration"]}(s)</span></p>
//   //                 <ul class="pricingTable-firstTable_table__options">
//   //                     ${lis}
//   //                 </ul>

//   //                 ${
//   //                   user_id == 0
//   //                     ? '<a href="signup.php?login" class="pricingTable-firstTable_table__getstart">Get Started Now</a>'
//   //                     : `<button class="pricingTable-firstTable_table__getstart plan-btn ${
//   //                         isSubscribed ? "disabled-btn" : ""
//   //                       }"
//   //                     data-plan-id="${element["plan_id"]}"
//   //                     data-plan-amount="${element["plan_amount"]}"
//   //                     data-plan-duration="${element["plan_duration"]}"
//   //                     ${isSubscribed ? "disabled" : ""}>
//   //                     ${isSubscribed ? "Subscribed" : "Get Started Now"}
//   //                 </button>`
//   //                 }

//   //             </li>`;

//   //       // Plans += `<div class="item" onclick="window.location.href = 'explore.php?category=${element['id']}';">
//   //       //                         <div class="thumb">
//   //       //                         <div class="overlay">
//   //       //                             <img src="peco_image_store/categories/${element['thumbnail']}" alt="">
//   //       //                         </div>
//   //       //                         <h4>${element['name']}</h4>
//   //       //                         </div>
//   //       //                     </div>`;
//   //     });

//   //     let elementsArray = document.querySelectorAll(".item");

//   //     elementsArray.forEach(function (elem, index) {
//   //       elem.addEventListener("click", function () {
//   //         window.location.href = "explore.php?category=" + planidsKeep[index];
//   //       });
//   //     });

//   //     $("#Plansloop").html(Plans);

//   //     // ! each plans buttons iteration
//   //     let btnList = document.querySelectorAll("#Plansloop button");

//   //     btnList.forEach(function (elem, index) {
//   //       elem.addEventListener("click", function () {
//   //         const planId = elem.getAttribute("data-plan-id");
//   //         const planAmount = elem.getAttribute("data-plan-amount");
//   //         const planDuration = elem.getAttribute("data-plan-duration");

//   //         // Inject data into the modal
//   //         document.getElementById("modalPlanId").textContent = planId;
//   //         document.getElementById("modalPlanAmount").textContent = planAmount;
//   //         document.getElementById("modalPlanDuration").textContent = planDuration;
//   //         document.getElementById("user_id1").textContent = userid;
//   //         document.getElementById("email1").textContent = email;

//   //         // Check if the user is authenticated before proceeding
//   //         $.post(
//   //           "scripts/data-scripts/auth-status.data.php",
//   //           { request_type: "auth-session-check" },
//   //           function (return_data) {
//   //             if (
//   //               return_data["message"] !== "error" &&
//   //               return_data["authenticated"] === true
//   //             ) {
//   //               // Check if the user has an active subscription
//   //               $.post(
//   //                 "scripts/data-scripts/check-subscription.php",
//   //                 { user_id: userid },
//   //                 function (subscription_data) {
//   //                   if (subscription_data["has_subscription"] === true) {
//   //                     // Show warning modal
//   //                     $("#subscriptionConfirmModal").modal("show");
//   //                   } else {
//   //                     // Proceed with payment
//   //                     var monthHolder;

//   //                     if (data[index]["plan_duration"] === "3 month") {
//   //                       monthHolder = 3;
//   //                     } else if (data[index]["plan_duration"] === "6 month") {
//   //                       monthHolder = 6;
//   //                     } else if (data[index]["plan_duration"] === "1 year") {
//   //                       monthHolder = 12;
//   //                     }

//   //                     // Call payment function
//   //                     plan_payment(
//   //                       data[index]["plan_amount"],
//   //                       data[index]["plan_id"],
//   //                       userid,
//   //                       email,
//   //                       monthHolder,
//   //                       elem
//   //                     );
//   //                   }
//   //                 },
//   //                 "json"
//   //               );
//   //             }
//   //           },
//   //           "json"
//   //         );
//   //       });
//   //     });

//   //     // console.log(collectionItem);
//   //     $("document").ready(function () {
//   //       console.log("Hey, got here !");
//   //       // let carousel = $('#fetched-categories');
//   //     });
//   //   }

//   //function confirm change plan

//   //   function viewPlansItemsByLoop(data, userid, email) {
//   //     var Plans = "";

//   //     data.forEach((element) => {
//   //       planidsKeep.push(element["plan_id"]);
//   //       var user_id = element[""];
//   //       var desc = element["plan_value"].split("\\n");
//   //       var lis = "";

//   //       for (var i = 0; i < desc.length; i++) {
//   //         lis += `<li>${desc[i]}</li>`;
//   //       }

//   //       let isSubscribed = element["is_subscribed"];
//   //       let wasSubscribed = element["was_subscribed"];

//   //       // Determine button text and class based on subscription status
//   //       let buttonText = "Get Started Now";
//   //       let buttonClass = "";

//   //       if (isSubscribed) {
//   //         buttonText = "Subscribed";
//   //         buttonClass = "disabled-btn";
//   //       } else if (wasSubscribed) {
//   //         buttonText = "Subscribe Again";
//   //         buttonClass = "";
//   //       }

//   //       Plans += `<li class="pricingTable-firstTable_table">
//   //               <h1 class="pricingTable-firstTable_table__header">${
//   //                 element["plan_name"]
//   //               }</h1>
//   //               <p class="pricingTable-firstTable_table__pricing"><span>N</span><span>${
//   //                 element["plan_amount"]
//   //               }</span><span>/ ${element["plan_duration"]}(s)</span></p>
//   //               <ul class="pricingTable-firstTable_table__options">
//   //                   ${lis}
//   //               </ul>

//   //               ${
//   //                 user_id == 0
//   //                   ? '<a href="signup.php?login" class="pricingTable-firstTable_table__getstart">Get Started Now</a>'
//   //                   : `<button class="pricingTable-firstTable_table__getstart plan-btn ${buttonClass}"
//   //                   data-plan-id="${element["plan_id"]}"
//   //                   data-plan-amount="${element["plan_amount"]}"
//   //                   data-plan-duration="${element["plan_duration"]}"
//   //                   ${isSubscribed ? "disabled" : ""}>
//   //                   ${buttonText}
//   //               </button>`
//   //               }

//   //           </li>`;
//   //     });

//   //     $("#Plansloop").html(Plans);

//   //     // Set up event handlers for plan buttons
//   //     $(".plan-btn").on("click", function () {
//   //       const planId = $(this).data("plan-id");
//   //       const planAmount = $(this).data("plan-amount");
//   //       const planDuration = $(this).data("plan-duration");

//   //       // Check if the user is authenticated before proceeding
//   //       $.post(
//   //         "scripts/data-scripts/auth-status.data.php",
//   //         { request_type: "auth-session-check" },
//   //         function (return_data) {
//   //           if (
//   //             return_data["message"] !== "error" &&
//   //             return_data["authenticated"] === true
//   //           ) {
//   //             // Check if the user has an active subscription
//   //             $.post(
//   //               "scripts/data-scripts/check-subscription.php",
//   //               { user_id: userid },
//   //               function (subscription_data) {
//   //                 if (subscription_data["has_subscription"] === true) {
//   //                   // Show warning modal
//   //                   $("#subscriptionConfirmModal").modal("show");

//   //                   // Store plan details in modal for reference
//   //                   $("#modalPlanId").text(planId);
//   //                   $("#modalPlanAmount").text(planAmount);
//   //                   $("#modalPlanDuration").text(planDuration);
//   //                   $("#user_id1").text(userid);
//   //                   $("#email1").text(email);
//   //                 } else {
//   //                   // Proceed with subscription
//   //                   plan_payment(planAmount, planId, userid, email);
//   //                 }
//   //               },
//   //               "json"
//   //             );
//   //           } else {
//   //             // Redirect to login
//   //             window.location.href = "signup.php?login";
//   //           }
//   //         },
//   //         "json"
//   //       );
//   //     });
//   //   }

//   // Handle subscription confirmation

//   function viewPlansItemsByLoop(data, userid, email) {
//     var Plans = "";
//     var activePlanId = data.active_plan_id;
//     var plansData = data.plans;

//     plansData.forEach((element) => {
//       planidsKeep.push(element["plan_id"]);
//       var desc = element["plan_value"].split("\\n");
//       var lis = "";

//       for (var i = 0; i < desc.length; i++) {
//         lis += `<li>${desc[i]}</li>`;
//       }

//       // Check if this is the active plan
//       let isSubscribed = element["is_subscribed"];
//       let wasSubscribed = element["was_subscribed"];

//       // Determine button text and class based on subscription status
//       let buttonText = "Get Started Now";
//       let buttonClass = "";
//       let isDisabled = false;

//       if (isSubscribed) {
//         buttonText = "Subscribed";
//         buttonClass = "disabled-btn";
//         isDisabled = true;
//       } else if (wasSubscribed) {
//         buttonText = "Subscribe Again";
//         buttonClass = "";
//       }

//       Plans += `<li class="pricingTable-firstTable_table ${
//         isSubscribed ? "active-plan" : ""
//       }">
//               ${
//                 isSubscribed
//                   ? '<div class="current-plan-badge">Current Plan</div>'
//                   : ""
//               }
//               <h1 class="pricingTable-firstTable_table__header">${
//                 element["plan_name"]
//               }</h1>
//               <p class="pricingTable-firstTable_table__pricing"><span>₦</span><span>${
//                 element["plan_amount"]
//               }</span><span>/ ${element["plan_duration"]}</span></p>
//               <ul class="pricingTable-firstTable_table__options">
//                   ${lis}
//               </ul>

//               ${
//                 userid == 0
//                   ? '<a href="signup.php?login" class="pricingTable-firstTable_table__getstart">Get Started Now</a>'
//                   : `<button class="pricingTable-firstTable_table__getstart plan-btn ${buttonClass}"
//                   data-plan-id="${element["plan_id"]}"
//                   data-plan-amount="${element["plan_amount"]}"
//                   data-plan-duration="${element["plan_duration"]}"
//                   ${isDisabled ? "disabled" : ""}>
//                   ${buttonText}
//               </button>`
//               }

//           </li>`;
//     });

//     $("#Plansloop").html(Plans);

//     // Set up event handlers for plan buttons
//     $(".plan-btn").on("click", function () {
//       if ($(this).hasClass("disabled-btn")) {
//         return; // Don't do anything if the button is disabled
//       }

//       const planId = $(this).data("plan-id");
//       const planAmount = $(this).data("plan-amount");
//       const planDuration = $(this).data("plan-duration");

//       // Check if the user is authenticated before proceeding
//       $.post(
//         "scripts/data-scripts/auth-status.data.php",
//         { request_type: "auth-session-check" },
//         function (return_data) {
//           if (
//             return_data["message"] !== "error" &&
//             return_data["authenticated"] === true
//           ) {
//             // Check if the user has an active subscription
//             $.post(
//               "scripts/data-scripts/check-subscription.php",
//               { user_id: userid },
//               function (subscription_data) {
//                 if (subscription_data["has_subscription"] === true) {
//                   // Show warning modal
//                   $("#subscriptionConfirmModal").modal("show");

//                   // Store plan details in modal for reference
//                   $("#modalPlanId").text(planId);
//                   $("#modalPlanAmount").text(planAmount);
//                   $("#modalPlanDuration").text(planDuration);
//                   $("#user_id1").text(userid);
//                   $("#email1").text(email);
//                 } else {
//                   // Proceed with subscription
//                   plan_payment(planAmount, planId, userid, email);
//                 }
//               },
//               "json"
//             );
//           } else {
//             // Redirect to login
//             window.location.href = "signup.php?login";
//           }
//         },
//         "json"
//       );
//     });
//   }
//   $("#confirmSubscriptionChange").on("click", function () {
//     // Get plan details from the modal
//     const planId = $("#modalPlanId").text();
//     const planAmount = $("#modalPlanAmount").text();
//     const userId = $("#user_id1").text();
//     const email = $("#email1").text();

//     // Close the modal
//     $("#subscriptionConfirmModal").modal("hide");

//     // Proceed with the subscription change
//     plan_payment(planAmount, planId, userId, email);
//   });

//   $(document).on("click", "#agreeBtn", function () {
//     let plan_id = $("#modalPlanId").text();
//     console.log("plan_id", plan_id);
//     let plan_amount = $("#modalPlanAmount").text();
//     let duration = $("#modalPlanDuration").text();
//     let user_id = $("#user_id1").text();
//     console.log(user_id);
//     let email = $("#email1").text();

//     let monthHolder;

//     if (duration === "3 month") {
//       monthHolder = 3;
//     } else if (duration === "6 month") {
//       monthHolder = 6;
//     } else if (duration === "1 year") {
//       monthHolder = 12;
//     }

//     plan_payment2(plan_amount, plan_id, user_id, email, monthHolder);
//     //remove the modal
//     $("#subscriptionConfirmModal").modal("hide");
//   });

//   // function payWithPaystack() {
//   //     var handler = PaystackPop.setup({
//   //         key: 'YOUR_PUBLIC_KEY', // Replace with your public key
//   //         email: "<EMAIL>",
//   //         amount: 100 * 100, // the amount value is multiplied by 100 to convert to the lowest currency unit
//   //         currency: 'NGN', // Use GHS for Ghana Cedis or USD for US Dollars
//   //         ref: 'YOUR_REFERENCE', // Replace with a reference you generated
//   //         callback: function(response) {
//   //         //this happens after the payment is completed successfully
//   //         var reference = response.reference;
//   //         alert('Payment complete! Reference: ' + reference);
//   //         // Make an AJAX call to your server with the reference to verify the transaction
//   //         },
//   //         onClose: function() {
//   //         alert('Transaction was not completed, window closed.');
//   //         },
//   //     });
//   //     handler.openIframe();
//   // }

//   // Add this function to check subscription status after subscription
//   function verifySubscription() {
//     $.ajax({
//       url: "scripts/data-scripts/user-settings.data.php",
//       type: "POST",
//       data: { action: "check_subscription" },
//       dataType: "json",
//       success: function (response) {
//         console.log("Subscription verification:", response);

//         // If subscription was successful, show a success message
//         if (
//           response.status === "success" &&
//           response.details &&
//           response.details.active_plans_count &&
//           response.details.active_plans_count > 0
//         ) {
//           alert(
//             "Subscription successful! You now have an active subscription."
//           );
//         }
//       },
//       error: function (xhr, status, error) {
//         console.error("Error verifying subscription:", xhr.responseText);
//       },
//     });
//   }

//   // Call this function after successful subscription
// });

$(document).ready(function () {
  var planidsKeep = [];

  // Add console logging to debug
  console.log("Plans view script loaded");

  $.post(
    "scripts/data-scripts/plans.data.php",
    { request_type: "get-plans" },
    function (return_data) {
      // Debug logging
      console.log("Plans data received:", return_data);

      // assign Callback Function Return Data to Variable for Usability
      var return_data_value = return_data;

      if (return_data_value["message"] != "error") {
        if (
          return_data_value["plans"] &&
          return_data_value["plans"].length > 0
        ) {
          // ! hide collections view
          // disableLoader();
          viewPlansItemsByLoop(
            return_data_value,
            return_data_value["user_id"],
            return_data_value["email"]
          );
        } else {
          // ! no collections available
          console.log("No plans available");
          $("#Plansloop").html(
            "<p>No subscription plans available at this time.</p>"
          );
          // disableLoader();
        }
      } else {
        // ! server error
        console.error("Server error when fetching plans");
        $("#Plansloop").html(
          "<p>Error loading subscription plans. Please try again later.</p>"
        );
        // disableLoader();
      }
    }
  ).fail(function (xhr, status, error) {
    console.error("AJAX error:", status, error);
    $("#Plansloop").html(
      "<p>Error connecting to server. Please try again later.</p>"
    );
  });

  function viewPlansItemsByLoop(data, userid, email) {
    var Plans = "";
    var activePlanId = data.active_plan_id;
    var plansData = data.plans;

    console.log("Rendering plans:", plansData);
    console.log("Active plan ID:", activePlanId);
    console.log("User ID:", userid);

    plansData.forEach((element) => {
      planidsKeep.push(element["plan_id"]);
      var desc = element["plan_value"].split("\\n");
      var lis = "";

      for (var i = 0; i < desc.length; i++) {
        lis += `<li>${desc[i]}</li>`;
      }

      // Check if this is the active plan
      let isSubscribed = element["is_subscribed"];
      let wasSubscribed = element["was_subscribed"];

      // Determine button text and class based on subscription status
      let buttonText = "Get Started Now";
      let buttonClass = "";
      let isDisabled = false;

      if (isSubscribed) {
        buttonText = "Subscribed";
        buttonClass = "disabled-btn";
        isDisabled = true;
      } else if (wasSubscribed) {
        buttonText = "Subscribe Again";
        buttonClass = "";
      }

      Plans += `<li class="pricingTable-firstTable_table ${
        isSubscribed ? "active-plan" : ""
      }">
              ${
                isSubscribed
                  ? '<div class="current-plan-badge">Current Plan</div>'
                  : ""
              }
              <h1 class="pricingTable-firstTable_table__header">${
                element["plan_name"]
              }</h1>
              <p class="pricingTable-firstTable_table__pricing"><span>₦</span><span>${
                element["plan_amount"]
              }</span><span>/ ${element["plan_duration"]}</span></p>
              <ul class="pricingTable-firstTable_table__options">
                  ${lis}
              </ul>
              
              ${
                userid == 0
                  ? '<a href="signup.php?login" class="pricingTable-firstTable_table__getstart">Get Started Now</a>'
                  : `<button class="pricingTable-firstTable_table__getstart plan-btn ${buttonClass}" 
                  data-plan-id="${element["plan_id"]}" 
                  data-plan-amount="${element["plan_amount"]}" 
                  data-plan-duration="${element["plan_duration"]}"
                  ${isDisabled ? "disabled" : ""}>
                  ${buttonText}
              </button>`
              }

          </li>`;
    });

    $("#Plansloop").html(Plans);
    console.log("Plans HTML rendered");

    // Set up event handlers for plan buttons
    $(".plan-btn").on("click", function () {
      if ($(this).hasClass("disabled-btn")) {
        return; // Don't do anything if the button is disabled
      }

      const planId = $(this).data("plan-id");
      const planAmount = $(this).data("plan-amount");
      const planDuration = $(this).data("plan-duration");

      console.log("Plan button clicked:", planId, planAmount, planDuration);

      // Check if the user is authenticated before proceeding
      $.post(
        "scripts/data-scripts/auth-status.data.php",
        { request_type: "auth-session-check" },
        function (return_data) {
          if (
            return_data["message"] !== "error" &&
            return_data["authenticated"] === true
          ) {
            // Check if the user has an active subscription
            $.post(
              "scripts/data-scripts/check-subscription.php",
              { user_id: userid },
              function (subscription_data) {
                if (subscription_data["has_subscription"] === true) {
                  // Show warning modal
                  $("#subscriptionConfirmModal").modal("show");

                  // Store plan details in modal for reference
                  $("#modalPlanId").text(planId);
                  $("#modalPlanAmount").text(planAmount);
                  $("#modalPlanDuration").text(planDuration);
                  $("#user_id1").text(userid);
                  $("#email1").text(email);
                } else {
                  // Proceed with subscription
                  // Extract month count from duration
                  let monthCount = 1;
                  if (
                    element["plan_duration"].toLowerCase().includes("month")
                  ) {
                    monthCount = parseInt(element["plan_duration"]) || 1;
                  } else if (
                    element["plan_duration"].toLowerCase().includes("year")
                  ) {
                    monthCount = 12;
                  }

                  plan_payment(
                    planAmount,
                    planId,
                    userid,
                    email,
                    monthCount,
                    this
                  );
                }
              },
              "json"
            );
          } else {
            // Redirect to login
            window.location.href = "signup.php?login";
          }
        },
        "json"
      );
    });
  }

  // Handle subscription confirmation
  //   $("#confirmSubscription").on("click", function () {
  //     const planId = $("#modalPlanId").text();
  //     const planAmount = $("#modalPlanAmount").text();
  //     const planDuration = $("#modalPlanDuration").text();
  //     const userId = $("#user_id1").text();
  //     const email = $("#email1").text();

  //     // Extract month count from duration
  //     let monthCount = 1;
  //     if (planDuration.toLowerCase().includes("month")) {
  //       monthCount = parseInt(planDuration) || 1;
  //     } else if (planDuration.toLowerCase().includes("year")) {
  //       monthCount = 12;
  //     }

  //     // Close the modal
  //     $("#subscriptionConfirmModal").modal("hide");

  //     // Process the payment
  //     plan_payment2(planAmount, planId, userId, email, monthCount);
  //   });

  // Handle subscription confirmation
  $("#confirmSubscription").on("click", function () {
    const planId = $("#modalPlanId").text();
    const planAmount = $("#modalPlanAmount").text();
    const planDuration = $("#modalPlanDuration").text();
    const userId = $("#user_id1").text();
    const email = $("#email1").text();

    // Extract month count from duration
    let monthCount = 1;
    if (planDuration.toLowerCase().includes("month")) {
      monthCount = parseInt(planDuration) || 1;
    } else if (planDuration.toLowerCase().includes("year")) {
      monthCount = 12;
    }

    // Close the modal
    $("#subscriptionConfirmModal").modal("hide");

    // Process the payment
    plan_payment(planAmount, planId, userId, email, monthCount);

    // Add console logging for debugging
    console.log("Subscription change initiated:", {
      planId,
      planAmount,
      userId,
      email,
      monthCount,
    });
  });

  // Add a function to refresh the subscription status display
  function refreshSubscriptionStatus() {
    // Check user subscription status and update UI
    check_user_subscription();

    // Reload plan buttons
    $.post(
      "scripts/data-scripts/plans.data.php",
      { request_type: "get-plans" },
      function (plansData) {
        if (plansData.plans && Array.isArray(plansData.plans)) {
          // Update plan buttons based on subscription status
          plansData.plans.forEach((plan) => {
            const buttonSelector = `button[data-plan-id="${plan.plan_id}"]`;
            if (plan.is_subscribed) {
              $(buttonSelector)
                .prop("disabled", true)
                .text("Subscribed")
                .addClass("disabled-btn");
            } else {
              $(buttonSelector)
                .prop("disabled", false)
                .text(
                  plan.was_subscribed ? "Subscribe Again" : "Get Started Now"
                )
                .removeClass("disabled-btn");
            }
          });
        }
      },
      "json"
    );
  }

  // Call this function after successful payment verification
  function onPaymentSuccess(reference) {
    $("#paymentReference").text(reference);
    $("#paymentSuccessModal").modal("show");

    // Refresh subscription status after a short delay
    setTimeout(refreshSubscriptionStatus, 1000);
  }
});
