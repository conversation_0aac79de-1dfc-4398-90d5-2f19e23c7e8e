  <!-- Header section-->
  <?php require_once(__DIR__ . '/resources/header.php'); ?>

  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/croppie/2.6.5/croppie.css" />
  <link rel="stylesheet" href="assets/css/profile.css" />
  <link rel="stylesheet" href="assets/css/explore-style.css">
  </head>

  <body>

      <!-------------Loader Start----------------->
      <?php require_once(__DIR__ . '/resources/loader.php'); ?>
      <!-------------Loader End----------------->

      <header id="#top">

          <!-------------Navbar Start----------------->
          <?php require_once(__DIR__ . '/resources/navbar.php'); ?>
          <!-------------Navbar End----------------->



      </header>

      <!------------- Modal for cropping image ----------------->
      <div class="modal fade cropImageModal" id="cropImagePop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
          <button type="button" class="close-modal-custom" data-dismiss="modal" aria-label="Close"><i class="feather icon-x"></i></button>
          <div class="modal-dialog">
              <div class="modal-content">
                  <div class="modal-body p-0">
                      <div class="modal-header-bg"></div>
                      <div class="up-photo-title">
                          <h3 class="modal-title">Update Profile Photo</h3>
                      </div>
                      <div class="up-photo-content pb-5">

                          <div id="upload-demo" class="center-block">
                              <h5><i class="fas fa-arrows-alt mr-1"></i> Drag your photo as you require</h5>
                          </div>
                          <div class="upload-action-btn text-center px-2">
                              <button type="button" id="cropImageBtn" class="btn btn-default btn-medium btn-primary px-3 mr-2">Save Photo</button>
                              <button type="button" class="btn btn-default btn-medium bg-default-light btn-secondary px-3 ml-sm-2 replacePhoto position-relative">Replace Photo</button>
                          </div>
                      </div>
                  </div>
              </div>
          </div>
      </div>
      <!------------- End Modal----------------->


      <!-- Modal to remove profile pic -->
      <!-- <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
          <div class="modal-dialog">
              <div class="modal-content">
                  <div class="modal-header">
                      <h5 class="modal-title" id="exampleModalLabel">Remove Profile Picture</h5>
                      <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                  </div>
                  <div class="modal-body">
                      Are you sure you want to remove your current profile picture?
                  </div>
                  <div class="modal-footer">
                      <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Nevermind!!</button>
                      <button type="button" class="btn btn-danger">Yes, remove it.</button>
                  </div>
              </div>
          </div>
      </div> -->

      <!-- Modal for removing profile image modified -->
      <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
          <div class="modal-dialog">
              <div class="modal-content">
                  <div class="modal-header">
                      <h5 class="modal-title" id="exampleModalLabel">Remove Profile Picture</h5>
                      <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                  </div>
                  <div class="modal-body">
                      Are you sure you want to remove your profile picture?
                  </div>
                  <div class="modal-footer">
                      <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                      <button type="button" class="btn btn-danger">Remove</button>
                  </div>
              </div>
          </div>
      </div>
      <!-- End modal --->

      <div class="page-banner change-name">
          <div class="container">
              <div class="row">
                  <div class="col-lg-6 offset-lg-3">
                      <div class="header-text">
                          <!-- <h2 class="users_name"><em>Claire </em> Doe</h2> -->
                          <div class="userprofile social ">
                              <div class="confirm-identity">
                                  <div class="ci-user d-flex align-items-center justify-content-center">
                                      <div class="ci-user-picture">
                                          <i id="remove_avatar" class="fa fa-times removeProfile hidden" data-bs-toggle="modal" data-bs-target="#exampleModal"></i>
                                          <img src="assets/images/default.jpg" id="item-img-output" class="imgpreviewPrf img-fluid userpicimg" alt="">
                                          <div id="icon_upload" class="upload-icon hidden">
                                              <a href="javascript:;" class="userEditeBtn">
                                                  <input type="file" class="item-img file filepreviewprofile" />
                                                  <i class="fa fa-camera fa-lg"></i>
                                              </a>
                                          </div>
                                      </div>
                                  </div>
                              </div>

                              <h3 class="username users_name">Claire Doe</h3>
                              <p id="user_country">GRA, IKEJA</p>
                          </div>
                          <p id="user_bio">When big ideas, bold creative, and brilliant delivery come together, your business thrives and we fuse market and customer insights with business realities to articulate and achieve objectives.</p>

                      </div>
                  </div>
              </div>
          </div>
      </div>

      <!-------------Profile Start----------------->
      <?php require_once(__DIR__ . '/resources/profile-me.php'); ?>
      <!-------------Profile End----------------->


      <!-------------Call_to_signup Start----------------->
      <?php require_once(__DIR__ . '/resources/call_to_signup.php'); ?>
      <!-------------Call_to_signup End----------------->

      <!-- Footer section-->
      <?php require_once(__DIR__ . '/resources/footer.php'); ?>


      <!-- Scripts -->
      <!-- Bootstrap core JavaScript -->
      <script src="vendor/jquery/jquery.min.js"></script>
      <script src="vendor/bootstrap/js/bootstrap.min.js"></script>
      <script src="https://cdnjs.cloudflare.com/ajax/libs/croppie/2.6.5/croppie.min.js"></script>
      <script src="assets/js/profile-page.js"></script>

      <script src="scripts/view-scripts/explore-view.js"></script>
      <script src="assets/js/explore-script.js"></script>
      <script src="assets/js/custom.js"></script>
      <script src="scripts/view-scripts/user_profile_view.js"></script>

      <script src="scripts/view-scripts/footer.js"></script>
      <script>
          $(document).ready(function() {
              set_interval();
          });
          $('body').click(function() {
              reset_interval();
          });
          $('body').mousemove(function() {
              reset_interval();
          });
          $('body').keypress(function() {
              reset_interval();
          });

          // setTimeout(function() {
          //     $('.loader').fadeToggle();
          // }, 1500);

          $("a[href='#top']").click(function() {
              $("html, body").animate({
                  scrollTop: 0
              }, "slow");
              return false;
          });

          let logout_link = document.getElementsByClassName("logged-out");
          let login_link = document.getElementsByClassName("logged-in");

          $.post("scripts/data-scripts/auth-status.data.php", {
              request_type: 'auth-session-check'
          }, function(data) {
              if (data["message"] == "success") {
                  // authed
                  $(logout_link).addClass("hide");
                  $(login_link).removeClass("hide");

                  $("#authenticated-username").html(data["username"]);
              } else {
                  // not authed
                  $(logout_link).removeClass("hide");
                  $(login_link).addClass("hide");
              }
          });

          $.ajax({
              type: 'get',
              url: 'scripts/data-scripts/user_profile.data.php',
              data: {
                  user_id: 0
              },
              dataType: 'json',
              success: function(result) {
                  if (result[0].avatar == "") {
                      return;
                  }
                  $("#auth-imgg").attr("src", `assets/images/${result[0].avatar}`);
                  $("#auth-img").attr("src", `assets/images/${result[0].avatar} `);
              }
          });



          $("#signout-btn").click(function() {
              logout_call();
          });



          function set_interval() {
              timer = setInterval(() => {
                  auto_logout();
              }, 3600000);
          }

          function reset_interval() {
              clearInterval(timer);
              timer = setInterval(() => {
                  auto_logout();
              }, 3600000);
          }

          function auto_logout() {
              logout_call();
          }

          function logout_call() {
              console.log("auto logout called");
              $.post("scripts/data-scripts/auth-status.data.php", {
                  request_type: 'auth-session-end'
              }, function(data) {
                  if (data["message"] == "success") {
                      // signed out
                      window.location.href = "signup.php?login";
                  }
              });
          }
      </script>
      <script>
          setTimeout(function() {
              $('.loader').fadeToggle();
          }, 2000);

          $("a[href='#top']").click(function() {
              $("html, body").animate({
                  scrollTop: 0
              }, "slow");
              return false;
          });
      </script>
  </body>

  </html>