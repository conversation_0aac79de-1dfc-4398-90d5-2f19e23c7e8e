<?php

header("Content-Type: application/json");
session_start();

include '../class-scripts/db-connection.class.php';
include '../class-scripts/user-auth.class.php';
// include '../class-scripts/user-oauth-provider.class.php';

if (isset($_POST['request_type'])) {
    $requestType = $_POST['request_type'];

    if ($requestType == 'auth-session-check') {
        if (isset($_SESSION['username']) && isset($_SESSION['id'])) {
            // Get user email from database
            $db = new DBconnection();
            $conn = $db->connect();
            $user_id = $_SESSION['id'];

            $sql = "SELECT email FROM users WHERE id = $user_id";
            $result = mysqli_query($conn, $sql);
            $email = '';

            if ($result && mysqli_num_rows($result) > 0) {
                $row = mysqli_fetch_assoc($result);
                $email = $row['email'];
            }

            $responseBody = array(
                "message" => "success",
                "authenticated" => true,
                "username" => $_SESSION['username'],
                "user_id" => $_SESSION['id'],
                "email" => $email
            );

            echo json_encode($responseBody);
        } else {
            $responseBody = array("message" => "error", "authenticated" => false);
            echo json_encode($responseBody);
        }
    }

    if ($requestType == 'auth-session-end') {
        if (isset($_SESSION['id'])) {

            $UserLoginObj = new UserAuth();
            $UserLoginObj->clearUserRememberToken($_SESSION['id']);

            $result = session_destroy();

            if ($result) {
                echo json_encode(array("message" => "success"));
            } else {
                echo json_encode(array("message" => "error"));
            }
        }
    }
}
