:root {
  --c-blue-900: #081030;
  --c-blue-800: #111b40;
  --c-blue-700: #152148;
  --c-blue-500: #1d7bdb;
  --c-blue-300: #293359;
  --c-blue-200: #4e5985;
  --c-blue-100: #dfe8ff;
  --c-green-400: #4ade80;
  --c-white-600: #ffffff;
  --default-theme: #ff565b;
}

body {
  font-family: "Poppins", sans-serif;
}

.expanded {
  max-width: 1320px;
}

@media (min-width: 768px) {
  .expanded {
    width: 100%;
    overflow: hidden;
    max-width: 100%;
    padding: 0px 7%;
  }
}

.mega-drpdwn {
  columns: 1;
  -webkit-columns: 1;
  -moz-columns: 1;
}

.dropdown-menu.show {
  display: grid;
}

.accordion-item {
  border: none;
}

img {
  width: 100%;
  overflow: hidden;
  /* margin-top: 150px;*/
}

/** i don't know who owns it **/
/* .thumb{
  width: 100%;
  overflow: hidden;
  margin-top: 150px;
  margin-left: 50px;
  }

  .FogP {
    /*background: darken(white, 5%);
    width: 100%;
    padding: 15px 40px;
    box-sizing: border-box;    
    color: gray;
    font-size: 12px;
    text-align: center;
    margin-left: 20px;
    margin-top: -15px;
  } */

a {
  text-decoration: none;
}

ul {
  padding: 0;
  margin: -15px 0px;
  /* margin: -15px; */
  list-style: none;
}

ul li {
  display: inline-block;
  color: #7a7a7a;
}

p {
  font-size: 15px;
  line-height: 28px;
  margin-bottom: 0;
  color: #7a7a7a;
}

.banner-button {
  width: 320px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  /* gap: 5px; */
  margin: 0 auto;
}

.main-button {
  font-size: 14px;
  text-transform: capitalize;
  font-weight: 400;
  color: #ffffff;
  background-color: var(--default-theme);
  padding: 11px 25px;
  border-radius: 7.5px;
  display: inline-block;
  outline: none;
  border: none;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}

button.main-button:hover {
  background-color: #2a2a2a;
}

.primary-button a {
  font-size: 14px;
  text-transform: capitalize;
  font-weight: 400;
  color: #ffffff;
  background-color: var(--default-theme);
  padding: 11px 25px;
  border-radius: 7.5px;
  display: inline-block;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}

.primary-button a:hover {
  background-color: #2a2a2a;
}

.white-button a {
  font-size: 14px;
  text-transform: capitalize;
  font-weight: 400;
  color: var(--default-theme);
  background-color: #ffffff;
  padding: 11px 25px;
  border-radius: 7.5px;
  display: inline-block;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}

.white-button a:hover {
  color: #ffffff;
  background-color: var(--default-theme);
}

.default-button a {
  font-size: 14px;
  text-transform: capitalize;
  font-weight: 400;
  color: #ffffff;
  background-color: var(--default-theme);
  padding: 11px 25px;
  border-radius: 7.5px;
  display: inline-block;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}

.default-button a:hover {
  color: var(--default-theme);
  background-color: #ffffff;
}

.line-space {
  margin: 25px;
  padding: 25px;
}
section {
  margin-top: 60px;
  padding-top: 60px;
}

@media (min-width: 576px) {
  section {
    margin-top: 15px;
    /* padding-top: 15px; */
  }
  .line-space {
    margin: 20px;
    padding: 20px;
  }
}

.section-heading {
  margin-bottom: 30px;
}

.section-heading h2 {
  font-size: 22px;
  font-weight: 700;
  text-transform: capitalize;
  color: #2a2a2a;
  line-height: 32px;
  letter-spacing: -0.5px;
}

@media (min-width: 576px) {
  .section-heading h2 {
    font-size: 34px;
    line-height: 40px;
    text-transform: uppercase;
    letter-spacing: auto;
  }
}
/*--------Trending Accordion Start--------------*/
/* .section-headings h2 {
  font-size: 22px;
  font-weight: 700;
  text-transform: capitalize;
  color: #2a2a2a;
  line-height: 32px;
  letter-spacing: -0.5px;
}

@media (min-width: 576px) {
  .section-headings h2 {
    font-size: 34px;
    line-height: 48px;
    text-transform: uppercase;
    letter-spacing: auto;
  }
} */
/*--------Trending Accordion End--------------*/

/*--------Notification Start--------------*/
.notification-box {
  position: absolute;
  z-index: 99;
  top: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  text-align: center;
}

.notification-bell * {
  display: block;
  margin: 0 auto;
  background-color: #fff;
  box-shadow: 0px 0px 15px #000;
}

.bell-middle {
  width: 25px;
  height: 25px;
  margin-top: -1px;
  border-radius: 12.5px 12.5px 0 0;
}

.bell-rad {
  width: 8px;
  height: 4px;
  margin-top: 2px;
  border-radius: 0 0 4px 4px;
}
.notification {
  position: relative;
  display: inline-block;
}
.notification-no {
  position: absolute;
  top: -10px; /* Adjust position as needed */
  right: -10px; /* Adjust position as needed */
  width: 20px; /* Adjust width as needed */
  height: 20px; /* Adjust height as needed */
  border-radius: 50%;
  background-color: green;
  color: #ff565b;
  font-size: 12px; /* Adjust font size as needed */
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}
.notification .notification-no {
  display: inline-block;
  min-width: 20px; /* Adjust size as needed */
  height: 20px;
  line-height: 20px;
  text-align: center;
}
/* .notification-count {
  position: sticky;
  top: 12px;
  right: 400px;
  width: 15px;
  height: 15px;
  line-height: 28px;
  font-size: 12px;
  border-radius: 50%;
  background-color: green;
  color: #ff565b;
 
  
} */
.notification-no:hover {
  color: #fff;
}

@media (min-width: 992px) {
  .notification-count {
    margin-left: 45px;
    margin-top: 0px;
  }
}

.badge-light {
  position: sticky;
  top: 10px;
  right: 330px;
  width: 15px;
  height: 15px;
  line-height: 18px;
  font-size: 12px;
  border-radius: 50%;
  background-color: green;
  color: #ff565b;
}
.badge-light:hover {
  color: #fff;
}

/*****************Shopping count**************/

.shopping-box {
  position: fixed;
  z-index: 99;
  top: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  text-align: center;
}

.shopping-bell * {
  display: block;
  margin: 0 auto;
  background-color: #fff;
  box-shadow: 0px 0px 15px #000;
}

.shopping-middle {
  width: 25px;
  height: 25px;
  margin-top: -1px;
  border-radius: 12.5px 12.5px 0 0;
}

.shopping-rad {
  width: 8px;
  height: 4px;
  margin-top: 2px;
  border-radius: 0 0 4px 4px;
}
.shopping-count {
  position: absolute;
  top: 12px;
  right: 419px;
  width: 15px;
  height: 15px;
  line-height: 16px;
  font-size: 12px;
  border-radius: 50%;
  background-color: green;
  color: #ff565b;
}
.shopping-count:hover {
  color: #fff;
}
/* 
@media (min-width: 100%) {
  .notification-count {
    position: absolute;
    top: 330px;
    right: 258px;
    width: 15px;
    height: 15px;
    line-height: 16px;
    font-size: 12px;
    border-radius: 50%;
    background-color: green;
    color: #ff565b;
  }
} */

/*****************Shopping count End**************/

/*--------Notification End--------------*/

.section-heading h2 em {
  color: var(--default-theme);
  font-style: normal;
  padding-left: 100px;
  position: relative;
}

.section-heading h2 em::before {
  width: 80px;
  height: 2px;
  background-color: var(--default-theme);
  content: "";
  position: absolute;
  left: 0;
  top: 22px;
}

.hide {
  display: none !important;
}

.loader {
  background-color: #2a2a2a;
  position: fixed;
  right: 0;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 222222;
}

.loader svg {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.loader svg path,
.loader svg rect {
  fill: var(--default-theme);
}

.main-navigation {
  position: absolute;
  width: 100%;
  padding: 0px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.navbar-nav {
  background-color: #2a2a2a;
  border-radius: 7.5px;
  padding: 10px 20px;
  margin-top: 10px;
}

@media (min-width: 992px) {
  .navbar-nav {
    background-color: transparent;
    border-radius: 0px;
    padding: 0px;
    margin-top: 0px;
  }
}

header.nav-scrolled .main-navigation {
  padding: 10px 0px;
  position: fixed;
  z-index: 222;
  background-color: #2a2a2a;
  -webkit-box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
}

header.nav-scrolled .main-navigation .navbar-brand {
  padding: 0px;
}

.navbar-brand span {
  font-weight: 800;
  color: #fff;
  font-size: 23px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 1);
}

header.nav-scrolled .main-navigation .navbar-nav {
  margin-top: 0px;
}

header.nav-scrolled .main-navigation .navbar-nav .nav-item .nav-link {
  border-top: none;
  padding: 8px 15px;
  font-weight: 300;
  letter-spacing: 0.5px;
}

@media (min-width: 576px) {
  header.nav-scrolled .main-navigation .navbar-nav .nav-item .nav-link {
    font-weight: 400;
  }
}

header.nav-scrolled .main-navigation .navbar-nav .nav-item .active {
  border-top: none;
  border-radius: 7px;
}

.navbar-expand-lg .navbar-collapse {
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  z-index: 1;
}

.navbar-light {
  padding: 10px 0px;
}

@media (min-width: 992px) {
  .navbar-light {
    padding: 0px;
  }
}

.navbar-light .navbar-brand {
  padding: 0px 0px 0px 0px;
}

@media (min-width: 992px) {
  .navbar-light .navbar-brand {
    padding: 14px 0px 0px 0px;
  }
}

.navbar-light .navbar-nav .nav-item {
  margin-left: 0px;
  text-align: center;
  border-bottom: 1px solid rgba(250, 250, 250, 0.1);
}

.navbar-light .navbar-nav .nav-item:last-child {
  border-bottom: none;
}

@media (min-width: 992px) {
  .navbar-light .navbar-nav .nav-item {
    margin-left: 10px;
    border-bottom: none;
  }
}

.navbar-light .navbar-nav .nav-item .nav-link {
  color: #ffffff;
  font-size: 15px;
  font-weight: 600;
  /* padding: 10px 20px; */
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 1);
  border-top: 2px solid transparent;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}

.navbar-light .navbar-nav .nav-item .nav-link:hover {
  color: var(--default-theme);
}

.navbar-light .navbar-nav .nav-item .active {
  font-weight: 400;
  color: var(--default-theme);
  background-color: transparent;
  border-radius: 0px;
  border-top: none;
}

@media (min-width: 992px) {
  .navbar-light .navbar-nav .nav-item .active {
    background-color: #ffffff;
    border-bottom-left-radius: 7px;
    border-bottom-right-radius: 7px;
    border-top: 2px solid var(--default-theme);
  }
}

.navbar-light .navbar-toggler {
  background-color: #fff;
}

/*** avatar sidebar ***/
button.rtl-side-btn {
  background: transparent;
  border: 0;
  padding: 0;
  cursor: pointer;
}

h2.rtl-heading {
  font-size: 16px;
  font-weight: 500;
  padding-left: 20px;
  margin: 22px 0;
}

.rtl-overlay {
  position: fixed;
  z-index: 3;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(3px);
  opacity: 0;
  visibility: hidden;
  transition: 0.4s;
}

body.open .rtl-overlay {
  opacity: 1;
  visibility: visible;
}

.burger {
  position: fixed;
  z-index: 4;
  top: 10px;
  right: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.my-account-link {
  display: none;
}

@media (width >= 500px) {
  .burger {
    display: flex;
  }
}
@media (max-width: 970px) {
  .burger {
    display: none;
  }
  .my-account-link {
    display: block;
  }
}

.rtl-burger-avatar {
  width: 40px;
  height: 40px;
  transition: 0.4s;
  border-radius: 30px;
}

body.open .rtl-burger-avatar {
  translate: 40px 0;
  opacity: 0;
  visibility: hidden;
}

.burger-icon {
  display: block;
  width: 30px;
  height: 30px;
  background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz48c3ZnIHdpZHRoPSIyNHB4IiBoZWlnaHQ9IjI0cHgiIHN0cm9rZS13aWR0aD0iMS41IiB2aWV3Qm94PSIwIDAgMjQgMjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgY29sb3I9IiNmZmZmZmYiPjxwYXRoIGQ9Ik0zIDVoMThNMyAxMmgxOE0zIDE5aDE4IiBzdHJva2U9IiNmZmZmZmYiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjwvcGF0aD48L3N2Zz4=)
    no-repeat center center;
}

body.open .burger-icon {
  background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz48c3ZnIHdpZHRoPSIyNHB4IiBoZWlnaHQ9IjI0cHgiIHN0cm9rZS13aWR0aD0iMS41IiB2aWV3Qm94PSIwIDAgMjQgMjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgY29sb3I9IiNmZmZmZmYiPjxwYXRoIGQ9Ik02Ljc1OCAxNy4yNDNMMTIuMDAxIDEybTUuMjQzLTUuMjQzTDEyIDEybTAgMEw2Ljc1OCA2Ljc1N00xMi4wMDEgMTJsNS4yNDMgNS4yNDMiIHN0cm9rZT0iI2ZmZmZmZiIgc3Ryb2tlLXdpZHRoPSIxLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+PC9wYXRoPjwvc3ZnPg==)
    no-repeat no-repeat center center;
}

.rtl-sidebar {
  /* position: absolute; */
  position: fixed;
  z-index: 99999;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 40px 20px;
  background: #000000;
  opacity: 0;
  visibility: hidden;
  filter: blur(10px);
  transition-property: filter, visibility, opacity;
  transition-duration: 0.6s;
}

@media (width >= 330px) {
  .rtl-sidebar {
    transition-property: translate;
  }
}

@media (width >= 400px) {
  .rtl-sidebar {
    translate: 100% 0;
    width: 195px;
    transition: 0.4s;
    border-left: 1px solid rgba(255, 255, 255, 0.16);
  }
}

body.open .rtl-sidebar {
  translate: 0 0;
  opacity: 1;
  visibility: visible;
  filter: blur(0);
}

.rtl-sidebar-avatar {
  width: 80px;
  height: 80px;
  margin-bottom: 20px;
  border-radius: 50px;
  box-shadow: 0 0 0 2px rgb(0 0 0), 0 0 0 4px rgb(2 90 158);
}

.rtl-sidebar-username {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.96);
  padding-bottom: 15px;
}

.rtl-sidebar-role {
  margin: 0 0 20px;
  font-size: 9px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.57);
}

.rtl-sidebar-menu {
  display: grid;
  width: 100%;
  padding: 10px 0;
  padding-left: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.16);
}

@media (width >= 350px) {
  .rtl-sidebar-menu {
    padding-left: 0;
  }
}

.rtl-sidebar-menu > button {
  display: flex;
  gap: 8px;
  align-items: center;
  font-family: "Poppins";
  font-size: 16px;
  font-weight: 200;
  letter-spacing: 2px;
  line-height: 1;
  padding: 10px 20px;
}

.rtl-sidebar-menu > button > img {
  width: 17px;
  height: 17px;
}

.rtl-sidebar-menu > button > a > span,
.rtl-sidebar-menu > button > span {
  color: #f9f9f9;
  font-size: 11px;
  translate: 0 1px;
  font-weight: 700;
}

.rtl-sidebar-menu > button > span a {
  text-decoration: none;
  color: #f9f9f9;
}

/*** avatar sidebar ***/

/** dropdown **/
/* .select-btn{
  display: flex;
  height: 50px;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}
.select-btn .btn-text{
  font-size: 17px;
  font-weight: 400;
  color: #fff;
  padding-right: 10px;
}
.select-btn .arrow-dwn{
  display: flex;
  height: 21px;
  width: 21px;
  color: #fff;
  font-size: 14px;
  border-radius: 50%;
  background: var(--default-theme);
  align-items: center;
  justify-content: center;
  transition: 0.3s;
}
.select-btn.open .arrow-dwn{
  transform: rotate(-180deg);
}
.list-items{
  position: absolute;
  margin-top: 15px;
  border-radius: 8px;
  padding: 16px;
  background-color: #fff;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
  /* max-height: 220px;
  overflow-y: scroll;
  overflow-x: hidden; 
  display: none;
}
.select-btn.open ~ .list-items{
  display: block;
} */

/* Always show the scrollbar of the dropdown */
/* .select-btn.open ~ .list-items::-webkit-scrollbar {
  width: 8px;
  height: 0;
}
.select-btn.open ~ .list-items::-webkit-scrollbar-thumb {
  background-color: rgba(0,0,0,.2);
  border-radius: 8px;
}
.select-btn.open ~ .list-items::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0,0,0,.3);
}

.list-items .item{
  display: flex;
  align-items: center;
  list-style: none;
  height: 50px;
  cursor: pointer;
  transition: 0.3s;
  padding: 0 15px;
  border-radius: 8px;
}
.list-items .item:hover{
  background-color: #e7edfe;
}
.item .item-text{
  font-size: 16px;
  font-weight: 400;
  color: #333;
} */

/** dropdown **/
button:focus:not(:focus-visible) {
  -webkit-box-shadow: none;
  box-shadow: none;
}

.main-banner {
  background-color: #333 !important;
  /* background-image: url(../images/banner-bg.jpg); */
  text-align: center;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
  padding: 170px 0px 270px 0px;
  /* padding: 8.3vw 0vw 14.2vw 0vw; */
}
/* .main-banner:after {
  content: ""; 
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(120deg,#eaee44,#33d0ff);
  background-color: #333;
  opacity: .7;
} */

.main-banner > * {
  z-index: 100;
}

@media (min-width: 576px) {
  .main-banner {
    padding: 270px 0px 400px 0px;
    /* padding: 14.3vw 0vw 19.8vw 0vw; */
    /* padding: calc(20% - 2px) 0 calc(23% - 2px) 0; */
  }
}

.main-banner h6 {
  font-size: 15px;
  color: #ffffff;
  text-transform: uppercase;
  font-weight: 700;
  letter-spacing: 2.5px;
  margin-bottom: 25px;
  margin-top: 15px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 1);
}

.main-banner h2 {
  color: #ffffff;
  font-size: 50px;
  font-weight: 700;
  text-transform: uppercase;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 1);
}

.main-banner h2 em,
.main-banner h6 em {
  font-style: normal;
  color: var(--default-theme);
}

/** typewriting animation start**/
.typed-cursor {
  opacity: 1;
  -webkit-animation: blink 0.7s infinite;
  -moz-animation: blink 0.7s infinite;
  animation: blink 0.7s infinite;
}

@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
/** typewriting animation start**/

.search-form {
  margin-top: -150px;
}

.search-form #search-form {
  padding: 30px;
  background-color: #ffffff;
  -webkit-box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.15);
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.15);
  border-radius: 7.5px;
  margin-bottom: 0px;
}

@media (min-width: 576px) {
  .search-form #search-form {
    padding: 60px;
  }
}

.search-form .image-credit {
  text-align: left;
  margin-left: 30px;
  color: rgba(234, 240, 246, 0.9);
  font-size: 14px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 1);
}

.search-form .image-credit span a {
  text-decoration: none;
  color: inherit;
}

.search-form .image-credit span a:hover {
  color: var(--default-theme);
}
.search-form #search-form label {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 10px;
}

@media (min-width: 576px) {
  .search-form #search-form label {
    margin-bottom: 20px;
  }
}

.search-form #search-form input,
.search-form #search-form select {
  width: 100%;
  height: 44px;
  border-radius: 7.5px;
  border: 1px solid #eeeeee;
  font-size: 14px;
  padding: 0px 15px;
  margin-bottom: 15px;
}

/*Clearing Floats*/
.cf:before,
.cf:after {
  content: "";
  display: table;
}

.cf:after {
  clear: both;
}

.cf {
  zoom: 1;
}

.search-form #search-form .search-wrapper {
  width: 100%;
  position: relative;
  /* margin: 150px auto 50px auto; */
}

.search-form #search-form .search-wrapper input {
  /* width: 330px; */
  width: 100%;
  padding-right: 20px;
  float: left;
}

.search-form #search-form .search-wrapper button::before {
  content: "Search Now";
}
@media (max-width: 992px) {
  .search-form #search-form .search-wrapper button::before {
    content: "\f002";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
  }
}
.search-form #search-form .search-wrapper button {
  overflow: visible;
  position: absolute;
  top: 0;
  z-index: 2;
  right: 0;
  /* float: right; */
  transform: translateX(2px);
  border: 0;
  padding: 0;
  cursor: pointer;
  height: 44px;
  /* width: 110px; */
  width: 20%;
  border-radius: 0px 7.5px 7.5px 0px;
}

.trending-list {
  margin-bottom: 15px;
}

.trending-list .badge {
  margin-right: 10px;
  cursor: pointer;
}
.trending-list .badge:hover {
  background-color: var(--default-theme);
  transform: scale(1.1);
}

/* .search-form #search-form button.main-button {
  width: 100%;
} */

section.explore-work .left-image img {
  border-radius: 7.5px;
}

section.explore-work .explore_more_button {
  text-align: center;
  margin-top: 50px;
}
t section.explore-work .right-content {
  margin-left: 0;
  margin-top: 30px;
}

@media (min-width: 992px) {
  section.explore-work .right-content {
    margin-left: 45px;
    margin-top: 0px;
  }
}

section.explore-work .right-content ul li {
  display: block;
  margin-bottom: 60px;
}

section.explore-work .right-content ul li:last-child {
  margin-bottom: 0px;
}

section.explore-work .right-content ul li h4 {
  font-size: 20px;
  color: #2a2a2a;
  font-weight: 700;
  margin-bottom: 30px;
  padding-bottom: 10px;
  border-bottom: 2px solid #eeeeee;
}

section.explore-work .right-content ul li ul.info li {
  margin-bottom: 30px;
  width: 100%;
}

section.explore-work .right-content ul li ul.info li span {
  font-size: 15px;
  color: #7a7a7a;
  display: block;
  width: 250px;
  height: 50px;
  float: none !important;
}

/* pricing section in photos.php start*/
.photos_price-table {
  background-color: var(--c-white-600);
  border-radius: 16px;
  max-width: 375px;
  padding: 22px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 15px 45px 0 rgba(0, 0, 0, 0.15);
  position: relative;
  background-image: linear-gradient(
    135deg,
    rgba(247, 247, 247, 0.15),
    rgba(248, 252, 255, 0.1) 20%,
    var(--c-white-600) 40%,
    var(--c-white-600) 100%
  );
}
.photos_price-table:after {
  content: "";
  display: block;
  top: -3px;
  left: -3px;
  bottom: -3px;
  right: -3px;
  z-index: -1;
  position: absolute;
  border-radius: 16px;
  background-image: linear-gradient(
    135deg,
    var(--c-green-400),
    var(--c-blue-500) 40%,
    var(--c-blue-300) 60%,
    var(--c-blue-700) 100%
  );
}
.photos_price-table .price {
  /* font-size: 3rem; */
  font-size: 2rem;
  line-height: 1;
  font-weight: 700;
  display: inline-flex;
  align-self: center;
  align-items: center;
  gap: 4px;
  position: relative;
  color: var(--default-theme);
}
.photos_price-table .price small {
  font-size: 1.25rem;
  font-weight: 400;
  position: absolute;
  left: -1.5ch;
}
.photos_price-table .title {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.25;
  text-align: center;
  margin-top: 16px;
  color: #2a2a2a;
}

.photos_price-table .description {
  font-size: 1rem;
  text-align: center;
  margin-top: 4px;
  color: #2a2a2a;
}

.sub-plan {
  position: relative;
}
.sub-plan + .sub-plan {
  margin-top: 16px;
}
.sub-plan summary {
  display: inline-flex;
  align-items: center;
  list-style: none;
}
.sub-plan summary::-webkit-details-marker {
  display: none;
}
.sub-plan[open] summary:after {
  content: "";
  display: block;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  position: fixed;
  z-index: 50;
}
.sub-plan[open] div {
  animation: scale 0.15s ease;
}

.sub-plan .checkmark {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--default-theme);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  margin-bottom: 6px;
}
.sub-plan .checkmark svg {
  width: 18px;
  height: 18px;
  color: #fff;
}
/* pricing section in photos.php end*/

@media (min-width: 576px) {
  section.explore-work .right-content ul li ul.info li span {
    display: inline-block;
    float: left !important;
  }
}

section.explore-work .right-content ul li ul.info li span svg {
  margin-right: 8px;
}

section.explore-work .right-content ul li ul.info li h6 {
  display: inline-block;
}

section.explore-work .right-content ul li ul.info li h6 a {
  font-size: 17px;
  font-weight: 700;
  color: #2a2a2a;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}

section.explore-work .right-content ul li ul.info li h6 a:hover {
  color: var(--default-theme);
}

section.explore-work .right-content ul li ul.info li em {
  font-size: 15px;
  line-height: 28px;
  color: #7a7a7a;
  font-style: normal;
  display: block;
}

@media (min-width: 576px) {
  section.explore-work .right-content ul li ul.info li em {
    display: inline;
  }
}

/*--------------------------------------------------------------
# About Section
--------------------------------------------------------------*/
.about .content h2 {
  font-weight: 700;
  font-size: 24px;
  color: var(--color-primary);
}

.about .content ul {
  list-style: none;
  padding: 0;
}

.about .content ul li {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.about .content ul strong {
  margin-right: 10px;
}

.about .content ul i {
  font-size: 16px;
  margin-right: 5px;
  color: var(--color-primary);
  line-height: 0;
}

/*--------------------------------------------------------------
# Testimonials Section
--------------------------------------------------------------*/
.testimonials {
  margin-top: 80px;
}

.testimonials .testimonial-item {
  box-sizing: content-box;
  padding: 30px;
  margin: 40px 30px;
  background: var(--color-secondary);
  min-height: 320px;
  display: flex;
  flex-direction: column;
  text-align: center;
  transition: 0.3s;
}

.testimonials .testimonial-item .stars {
  margin-bottom: 15px;
}

.testimonials .testimonial-item .stars i {
  color: #ffc107;
  margin: 0 1px;
}

.testimonials .testimonial-item .testimonial-img {
  width: 90px;
  border-radius: 50%;
  border: 5px solid #474a4d;
  margin: 0 auto;
}

.testimonials .testimonial-item h3 {
  font-size: 18px;
  font-weight: bold;
  margin: 10px 0 5px 0;
  color: #fff;
}

.testimonials .testimonial-item h4 {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.5);
  margin: 0;
}

.testimonials .testimonial-item p {
  font-style: italic;
  margin: 0 auto 15px auto;
}

.testimonials .swiper-pagination {
  margin-top: 20px;
  position: relative;
}

.testimonials .swiper-pagination .swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  background-color: rgba(255, 255, 255, 0.2);
  opacity: 1;
}

.testimonials .swiper-pagination .swiper-pagination-bullet-active {
  background-color: rgba(255, 255, 255, 0.5);
}

.testimonials .swiper-slide {
  opacity: 0.3;
}

@media (max-width: 1199px) {
  .testimonials .swiper-slide-active {
    opacity: 1;
  }

  .testimonials .swiper-pagination {
    margin-top: 0;
  }

  .testimonials .testimonial-item {
    margin: 40px 20px;
  }
}

@media (min-width: 1200px) {
  .testimonials .swiper-slide-next {
    opacity: 1;
    transform: scale(1.12);
  }
}

/*--------------------------------------------------------------
# Gallery Section
--------------------------------------------------------------*/
.gallery {
  margin-top: 40px;
}

.gallery .gallery-item {
  position: relative;
  overflow: hidden;
  border-radius: 10px;
}

.gallery .gallery-item img {
  transition: 0.3s;
}

.gallery .gallery-links {
  position: absolute;
  inset: 0;
  opacity: 0;
  transition: all ease-in-out 0.3s;
  background: rgba(0, 0, 0, 0.6);
  z-index: 3;
}

.gallery .gallery-links .preview-link,
.gallery .gallery-links .details-link,
.gallery .gallery-links .bookmark {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.5);
  transition: 0.3s;
  line-height: 1.2;
  margin: 30px 8px 0 8px;
}

.gallery .gallery-links .preview-link:hover,
.gallery .gallery-links .details-link:hover,
.gallery .gallery-links .bookmark:hover {
  color: #fff;
}

.gallery .gallery-links .details-link {
  font-size: 30px;
  line-height: 0;
}

.gallery .gallery-item:hover .gallery-links {
  opacity: 1;
}

.gallery .gallery-item:hover .preview-link,
.gallery .gallery-item:hover .details-link,
.gallery .gallery-item:hover .bookmark {
  margin-top: 0;
}

/** Like button style **/
.like-button {
  /* position: absolute;
  top: 50%;
  left: 50%; */
  position: relative;
  top: -3%;
  left: 3%;
  font-size: 0.8rem !important;
  transform: translate(-50%, -50%);
}
.heart-icon {
  width: 20px;
  height: 17px;
  fill: #a9aeb3;
}
.heart-icon.red {
  position: absolute;
  top: 0;
  left: 0;
  fill: #e01f4f;
  transform: scale(0);
}
.heart-icon.stroke {
  position: absolute;
  top: 0;
  left: 0;
}
.heart-icon.stroke circle {
  fill: none;
  stroke: #e0e2e4;
  stroke-width: 2;
}
/** End Like button style **/
.gallery .gallery-item:hover img {
  transform: scale(1.1);
}

.glightbox-clean .gslide-description {
  background: #222425;
}

.glightbox-clean .gslide-title {
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

/*--------------------------------------------------------------
# End Gallery Section
--------------------------------------------------------------*/
section.whats-trending .section-heading,
section.whats-trending .left-content {
  margin-right: 0px;
}

@media (min-width: 992px) {
  section.whats-trending .section-heading,
  section.whats-trending .left-content {
    margin-right: 100px;
  }
}

section.whats-trending .section-heading p,
section.whats-trending .left-content p,
section.whats-trending .left-content ul {
  margin-bottom: 30px;
}

section.whats-trending .left-content ul {
  list-style: none;
  padding: 0;
}

section.whats-trending .left-content ul li {
  padding-bottom: 10px;
  font-size: 15px;
}

section.whats-trending .left-content ul li a {
  font-size: 13px;
  color: #cd4f40;
}

section.whats-trending .left-content ul li a:hover {
  font-size: 13px;
  color: #ffa556;
}

section.whats-trending .left-content ul i {
  font-size: 15px;
  padding-right: 4px;
  color: var(--default-theme);
}

section.whats-trending .right-image {
  position: relative;
  display: none;
  text-align: center;
  margin-top: 150px;
}

@media (min-width: 992px) {
  section.whats-trending .right-image {
    display: inline-block;
  }
}

section.whats-trending .right-image .thumb {
  position: relative;
}

section.whats-trending .right-image .thumb .hover-effect {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(255, 86, 91, 0.9);
  border-radius: 7.5px;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}

section.whats-trending .right-image .thumb .hover-effect .inner-content {
  position: absolute;
  width: 100%;
  text-align: center;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

section.whats-trending .right-image .thumb .hover-effect .inner-content h4 {
  margin-bottom: 10px;
  -webkit-transform: translateX(-30px);
  transform: translateX(-30px);
  opacity: 0;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}

section.whats-trending .right-image .thumb .hover-effect .inner-content h4 a {
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

section.whats-trending .right-image .thumb .hover-effect .inner-content span {
  font-size: 15px;
  color: #ffffff;
  display: inline-block;
  -webkit-transform: translateX(30px);
  transform: translateX(30px);
  opacity: 0;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}

section.whats-trending .right-image .thumb:hover .hover-effect {
  opacity: 1;
  visibility: visible;
}

section.whats-trending
  .right-image
  .thumb:hover
  .hover-effect
  .inner-content
  h4 {
  opacity: 1;
  -webkit-transform: translateX(0px);
  transform: translateX(0px);
}

section.whats-trending
  .right-image
  .thumb:hover
  .hover-effect
  .inner-content
  span {
  -webkit-transform: translateX(0px);
  transform: translateX(0px);
  opacity: 1;
}

section.whats-trending .right-image .thumb img {
  border-radius: 7.5px;
  width: 70%;
}

/* section.whats-trending .right-image::after {
  width: 440px;
  height: 300px;
  right: -440px;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  position: absolute;
  background: url(../images/stamp2_.png);
  content: '';
} */

/*** Catgories image slider ***/
.categories-list .slider-content {
  min-height: 100%;
  max-height: 100%;
  position: relative;
  margin-top: -10px;
  background-color: #fff;
  border-radius: 23px;
  padding: 35px;
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.15);
}

.categories-list .slider-content h2,
.related-list .slider-content h2 {
  font-size: 30px;
  margin-bottom: 30px;
}

.categories-list .slider-content h2 em,
.related-list .slider-content h2 em {
  font-style: normal;
  color: var(--default-theme);
}

.categories-list .slider-content .item .thumb,
.related-list .slider-content .item .thumb {
  position: relative;
  border-radius: 10px;
  overflow: hidden;
  background-color: #000;
}

.categories-list .slider-content .item .thumb .overlay,
.related-list .slider-content .item .thumb .overlay {
  height: 100%;
  width: 100%;
  opacity: 1;
  transition: 0.5s ease;
  background-color: #000;
}

.categories-list .slider-content .item:hover .thumb .overlay,
.related-list .slider-content .item:hover .thumb .overlay {
  opacity: 0.5;
}

.categories-list .slider-content .item .thumb h4,
.related-list .slider-content .item .thumb h4 {
  position: absolute;
  left: 20px;
  bottom: 15px;
  font-size: 18px;
  color: #fff;
}

.categories-list .owl-nav,
.collections-section .owl-nav,
.related-list .owl-nav {
  position: absolute;
  top: -60px;
  right: 0;
}

.categories-list .owl-nav .owl-next span,
.collections-section .owl-nav .owl-next span,
.related-list .owl-nav .owl-next span {
  display: none;
}

.categories-list .owl-nav .owl-next::after,
.collections-section .owl-nav .owl-next::after,
.related-list .owl-nav .owl-next::after {
  font-family: "FontAwesome";
  content: "\f061";
  color: #cdcdcd;
  font-size: 20px;
  transition: all 0.3s;
}

.categories-list .owl-nav .owl-prev span,
.collections-section .owl-nav .owl-prev span,
.related-list .owl-nav .owl-prev span {
  display: none;
}

.categories-list .owl-nav .owl-prev::after,
.collections-section .owl-nav .owl-prev::after,
.related-list .owl-nav .owl-prev::after {
  font-family: "FontAwesome";
  content: "\f060";
  color: #cdcdcd;
  margin-right: 15px;
  font-size: 20px;
  transition: all 0.3s;
}

.categories-list .owl-nav .owl-prev:hover::after,
.categories-list .owl-nav .owl-next:hover::after,
.related-list .owl-nav .owl-prev:hover::after,
.related-list .owl-nav .owl-next:hover::after,
.collections-section .owl-nav .owl-prev:hover::after,
.collections-section .owl-nav .owl-next:hover::after {
  color: var(--default-theme);
}
/*** Catgories image slider end ***/
section.categories-slide {
  margin-top: 60px;
  padding: 60px 0px;
  background-image: url(../images/contact-us-bg.jpg);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
}

@media (min-width: 992px) {
  section.categories-slide {
    margin-top: 120px;
    padding: 120px 0px;
  }
}

section.categories-slide .left-form {
  padding-right: 15px;
}

@media (min-width: 992px) {
  section.categories-slide .left-form {
    padding-right: 0px;
  }
}

section.categories-slide .right-map {
  padding-left: 15px;
}

@media (min-width: 992px) {
  section.categories-slide .right-map {
    padding-left: 0px;
  }
}

section.categories-slide form {
  padding: 30px;
  border-top-right-radius: 7.5px;
  border-bottom-right-radius: 7.5px;
  background-color: #ffffff;
  border-top-left-radius: 7.5px;
  border-bottom-left-radius: 7.5px;
  margin-bottom: 0px;
}

@media (min-width: 992px) {
  section.categories-slide form {
    padding: 60px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
  }
}

section.categories-slide form input {
  width: 100%;
  height: 44px;
  border-radius: 7.5px;
  border: 1px solid #eeeeee;
  font-size: 14px;
  padding: 0px 15px;
  margin-bottom: 15px;
}

section.categories-slide form textarea {
  width: 100%;
  max-width: 100%;
  min-width: 100%;
  min-height: 120px;
  height: 120px;
  max-height: 120px;
  border-radius: 7.5px;
  border: 1px solid #eeeeee;
  font-size: 14px;
  padding: 10px 15px;
  margin-bottom: 15px;
}

section.categories-slide form button.main-button {
  width: 100%;
}

section.categories-slide form .login-signup {
  margin-top: 30px;
  text-align: center;
}

section.categories-slide #map iframe {
  border-top-right-radius: 77.5px;
  border-bottom-right-radius: 7.5px;
  border-top-left-radius: 7.5px;
  border-bottom-left-radius: 7.5px;
  margin-top: 30px;
}

@media (min-width: 992px) {
  section.categories-slide #map iframe {
    margin-top: 0px;
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
  }
}

/** Collections section starts**/
section.collections-section .collection-content {
  margin-left: 0;
  margin-top: 30px;
  margin-bottom: 50px;
}

@media (min-width: 992px) {
  section.collections-section .collection-content {
    margin-left: 50px;
    margin-top: 0px;
    margin-bottom: 50px;
  }
}

img.collection-img {
  max-width: 100%;
  vertical-align: middle;
  display: inline-block;
}

/* Main CSS */
.grid-wrapper > div {
  display: flex;
  justify-content: center;
  align-items: center;
}
.grid-wrapper > div > img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 5px;
}

.grid-wrapper {
  display: grid;
  grid-gap: 5px;
  grid-template-columns: repeat(2, minmax(150px, 1fr));
  grid-auto-rows: 150px;
  grid-auto-flow: dense;
}
.grid-wrapper .wide {
  grid-column: span 2;
}
.grid-wrapper .tall {
  grid-row: span 2;
}
.grid-wrapper .big {
  grid-column: span 2;
  grid-row: span 2;
}

.collections-card {
  /*box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);*/
  transition: 0.3s;
  width: fit-content;
  border: none;
  border-radius: 5px;
}

.collections-info:hover {
  /*box-shadow: 0 8px 16px 0 rgba(0,0,0,0.2);*/
  box-shadow: 0px 8px 12px 6px rgba(0, 0, 0, 0.2);
}

.collections-info {
  padding: 2px 16px;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
  transition: 0.3s;
  border-radius: 5px;
  margin-top: 5px;
  grid-column: span 2;
  justify-content: flex-start !important;
}
/* @media only screen and (max-width: 778px) {
	.collections-info {

    }
} */

.collection-name,
.curator {
  margin: 5px;
}
/** Collections section ends**/

/** scroll nav start **/
.homepage-floating-menu {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--default-theme);
  position: fixed;
  bottom: 35px;
  right: 35px;
  z-index: 22222;
}
.homepage-floating-menu input {
  position: absolute;
  width: 100% !important;
  height: 100% !important;
  margin: 0;
  opacity: 0;
  cursor: pointer;
}
.homepage-floating-menu input:checked ~ .icon-compass {
  transform: rotate(135deg);
}
.homepage-floating-menu input:checked ~ .homepage-floating-nav li:nth-child(1) {
  transform: translatey(-48px);
}
.homepage-floating-menu input:checked ~ .homepage-floating-nav li:nth-child(2) {
  transform: translatey(-88px);
}
.homepage-floating-menu input:checked ~ .homepage-floating-nav li:nth-child(3) {
  transform: translatey(-128px);
}
.homepage-floating-menu input:checked ~ .homepage-floating-nav li:nth-child(4) {
  transform: translatey(-168px);
}
.homepage-floating-menu input ~ .plus-icon {
  transform: rotate(0);
}
.homepage-floating-menu > .plus-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  font-size: 24px;
  line-height: 1;
  color: #fff;
  background: var(--default-theme);
  border-radius: 50%;
  transform: rotate(0);
  transition: all 0.5s ease;
  pointer-events: none;
  position: absolute;
  box-shadow: 1px 1px 3px 1px rgba(0, 0, 0, 0.2);
}
.homepage-floating-menu > .homepage-floating-nav {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  margin: 0;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
}
.homepage-floating-menu > .homepage-floating-nav li {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: var(--default-theme);
  cursor: pointer;
  position: absolute;
  transition: all 0.5s ease;
}
.homepage-floating-menu > .homepage-floating-nav li span {
  font-size: 14px;
  line-height: 1;
  color: #fff;
}
.homepage-floating-menu
  input:checked
  ~ .homepage-floating-nav
  li:nth-child(1):hover {
  transform: translate(-6px, -48px) !important;
}
.homepage-floating-menu
  input:checked
  ~ .homepage-floating-nav
  li:nth-child(2):hover {
  transform: translate(-6px, -88px) !important;
}
.homepage-floating-menu
  input:checked
  ~ .homepage-floating-nav
  li:nth-child(3):hover {
  transform: translate(-6px, -128px) !important;
}
.homepage-floating-menu
  input:checked
  ~ .homepage-floating-nav
  li:nth-child(4):hover {
  transform: translate(-6px, -168px) !important;
}
/*=-=-=-=-=-=-=-=-=-=-=-=-=-=-= */
/*=-=-=-=-=-=-=-=-=-=-=-=-=-=-= */
/*=-=-=-=-=-=-=-=-=-=-=-=-=-=-= */
/*=-=-=-=-=-=-=-=-=-=-=-=-=-=-= */
/*=-=-=-=-=-=-=-=-=-=-=-=-=-=-= */
/*=-=-=-=-=-=-=-=-=-=-=-=-=-=-= */
.vertical-floating-menu {
  margin-left: 30%;
  margin-top: 10%;
}
/*tooltip Box*/
.con-tooltip {
  position: relative;
  border-radius: 9px;
  padding: 0 5px;
  display: inline-block;
  transition: all 0.3s ease-in-out;
  cursor: default;
}
/*tooltip */
.homepage-tooltip {
  visibility: hidden;
  z-index: 1;
  opacity: 0.9;
  width: 100px;
  /* padding: 0px 20px; */
  text-align: center;
  background: #fff;
  color: var(--default-theme);
  position: absolute;
  top: -140%;
  left: -25%;
  border-radius: 9px;
  font-size: 14px;
  transform: translateY(9px);
  transition: all 0.3s ease-in-out;
  box-shadow: 0 0 3px rgba(56, 54, 54, 0.86);
}
.homepage-tooltip p {
  font-size: 12px;
  line-height: 15px;
  color: #7a7a7a;
}
/* tooltip after*/
.homepage-tooltip::after {
  content: " ";
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 12px 12.5px 0 12.5px;
  border-color: #808080 transparent transparent transparent;
  position: absolute;
  left: 40%;
}
.con-tooltip:hover .homepage-tooltip {
  visibility: visible;
  transform: translateY(-10px);
  opacity: 1;
  transition: 0.3s linear;
  animation: odsoky 1s ease-in-out infinite alternate;
}
@keyframes odsoky {
  0% {
    transform: translateY(6px);
  }
  100% {
    transform: translateY(1px);
  }
}
/*hover ToolTip*/
/*left*/
.left .homepage-tooltip {
  top: -30%;
  left: -410%;
}
.left .homepage-tooltip::after {
  top: 30%;
  left: 94%;
  transform: rotate(-90deg);
}

/** scroll nav end **/
ul {
  margin: 0px;
  padding: 0px;
}
.footer-section {
  background: rgb(42, 42, 42);
  position: relative;
}
.footer-cta {
  border-bottom: 1px solid #373636;
}
.single-cta i {
  color: rgb(255, 86, 91);
  font-size: 30px;
  float: left;
  margin-top: 8px;
}
.cta-text {
  padding-left: 15px;
  display: inline-block;
}
.cta-text h4 {
  color: #fff;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 2px;
}
.cta-text span,
.cta-text span a,
.cta-text a {
  color: #c9c5c5 !important;
  font-size: 15px;
}
.footer-content {
  position: relative;
  z-index: 2;
}
.footer-pattern img {
  position: absolute;
  top: 0;
  left: 0;
  height: 330px;
  background-size: cover;
  background-position: 100% 100%;
}
.footer-logo {
  margin-bottom: 30px;
}
.footer-logo img {
  max-width: 200px;
}
.footer-text p {
  margin-bottom: 14px;
  font-size: 14px;
  color: #c9c5c5;
  line-height: 28px;
}
.footer-social-icon span {
  color: #fff;
  display: block;
  font-size: 20px;
  font-weight: 700;
  font-family: "Poppins", sans-serif;
  margin-bottom: 20px;
}
.footer-social-icon a {
  color: #fff;
  font-size: 16px;
  margin-right: 15px;
}
.footer-social-icon i {
  height: 40px;
  width: 40px;
  text-align: center;
  line-height: 38px;
  border-radius: 50%;
}
.facebook-bg {
  background: #3b5998;
}
.twitter-bg {
  background: #55acee;
}
.google-bg {
  background: #dd4b39;
}
.footer-widget-heading h3 {
  color: #fff;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 40px;
  position: relative;
}
.footer-widget-heading h3::before {
  content: "";
  position: absolute;
  left: 0;
  bottom: -15px;
  height: 2px;
  width: 50px;
  background: rgb(255, 86, 91);
}
.footer-widget ul li {
  display: inline-block;
  float: left;
  width: 50%;
  margin-bottom: 12px;
}
.footer-widget ul li a:hover {
  color: rgb(255, 86, 91);
}
.footer-widget ul li a {
  color: #c9c5c5;
  text-transform: capitalize;
}
.subscribe-form {
  position: relative;
  overflow: hidden;
}
.subscribe-form input {
  width: 100%;
  padding: 14px 28px;
  background: #2e2e2e;
  border: 1px solid #2e2e2e;
  color: #fff;
}
.subscribe-form button {
  position: absolute;
  right: 0;
  background: rgb(255, 86, 91);
  padding: 13px 20px;
  border: 1px solid rgb(255, 86, 91);
  top: 0;
}
.subscribe-form button i {
  color: #fff;
  font-size: 22px;
  transform: rotate(-6deg);
}
.copyright-area {
  background: #202020;
  padding: 25px 0;
}
.copyright-text p {
  margin: 0;
  font-size: 14px;
  color: #878787;
}
.copyright-text p a {
  color: rgb(255, 86, 91);
}
.footer-menu li {
  display: inline-block;
  margin-left: 20px;
}
.footer-menu li:hover a {
  color: rgb(255, 86, 91);
}
.footer-menu li a {
  font-size: 14px;
  color: #878787;
}
.interact-btn__modal .modal-header {
  border-bottom: none;
}
.interact-btn__modal .modal-body {
  padding: 10px;
}
.interact-btn__modal .modal-body .collections-body {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
  gap: 40px;
}
.collections-body .collections-img {
  flex-grow: 2;
  display: flex;
  justify-content: center;
}
.img-details__collection .collections-img__container {
  margin-top: 32px;
  margin-bottom: 20px;
  width: 300px;
  height: auto;
  object-fit: contain;
  background-color: #ddd;
}
.collections-img .img-details__collection p {
  margin: 0px;
  font-size: 13px;
}
.collections-body .collections-input {
  flex-grow: 3;
}
.collections-input .old-collection h5 {
  margin-top: 24px;
  margin-bottom: 18px;
}
.old-collection__list .list-group li input {
  padding: 12px;
  border: 1px solid var(--default-theme);
  background-color: var(--default-theme);
  border-radius: 50px;
}
.mt-32 {
  margin-top: 32px;
}
.font-12 {
  font-size: 12px;
}
.font-em-09 {
  font-size: 0.9em;
}
.italics {
  font-style: italic !important;
}
.bolden {
  font-weight: bolder !important;
}
/** Photos.php collection modal end **/

@media (min-width: 992px) {
  footer .sub-footer a.scroll-to-top {
    text-align: right;
    color: #7a7a7a;
  }
}

@media (min-width: 992px) {
  footer .sub-footer2 a.scroll-to-top {
    text-align: right;
    color: #7a7a7a;
  }
}

footer .sub-footer a.scroll-to-top svg {
  margin-left: 3px;
}

footer .sub-footer a.scroll-to-top:hover {
  color: var(--default-theme);
}

footer .sub-footer2 a.scroll-to-top svg {
  margin-left: 3px;
}

footer .sub-footer2 a.scroll-to-top:hover {
  color: var(--default-theme);
}

.page-banner {
  background-color: #333;
  background-image: url(../images/page-banner-bg.jpg);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
  padding: 100px 0px 90px 0px;
  text-align: center;
}

.page-banner2 {
  background-color: #333;
  background-image: url(../images/page-banner-bg.jpg);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
  padding: 100px 0px 90px 0px;
  text-align: center;
}

@media (min-width: 768px) {
  .page-banner {
    padding: 100px 0px;
  }
}

@media (min-width: 768px) {
  .page-banner2 {
    padding: 180px 0px;
  }
}

.page-banner h2 {
  color: #ffffff;
  font-size: 36px;
  font-weight: 700;
  text-transform: uppercase;
  margin-top: 15px;
  margin-bottom: 25px;
}

.page-banner2 h2 {
  color: #ffffff;
  font-size: 36px;
  font-weight: 700;
  text-transform: uppercase;
  margin-top: 15px;
  margin-bottom: 25px;
}

@media (min-width: 768px) {
  .page-banner h2 {
    font-size: 45px;
  }
}

@media (min-width: 768px) {
  .page-banner2 h2 {
    font-size: 50px;
  }
}

.page-banner h2 em {
  font-style: normal;
  color: var(--default-theme);
}

.page-banner p em {
  font-style: normal;
  color: var(--default-theme);
}

.page-banner2 h2 em {
  font-style: normal;
  color: var(--default-theme);
}

.page-banner p {
  color: #ffffff;
}

.page-banner2 p {
  color: #ffffff;
}

.services {
  margin-top: -85px;
}

.services .service-item {
  padding: 30px;
  text-align: center;
  background-color: #ffffff;
  -webkit-box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.15);
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.15);
  border-radius: 7.5px;
  margin-bottom: 15px;
}

@media (min-width: 992px) {
  .services .service-item {
    margin-bottom: 0px;
  }
}

.services .service-item .icon {
  fill: var(--default-theme);
}

.services .service-item h4 {
  margin-top: 15px;
  font-size: 20px;
  font-weight: 700;
  color: #2a2a2a;
}

section.interior-design .left-image {
  margin-right: 0;
  /* height: 50rem; */
  /* width: 100%; */
}

@media (min-width: 992px) {
  section.interior-design .left-image {
    margin-right: 50px;
  }
}

section.interior-design .left-image img {
  border-radius: 7.5px;
  height: 100%;

  object-fit: cover;
}
.spimage {
  max-height: 400px;
  width: 700px;
}

section.interior-design .right-content {
  margin-left: 0;
  margin-top: 30px;
}

@media (min-width: 992px) {
  section.interior-design .right-content {
    margin-left: 50px;
    margin-top: 0px;
  }
}

section.interior-design .right-content h4 {
  margin-top: 50px;
  margin-bottom: 0px;
  font-size: 20px;
  font-weight: 700;
  color: #2a2a2a;
}

section.interior-design .right-content .accordion-button {
  padding: 30px 0px;
  border-bottom: 1px solid #eeeeee;
  border-left: none;
  border-right: none;
  border-top: none;
  width: 100%;
  outline: none;
  text-align: left;
  font-size: 17px;
  background-color: transparent;
  color: #2a2a2a;
}

section.interior-design .right-content .accordion-button:not(.collapsed) {
  color: #2a2a2a;
  border-bottom: none;
  background-color: transparent;
}

section.interior-design .right-content .accordion-button:focus {
  border: none;
  z-index: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}

section.interior-design
  .right-content
  .accordion-button:not(.collapsed)::after {
  background-image: auto !important;
}

section.interior-design .right-content .accordion-button::after {
  color: #2a2a2a;
}

section.interior-design .right-content .accordion-collapse {
  border: none;
}

section.interior-design .right-content .accordion-body {
  padding: 0px 0px 30px 0px;
  border-bottom: 1px solid #eeeeee;
  border-left: none;
  border-right: none;
  border-top: none;
}

.what-they-say .testimonials {
  background-image: url(../images/testimonial-bg.png);
  background-repeat: no-repeat;
  background-size: 815px 560px;
  background-position: center center;
  border-radius: 7.5px;
}

@media (min-width: 992px) {
  .what-they-say .testimonials {
    background-position: center right;
  }
}

.what-they-say .col-lg-6 {
  padding: 0px;
}

.what-they-say .carousel {
  padding: 100px 0px;
}

.what-they-say .testimonial-item {
  padding: 60px;
  margin: 20px;
  z-index: 1;
  -webkit-box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.15);
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.15);
  background-color: #ffffff;
  border-radius: 7.5px;
  border-bottom-right-radius: 0px;
  position: relative;
}

.what-they-say .testimonial-item img {
  max-width: 80px;
  border-radius: 50%;
  margin-right: 20px;
  float: left !important;
}

.what-they-say .testimonial-item .icon {
  position: absolute;
  left: 30px;
  top: -20px;
  fill: var(--default-theme);
}

.what-they-say .testimonial-item h4 {
  font-size: 18px;
  font-weight: 700;
  color: #2a2a2a;
}

.what-they-say .testimonial-item span {
  font-size: 14px;
  color: #7a7a7a;
  display: block;
  margin-bottom: 8px;
}

.what-they-say .testimonial-item p {
  margin-top: 25px;
  margin-bottom: 0px;
}

.what-they-say .testimonial-item:after {
  content: "";
  position: absolute;
  right: -10px;
  bottom: -10px;
  width: 100%;
  height: 10px;
  border-bottom-left-radius: 7.5px;
  border-bottom-right-radius: 7.5px;
  z-index: -2;
  background-color: #f6c5c6;
}

.what-they-say .testimonial-item:before {
  content: "";
  position: absolute;
  right: -10px;
  bottom: -5px;
  width: 10px;
  height: 100%;
  border-top-right-radius: 7.5px;
  border-bottom-right-radius: 7.5px;
  z-index: -2;
  background-color: #f6c5c6;
}

.what-they-say .carousel-indicators {
  bottom: 40px;
}

.what-they-say .carousel-indicators button {
  width: 10px;
  height: 10px;
  background-color: #7a7a7a;
  -webkit-box-shadow: none;
  box-shadow: none;
  border: none;
  outline: none;
  opacity: 0.5;
  margin: 0px 5px;
  padding: 0px;
  border-radius: 50%;
}

.what-they-say .carousel-indicators .active {
  opacity: 1;
  background-color: var(--default-theme);
}

.call-to-action {
  background-color: #2a2a2a;
  /* margin-top: 120px; */
  padding: 45px 0px;
}

.call-to-action h2 {
  font-size: 24px;
  font-weight: 700;
  text-transform: uppercase;
  color: #ffffff;
  margin: 0px 0px 20px 0px;
  text-align: center;
}

@media (min-width: 992px) {
  .call-to-action h2 {
    text-align: left;
    margin: 0px;
    font-size: 35px;
  }
}

.call-to-action .default-button {
  text-align: center;
}

.call-to-action .white-button {
  text-align: center;
}

@media (min-width: 992px) {
  .call-to-action .white-button {
    text-align: right;
  }

  .call-to-action .default-button {
    text-align: right;
  }
}

section.explore-item h2 {
  font-size: 34px;
  font-weight: 700;
  color: #2a2a2a;
  text-align: center;
  text-transform: uppercase;
}

section.explore-item .main-image img {
  border-radius: 7.5px;
}

section.explore-item .project-info {
  background-color: #fafafa;
  -webkit-box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.15);
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.15);
  margin: 15px 0px 30px 0px;
  border-radius: 7.5px;
  position: relative;
  padding: 30px;
}

@media (min-width: 992px) {
  section.explore-item .project-info {
    margin: -140px 30px 80px 30px;
    background-color: rgba(250, 250, 250, 0.9);
    -webkit-box-shadow: none;
    box-shadow: none;
  }
}

section.explore-item .project-info .info-item {
  margin: 10px auto;
  text-align: center;
}

@media (min-width: 992px) {
  section.explore-item .project-info .info-item {
    margin: 0 auto;
  }
}

section.explore-item .project-info .info-item h6 {
  font-size: 17px;
  color: #2a2a2a;
}

section.explore-item .project-info .info-item span {
  font-size: 15px;
  color: #7a7a7a;
}

section.explore-item p {
  margin-bottom: 60px;
}

section.explore-item img {
  border-radius: 7.5px;
  margin-bottom: 15px;
}

@media (min-width: 992px) {
  section.explore-item img {
    margin-bottom: 0px;
  }
}

section.explore-item .down-content h4 {
  margin-top: 50px;
  margin-bottom: 20px;
  font-size: 34px;
  font-weight: 700;
  color: #2a2a2a;
  text-align: left;
  text-transform: uppercase;
}

section.explore-item .projects-pagination {
  border-top: 1px solid #eeeeee;
  padding-top: 60px;
}

section.explore-item .projects-pagination img {
  max-width: 60px;
}

@media (min-width: 992px) {
  section.explore-item .projects-pagination img {
    max-width: 115px;
  }
}

section.explore-item .projects-pagination .right-pagination {
  margin-top: 15px;
  padding-top: 30px;
  border-top: 1px solid #eeeeee;
  text-align: right;
}

@media (min-width: 992px) {
  section.explore-item .projects-pagination .right-pagination {
    margin-top: 0px;
    padding-top: 0px;
    border-top: none;
  }
}

section.explore-item .projects-pagination .right-pagination img {
  float: right;
}

section.explore-item .projects-pagination .left-pagination img {
  float: left;
}

section.explore-item .projects-pagination .right-content {
  display: inline-block;
  margin-left: 15px;
}

@media (min-width: 992px) {
  section.explore-item .projects-pagination .right-content {
    margin-left: 25px;
  }
}

section.explore-item .projects-pagination .right-content h6 {
  margin-top: 8px;
  font-size: 18px;
  font-weight: 700;
  color: #2a2a2a;
  text-transform: capitalize;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}

@media (min-width: 992px) {
  section.explore-item .projects-pagination .right-content h6 {
    margin-top: 28px;
  }
}

section.explore-item .projects-pagination .right-content h6:hover {
  color: var(--default-theme);
}

section.explore-item .projects-pagination .right-content span {
  font-size: 15px;
  color: #7a7a7a;
}

section.explore-item .projects-pagination .left-content {
  margin-right: 15px;
  display: inline-block;
  text-align: right;
}

@media (min-width: 992px) {
  section.explore-item .projects-pagination .left-content {
    margin-right: 25px;
  }
}

section.explore-item .projects-pagination .left-content h6 {
  margin-top: 8px;
  font-size: 18px;
  font-weight: 700;
  color: #2a2a2a;
  text-transform: capitalize;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
  text-align: right;
}

@media (min-width: 992px) {
  section.explore-item .projects-pagination .left-content h6 {
    padding-top: 20px;
  }
}

section.explore-item .projects-pagination .left-content h6:hover {
  color: var(--default-theme);
}

section.explore-item .projects-pagination .left-content span {
  text-align: right;
  font-size: 15px;
  color: #7a7a7a;
}

section.trending-page .card {
  border: none;
}

@media (min-width: 650px) {
  section.trending-page .grid-item {
    width: 100%;
  }
}

@media (min-width: 850px) {
  section.trending-page .grid-item {
    width: 50%;
  }
}

@media (min-width: 1200px) {
  section.trending-page .grid-item {
    width: 33%;
  }
}

@media (min-width: 1400px) {
  section.trending-page .grid-item {
    width: 25%;
  }
}

section.trending-page .trending-item {
  margin-bottom: 20px;
}

@media (min-width: 1200px) {
  section.trending-page .trending-item {
    margin-bottom: 30px;
  }
}

section.trending-page .trending-item .thumb {
  border-radius: 7.5px;
  position: relative;
  overflow: hidden;
}

section.trending-page .trending-item .thumb span.banner {
  background-color: var(--default-theme);
  color: #ffffff;
  font-size: 14px;
  padding: 10px 16px;
  display: inline-block;
  border-bottom-right-radius: 7.5px;
  border-top-left-radius: 7.5px;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 2;
}

section.trending-page .trending-item .thumb .hover-effect {
  position: absolute;
  z-index: 2;
  width: 100%;
  bottom: -100%;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}

section.trending-page .trending-item .thumb .hover-effect .inner-content {
  padding: 20px 30px;
  border-bottom-left-radius: 7.5px;
  border-bottom-right-radius: 7.5px;
  background-color: var(--default-theme);
}

section.trending-page .trending-item .thumb .hover-effect .inner-content h4 {
  font-size: 18px;
  font-weight: 700;
  text-transform: capitalize;
  color: #ffffff;
  margin: 0px;
  margin-right: 20px;
}

section.trending-page
  .trending-item
  .thumb
  .hover-effect
  .inner-content
  a.icon {
  color: #ffffff;
  right: 30px;
  bottom: 18px;
  position: absolute;
}

section.trending-page .trending-item .thumb img {
  border-radius: 7.5px;
  -webkit-transition: all 1s;
  transition: all 1s;
  z-index: 1;
}

section.trending-page .trending-item .thumb:hover img {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}

section.trending-page .trending-item .thumb:hover .hover-effect {
  bottom: 0;
}

section.contact-page-map #map iframe {
  border-radius: 7.5px;
}

section.contact-us-page {
  margin-top: -60px;
  padding-top: 0px;
  z-index: 2;
  position: relative;
}

section.contact-us-page .contact-page-form {
  padding: 30px;
  background-color: #ffffff;
  -webkit-box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.15);
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.15);
  border-radius: 7.5px;
}

/*@media (min-width: 992px) {
  section.contact-us-page .contact-page-form {
    padding: 60px;
  }
}

section.contact-us-page .contact-page-form form {
  margin-bottom: 0px;
  margin-right: 0px;
}

@media (min-width: 992px) {
  section.contact-us-page .contact-page-form form {
    margin-right: 45px;
  }
}

section.contact-us-page .contact-page-form form input {
  width: 77%;
  height: 44px;
  border-radius: 7.5px;
  border: 1px solid #eeeeee;
  font-size: 14px;
  padding: 0px 15px;
  margin-bottom: 15px;
  margin-left: 38px;
}

section.contact-us-page .contact-page-form form textarea {
  width: 100%;
  max-width: 100%;
  min-width: 100%;
  min-height: 120px;
  height: 120px;
  max-height: 120px;
  border-radius: 7.5px;
  border: 1px solid #eeeeee;
  font-size: 14px;
  padding: 10px 15px;
  margin-bottom: 15px;
}

section.contact-us-page .contact-page-form form button.main-button {
  width: 100%;
}*/

section.contact-us-page .right-info ul {
  margin-top: 30px;
  padding-top: 30px;
  border-top: 1px solid #eeeeee;
}

@media (min-width: 992px) {
  section.contact-us-page .right-info ul {
    margin-top: 0px;
    padding-top: 0px;
    border-top: none;
  }
}

section.contact-us-page .right-info ul li {
  display: block;
  width: 100%;
  padding: 30px;
  border-radius: 7.5px;
  background-color: var(--default-theme);
  text-align: center;
  margin-bottom: 30px;
}

section.contact-us-page .right-info ul li:last-child {
  margin-bottom: 0px;
}

section.contact-us-page .right-info ul li .icon {
  fill: #ffffff;
}

section.contact-us-page .right-info ul li h6 {
  margin-top: 15px;
  font-size: 18px;
  text-transform: capitalize;
  font-weight: 700;
  color: #ffffff;
}

section.contact-us-page .right-info ul li span {
  font-size: 15px;
  color: #ffffff;
}

/*# sourceMappingURL=style.css.map */
.hidden {
  display: none;
}
.photos_modal .modal-content .modal-body {
  display: flex;
  justify-content: space-between;
}
.photos_modal .modal-content p {
  color: #000;
}
.photos_modal .modal-dialog {
  max-width: 210px;
}
#add_to_cart {
  z-index: 3800;
}

/* Default Card Selection Modal Styles */
#default_card_selection_table .table-success {
  background-color: rgba(25, 135, 84, 0.1);
}

#default_card_selection_table .badge {
  font-size: 0.75rem;
  padding: 0.35em 0.65em;
}

#default_card_selection_table .set-default-card-btn {
  transition: all 0.2s ease;
}

#default_card_selection_table .set-default-card-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Card loader styles */
#card-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  display: none;
}

#card-loader:after {
  content: "";
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Subscription plan button styles */
.pricingTable-firstTable_table__getstart {
  position: relative;
  display: block;
  margin: 0 auto;
  padding: 10px;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 5px;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pricingTable-firstTable_table__getstart:not(.disabled-btn):hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}

.pricingTable-firstTable_table__getstart.disabled-btn {
  border-color: #6c757d !important;
  cursor: not-allowed;
  opacity: 0.65;
}

/* Add a checkmark icon to subscribed plans */
.pricingTable-firstTable_table__getstart.disabled-btn::before {
  content: "✓ ";
  font-weight: bold;
}
/* Subscribed plan indicator */
.subscribed-plan {
  color: #8dd69e;
  align-items: center;
}

.subscribed-plan::before {
  content: "✓";
  display: inline-block;
  margin-right: 5px;
  font-weight: bold;
  color: #8dd69e;
}
