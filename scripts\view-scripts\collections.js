$(document).ready(function(){
    // First get the user session
    $.ajax({
        url: 'scripts/data-scripts/get_session.php',
        method: 'GET',
        dataType: 'json',
        success: function(response) {
            console.log('Session response:', response);
            fetchCollections();
        },
        error: function(xhr, status, error) {
            console.error('Session fetch failed:', error);
            fetchCollections();
        }
    });

    function fetchCollections() {
        $.ajax({
            url: 'scripts/data-scripts/collections.data.php',
            method: 'POST',
            data: { action: 'fetch' },
            dataType: 'json',
            success: function(response) {
                console.log('Collections response:', response);
                if (response.success && response.data) {
                    renderCollections(response.data.public || []);
                } else {
                    console.error('Failed to fetch collections:', response.message);
                    showErrorMessage('Unable to load collections');
                }
            },
            error: function(xhr, status, error) {
                console.error('Collection fetch failed:', error);
                showErrorMessage('Network error occurred');
            }
        });
    }

    function createCollectionCard(collection) {
        return `
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="collection-card">
                    <div class="collection-images">
                        ${createImageGrid(collection.images)}
                    </div>
                    <div class="card-body">
                        <h5 class="card-title">${collection.collection_title}</h5>
                        <div class="collection-stats">
                            <span class="image-count">
                                <i class="fas fa-images"></i> ${collection.image_count} photos
                            </span>
                        </div>
                        <div class="card-footer">
                            <small class="text-muted">
                                <i class="fas fa-user"></i> ${collection.user_name}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    function createImageGrid(images) {
        if (!images || images.length === 0) {
            return `<img src="assets/images/placeholder.jpg" class="card-img-top" alt="No images">`;
        }

        // Show up to 4 images in grid
        const displayImages = images.slice(0, 4);
        const gridClass = displayImages.length > 1 ? 'image-grid' : '';
        
        return `
            <div class="${gridClass}">
                ${displayImages.map(img => `
                    <div class="grid-item">
                        <img src="${img.thumbnail}" alt="${img.title || 'Collection image'}" loading="lazy">
                    </div>
                `).join('')}
            </div>
        `;
    }

    function renderCollections(collections = []) {
        const container = $('#public-collections');
        
        container.html(
            collections.length > 0
                ? collections.map(collection => createCollectionCard(collection)).join('')
                : '<div class="col-12"><p class="text-center text-muted">No collections found</p></div>'
        );

        updateCollectionsCount(collections.length);
    }

    function showErrorMessage(message) {
        // Add this HTML to your page for the error message
        const errorDiv = $('#error-message');
        if (errorDiv.length) {
            errorDiv.text(message).fadeIn().delay(3000).fadeOut();
        }
    }

    function updateCollectionsCount(count) {
        // Update the collections count in the header
        $('.header-text h2').html(`Collect<em>ions</em> (${count})`);
    }
    function updateCreateButton(userId) {
        const createBtn = $('#createCollectionBtn');
        if (!userId) {
            createBtn.hide();
        } else {
            createBtn.show();
        }
    }

    // Create Collection Button Click Handler
    $('#createCollectionBtn').click(function() {
        if (!userId) {
            window.location.href = 'login.php';
            return;
        }
        $('#createCollectionModal').modal('show');
    });

    // Save Collection Handler
    $('#saveCollection').click(function() {
        const title = $('#collectionTitle').val().trim();
        const type = $('#collectionType').val();

        if (!title) {
            showErrorMessage('Please enter a collection title');
            return;
        }

       $.ajax({
        url: 'scripts/data-scripts/collections.data.php',
        method: 'POST',
        data: {
            action: 'create',
            userId: userId,
            title: title,
            type: type
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                $('#createCollectionModal').modal('hide');
                $('#createCollectionForm')[0].reset();
                fetchCollections(userId); // Refresh collections
                showSuccessMessage('Collection created successfully');
            } else {
                showErrorMessage(response.message || 'Failed to create collection');
            }
        },
        error: function(xhr, status, error) {
            showErrorMessage('Error creating collection');
            console.error('Create collection error:', error);
        }
    });
    });

    function showSuccessMessage(message) {
        const errorDiv = $('#error-message');
        errorDiv.removeClass('alert-danger').addClass('alert-success')
            .text(message).fadeIn().delay(3000).fadeOut();
    }
});