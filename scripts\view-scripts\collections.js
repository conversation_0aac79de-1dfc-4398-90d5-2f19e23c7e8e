$(document).ready(function(){
    // Initialize session variables
    let userId = null;
    
    // First get the user session
    $.ajax({
        url: 'scripts/data-scripts/get_session.php',
        method: 'GET',
        dataType: 'json',
        success: function(response) {
            console.log(response);
            if (response.success) {
                userId = response.userId;
                fetchCollections();
            } 
        },
        error: function(xhr, status, error) {
            console.error('Session fetch failed:', error);
        }
    });

    function fetchCollections() {
        $.ajax({
            url: 'scripts/data-scripts/collections.data.php',
            method: 'POST',
            data: { userId: userId },
            dataType: 'json',
            success: function(response) {
                if (response.success && response.data) {
                    console.log('Collections fetched successfully:', response);
                    renderCollections(response.data.private, response.data.public);
                } else {
                    console.error('Failed to fetch collections:', response.message);
                    // Show error message to user
                    showErrorMessage('Unable to load collections');
                }
            },
            error: function(xhr, status, error) {
                console.error('Collection fetch failed:', error);
                showErrorMessage('Network error occurred');
            }
        });
    }

    function createCollectionCard(collection) {
        return `
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="collection-card">
                    <div class="collection-images">
                        ${createImageGrid(collection.images)}
                    </div>
                    <div class="card-body">
                        <h5 class="card-title">${collection.collection_title}</h5>
                        <div class="collection-stats">
                            <span class="image-count">
                                <i class="fas fa-images"></i> ${collection.image_count} photos
                            </span>
                        </div>
                        <div class="card-footer">
                            <small class="text-muted">
                                <i class="fas fa-user"></i> ${collection.user_name}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    function createImageGrid(images) {
        if (!images || images.length === 0) {
            return `<img src="assets/images/placeholder.jpg" class="card-img-top" alt="No images">`;
        }

        // Show up to 4 images in grid
        const displayImages = images.slice(0, 4);
        const gridClass = displayImages.length > 1 ? 'image-grid' : '';
        
        return `
            <div class="${gridClass}">
                ${displayImages.map(img => `
                    <div class="grid-item">
                        <img src="${img.thumbnail}" alt="${img.title || 'Collection image'}" loading="lazy">
                    </div>
                `).join('')}
            </div>
        `;
    }

    function renderCollections(privateCollections = [], publicCollections = []) {
        const privateContainer = $('#private-collections');
        const publicContainer = $('#public-collections');

        // Handle empty collections
        if (privateCollections.length === 0) {
            privateContainer.html('<div class="col-12"><p class="text-center text-muted">No private collections found</p></div>');
        } else {
            privateContainer.html(privateCollections
                .map(collection => createCollectionCard(collection))
                .join(''));
        }

        if (publicCollections.length === 0) {
            publicContainer.html('<div class="col-12"><p class="text-center text-muted">No public collections found</p></div>');
        } else {
            publicContainer.html(publicCollections
                .map(collection => createCollectionCard(collection))
                .join(''));
        }

        // Update collections count in header
        updateCollectionsCount(privateCollections.length + publicCollections.length);
    }

    function showErrorMessage(message) {
        // Add this HTML to your page for the error message
        const errorDiv = $('#error-message');
        if (errorDiv.length) {
            errorDiv.text(message).fadeIn().delay(3000).fadeOut();
        }
    }

    function updateCollectionsCount(count) {
        // Update the collections count in the header
        $('.header-text h2').html(`Collect<em>ions</em> (${count})`);
    }
});