$(document).ready(function () {
    var currentPage = 1;
    var limit = 12;
    var isLoading = false;

    function loadUsers(page) {
        if (isLoading) return;
        isLoading = true;
        currentPage = page; // Update currentPage immediately when function is called

        $.ajax({
            url: 'scripts/data-scripts/members.data.php',
            data: { page: page, limit: limit },
            dataType: 'json',
            success: function (response) {
                console.log("Response:", response);
                var userList = $('.members_card_gen');
                userList.html(""); // Clear existing content

                if (!response.data || response.data.length === 0) {
                    userList.html("<p>No users found</p>");
                    isLoading = false;
                    return;
                }

                // Iterate through the fetched users and append them to the list
                $.each(response.data, function (index, user) {
                    var dateString = user.registration_date;
                    var date = new Date(dateString);
                    var formattedDate = date.toLocaleString('en-US', { month: 'long', day: 'numeric', year: 'numeric' });

                    userList.append(`
                        <div class="col-md-3" id="members">
                            <div class="profile-card">
                                <div class="card-content">
                                    <div class="avatar-wrapper">
                                        <div class="avatar">
                                            ${user.avatar ?
                                               
                                                `<img class="img-circle" src="assets/images/${user.avatar}" alt="User Avatar">` :
                                                `<i class="fas fa-user"></i>`
                                            }
                                            <div class="avatar-glow"></div>
                                        </div>
                                    </div>
                                    <div class="profile-info">
                                        <h2 class="name">${(user.firstname && user.lastname) ? 
                                        `${user.firstname} ${user.lastname}` : 
                                        user.username}</h2>
                                        <p class="country">${user.country_name}</p>
                                        <div class="stats">
                                            <div class="stat">
                                                <span class="stat-value">${user.number_of_followers}</span>
                                                <span class="stat-label">Followers</span>
                                            </div>
                                            <div class="stat">
                                                <span class="stat-value">${user.number_of_images}</span>
                                                <span class="stat-label">Uploads</span>
                                            </div>
                                        </div>
                                        <div class="bio">Members since ${formattedDate}</div>
                                        <div class="d-grid">
                                            <a class="action-btn" href="profile.php?user_id=${user.username}">
                                                <span>See profile</span>
                                                <i class="fas fa-arrow-right"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-shine"></div>
                            </div>
                        </div>
                    `);
                });

                // Generate pagination buttons
                updatePagination(response.pagination);
                isLoading = false;
            },
            error: function () {
                console.log('Error occurred while fetching users.');
                isLoading = false;
            }
        });
    }

    function updatePagination(pagination) {
        // Remove existing pagination before adding new ones
        $('.pagination-container').remove();
        
        var paginationHtml = '<div class="pagination-container">';
        
        // Previous button
        paginationHtml += `<button class="page-btn" 
            ${currentPage === 1 ? 'disabled' : ''} 
            data-page="${currentPage - 1}">
            <i class="fas fa-chevron-left"></i> Previous
        </button>`;

        // Numbered buttons
        for (let i = 1; i <= pagination.totalPages; i++) {
            paginationHtml += `<button class="page-btn ${i === currentPage ? 'active' : ''}" 
                data-page="${i}">${i}</button>`;
        }

        // Next button
        paginationHtml += `<button class="page-btn" 
            ${currentPage === pagination.totalPages ? 'disabled' : ''} 
            data-page="${currentPage + 1}">
            Next <i class="fas fa-chevron-right"></i>
        </button>`;

        paginationHtml += '</div>';

        // Add pagination after the user list
        $('.members_card_gen').after(paginationHtml);

        // Add click handlers to pagination buttons
        $('.page-btn').click(function(e) {
            e.preventDefault(); // Prevent default button behavior
            if (!$(this).prop('disabled')) {
                let page = $(this).data('page');
                loadUsers(page);
                // Scroll to top of results
                $('.members_card_gen')[0].scrollIntoView({ behavior: 'smooth' });
            }
        });
    }

    // Initial load
    loadUsers(1);

    // Remove scroll event listener since we're using buttons
    $(window).off('scroll');

    // Filter button click event
    // $("#member_FilterBtn").click(function (e) {
    //     e.preventDefault();

    //     var country = $("#country").val();
    //     var uname = $("#members_id").val();

    //     if (country == "" && uname == "") {
    //         // Do nothing if both fields are empty
    //     } else {
    //         console.log(country + " - " + uname);

    //         $.ajax({
    //             url: 'scripts/data-scripts/members.data.php',
    //             data: { country: country, uname: uname },
    //             dataType: 'json',
    //             success: function (response) {
    //                 console.log(response);
    //                 var userList = $('.members_card_gen');
    //                 userList.html(""); // Clear existing content

    //                 // Iterate through the fetched users and append them to the list
    //                 $.each(response, function (index, user) {
    //                     var dateString = user.date;
    //                     var date = new Date(dateString);
    //                     var formattedDate = date.toLocaleString('en-US', { month: 'long', day: 'numeric', year: 'numeric' });

    //                     userList.append(`
    //                         <div class="col-md-4">
    //                             <div class="box box-widget widget-user">
    //                                 <div class="widget-user-header bg-aqua">
    //                                     <h3 class="widget-user-username">${user.username}</h3>
    //                                     <h5 class="widget-user-desc">${user.country}</h5>
    //                                 </div>
    //                                 <div class="widget-user-image">
    //                                     ${user.avatar ?
    //                                         `<img class="img-circle" src="assets/images/${user.avatar}" alt="User Avatar">` :
    //                                         `<i class="fas fa-user"></i>`
    //                                     }
    //                                 </div>
    //                                 <div class="box-footer">
    //                                     <div class="row">
    //                                         <div class="col-sm-6 border-right">
    //                                             <div class="description-block">
    //                                                 <h5 class="description-header">${user.followers}</h5>
    //                                                 <span class="description-text">FOLLOWERS</span>
    //                                             </div>
    //                                         </div>
    //                                         <div class="col-sm-6">
    //                                             <div class="description-block">
    //                                                 <h5 class="description-header">${user.images}</h5>
    //                                                 <span class="description-text">Photos</span>
    //                                             </div>
    //                                         </div>
    //                                     </div>
    //                                     <div>
    //                                         <h5 class="description-footer">${formattedDate}</h5>
    //                                     </div>
    //                                 </div>
    //                             </div>
    //                         </div>
    //                     `);
    //                 });
    //             },
    //             error: function () {
    //                 console.log('Error occurred while fetching data.');
    //             }
    //         });
    //     }
    // });

    // Load countries for the filter dropdown
    $.ajax({
        url: 'scripts/data-scripts/members.data.php',
        data: { value: "countries" },
        dataType: 'json',
        success: function (response) {
            if (response.status === 'success') {
                var country = $('#country');
                console.log(response.data);

                // Append countries to the dropdown
                $.each(response.data, function (i, coun) {
                    
                    country.append(`
                        <option value="${coun.country_code}">${coun.country_name}</option>
                    `);
                });
            } else {
                console.log('Error:', response.message);
            }
        },
        error: function () {
            console.log('Error occurred while fetching country.');
        }
    });
});