<?php
header("Content-Type: application/json");
// session_start();
if (!isset($_SESSION)) {
    session_start();
}

include '../class-scripts/db-connection.class.php';
include '../class-scripts/user_profile.class.php';


$UserProfile_Obj = new UserProfile_Section();

if ($_SERVER["REQUEST_METHOD"] == "GET") {
    if (isset($_GET["user_id"])) {

        $func_userid = $_GET["user_id"];
        if ($func_userid == 0) {
            if (isset($_SESSION["id"])) {
                $func_userid = $_SESSION["id"];
            } else {
                return;
            }
        }
        echo $UserProfile_Obj->Profile_View($func_userid);
    }
}


if (isset($_POST["image_src"])) {

    $func_userid = $_SESSION["id"];
    $avatar = $_POST["image_src"];
    $UserProfile_Obj->Profile_Picture($func_userid, $avatar);
}


if (isset($_POST["fetch_liked_images"])) {
    $func_userid = $_POST["user_id"];
    $offset = $_POST["offset"];

    $UserProfile_Obj->fetchLikedImages($func_userid, $offset);
}

//coper emmanuel worked here
if (isset($_POST["fetch_purchased_images"])) {
    $func_userid = $_POST["user_id"];
    $offset = $_POST["offset"];
    $UserProfile_Obj->fetchPurchasedImages($func_userid, $offset);
}

if (isset($_POST["fetch_bookmarkers"])) {
    $func_userid = $_POST["user_id"];
    $offset = $_POST["offset"];
    $UserProfile_Obj->fetchBookmarkers($func_userid, $offset);
}

if (isset($_POST["fetch_own_images"])) {
    $func_userid = $_POST["user_id"];
    $offset = $_POST["offset"];

    $UserProfile_Obj->fetchOwnImages($func_userid, $offset);
}

if (isset($_POST["fetch_bookmarks"])) {
    $func_userid = $_POST["user_id"];
    //$offset = $_POST["offset"];

    $UserProfile_Obj->fetch_bookmarks($func_userid);
}

// Add this to handle avatar removal
if (isset($_POST["remove_avatar"])) {
    $func_userid = $_SESSION["id"];

    // Get current avatar filename
    $sql = "SELECT avatar FROM users WHERE id = '$func_userid'";
    $result = mysqli_query($UserProfile_Obj->connect(), $sql);

    if ($result && $result->num_rows > 0) {
        $row = mysqli_fetch_assoc($result);
        $current_avatar = $row["avatar"];

        // Delete file if it exists
        if (!empty($current_avatar)) {
            $filepath = "../../assets/images/" . $current_avatar;
            if (file_exists($filepath)) {
                unlink($filepath);
            }
        }

        // Update database to remove avatar reference
        $sql = "UPDATE users SET avatar = '' WHERE id = '$func_userid'";
        $update_result = mysqli_query($UserProfile_Obj->connect(), $sql);

        if ($update_result) {
            echo json_encode(array("status" => 1, "message" => "Avatar removed successfully"));
        } else {
            echo json_encode(array("status" => 0, "message" => "Database error"));
        }
    } else {
        echo json_encode(array("status" => 0, "message" => "User not found"));
    }
}
