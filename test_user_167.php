<?php
// session_start();
// include 'resources/db_config/db_connection.php';
// include 'scripts/class-scripts/user.class.php';

// echo "<h2>Testing Purchase History for User 167</h2>";

// // Set session to user 167 for testing
// $_SESSION['id'] = 167;
// $_SESSION['username'] = 'test_user_167';

// echo "<p>Testing with User ID: 167</p>";

// // Test the User class method directly
// $userObj = new User();

// echo "<h3>Testing fetchPurchaseHistories method</h3>";

// // Capture the output
// ob_start();
// $userObj->fetchPurchaseHistories(167, 0, '', '', '');
// $output = ob_get_clean();

// echo "<p><strong>Raw output from User class:</strong></p>";
// echo "<pre style='background: #f0f0f0; padding: 10px; max-height: 400px; overflow-y: scroll;'>" . htmlspecialchars($output) . "</pre>";

// // Parse the JSON
// $data = json_decode($output, true);
// if ($data) {
//     echo "<p><strong>Parsed JSON data:</strong></p>";
//     echo "<pre>" . print_r($data, true) . "</pre>";

//     if (isset($data['purchase_histories']) && !empty($data['purchase_histories'])) {
//         echo "<p>✅ Found " . count($data['purchase_histories']) . " purchase records</p>";

//         echo "<h4>Purchase Records:</h4>";
//         echo "<table border='1' style='border-collapse: collapse;'>";
//         echo "<tr><th>Type</th><th>Item Name</th><th>Item ID</th><th>Amount</th><th>Date</th><th>Status</th></tr>";

//         foreach ($data['purchase_histories'] as $purchase) {
//             echo "<tr>";
//             echo "<td>" . $purchase['purchase_type'] . "</td>";
//             echo "<td>" . $purchase['item_name'] . "</td>";
//             echo "<td>" . $purchase['item_id'] . "</td>";
//             echo "<td>₦" . $purchase['amount'] . "</td>";
//             echo "<td>" . $purchase['purchase_date'] . "</td>";
//             echo "<td>" . $purchase['status'] . "</td>";
//             echo "</tr>";
//         }
//         echo "</table>";
//     } else {
//         echo "<p>❌ No purchase records found in the response</p>";
//     }
// } else {
//     echo "<p>❌ Failed to parse JSON response</p>";
//     echo "<p>JSON Error: " . json_last_error_msg() . "</p>";
// }

// // Test direct database query
// echo "<h3>Direct Database Query Test</h3>";

// $sql = "SELECT ph.*, 
//         COALESCE(i.title, CONCAT('Image ID: ', ph.img_id)) as title, 
//         COALESCE(i.tags, 'N/A') as tags, 
//         'image' as purchase_type, 
//         COALESCE(ph.payment_status, ph.status, 1) as status,
//         ph.Date_created as purchase_date
//     FROM puchase_history ph 
//     LEFT JOIN images i ON ph.img_id = i.id 
//     WHERE ph.user_id = 167
//     ORDER BY ph.Date_created DESC";

// echo "<p><strong>SQL Query:</strong></p>";
// echo "<pre>" . htmlspecialchars($sql) . "</pre>";

// $result = $db->query($sql);
// if ($result) {
//     echo "<p>✅ Query executed successfully. Found " . $result->num_rows . " rows</p>";

//     if ($result->num_rows > 0) {
//         echo "<table border='1' style='border-collapse: collapse;'>";

//         // Headers
//         $fields = $result->fetch_fields();
//         echo "<tr>";
//         foreach ($fields as $field) {
//             echo "<th>" . $field->name . "</th>";
//         }
//         echo "</tr>";

//         // Data
//         $result->data_seek(0);
//         while ($row = $result->fetch_assoc()) {
//             echo "<tr>";
//             foreach ($row as $value) {
//                 echo "<td>" . htmlspecialchars($value) . "</td>";
//             }
//             echo "</tr>";
//         }
//         echo "</table>";
//     }
// } else {
//     echo "<p>❌ Query failed: " . $db->error . "</p>";
// }

// // Check error logs for any issues
// echo "<h3>Recent Error Logs</h3>";
// $log_file = ini_get('error_log');
// if ($log_file && file_exists($log_file)) {
//     $logs = file_get_contents($log_file);
//     $lines = explode("\n", $logs);
//     $recent_lines = array_slice($lines, -20);

//     echo "<pre style='background: #f0f0f0; padding: 10px; max-height: 200px; overflow-y: scroll;'>";
//     foreach ($recent_lines as $line) {
//         if (stripos($line, 'Purchase History') !== false || stripos($line, 'SQL') !== false) {
//             echo htmlspecialchars($line) . "\n";
//         }
//     }
//     echo "</pre>";
// }

?>

<!-- <p><a href="account-settings.php">Go to Account Settings</a></p> -->


<?php
session_start();
include 'resources/db_config/db_connection.php';
include 'scripts/class-scripts/user.class.php';

echo "<h2>Testing Purchase History for User 167</h2>";

// Set session to user 167 for testing
$_SESSION['id'] = 167;
$_SESSION['username'] = 'test_user_167';

echo "<p>Testing with User ID: 167</p>";

// Test the User class method directly
$userObj = new User();

echo "<h3>Testing fetchPurchaseHistories method</h3>";

// Capture the output
ob_start();
$userObj->fetchPurchaseHistories(167, 0, '', '', '');
$output = ob_get_clean();

echo "<p><strong>Raw output from User class:</strong></p>";
echo "<pre style='background: #f0f0f0; padding: 10px; max-height: 400px; overflow-y: scroll;'>" . htmlspecialchars($output) . "</pre>";

// Parse the JSON
$data = json_decode($output, true);
if ($data) {
    echo "<p><strong>Parsed JSON data:</strong></p>";
    echo "<pre>" . print_r($data, true) . "</pre>";

    if (isset($data['purchase_histories']) && !empty($data['purchase_histories'])) {
        echo "<p>✅ Found " . count($data['purchase_histories']) . " purchase records</p>";

        echo "<h4>Purchase Records:</h4>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Type</th><th>Item Name</th><th>Item ID</th><th>Amount</th><th>Date</th><th>Status</th></tr>";

        foreach ($data['purchase_histories'] as $purchase) {
            echo "<tr>";
            echo "<td>" . $purchase['purchase_type'] . "</td>";
            echo "<td>" . $purchase['item_name'] . "</td>";
            echo "<td>" . $purchase['item_id'] . "</td>";
            echo "<td>₦" . $purchase['amount'] . "</td>";
            echo "<td>" . $purchase['purchase_date'] . "</td>";
            echo "<td>" . $purchase['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ No purchase records found in the response</p>";
    }
} else {
    echo "<p>❌ Failed to parse JSON response</p>";
    echo "<p>JSON Error: " . json_last_error_msg() . "</p>";
}

// Test direct database query
echo "<h3>Direct Database Query Test</h3>";

$sql = "SELECT ph.*, 
        COALESCE(i.title, CONCAT('Image ID: ', ph.img_id)) as title, 
        COALESCE(i.tags, 'N/A') as tags, 
        'image' as purchase_type, 
        COALESCE(ph.payment_status, ph.status, 1) as status,
        ph.Date_created as purchase_date
    FROM puchase_history ph 
    LEFT JOIN images i ON ph.img_id = i.id 
    WHERE ph.user_id = 167
    ORDER BY ph.Date_created DESC";

echo "<p><strong>SQL Query:</strong></p>";
echo "<pre>" . htmlspecialchars($sql) . "</pre>";

$result = $db->query($sql);
if ($result) {
    echo "<p>✅ Query executed successfully. Found " . $result->num_rows . " rows</p>";

    if ($result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";

        // Headers
        $fields = $result->fetch_fields();
        echo "<tr>";
        foreach ($fields as $field) {
            echo "<th>" . $field->name . "</th>";
        }
        echo "</tr>";

        // Data
        $result->data_seek(0);
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    }
} else {
    echo "<p>❌ Query failed: " . $db->error . "</p>";
}

// Check error logs for any issues
echo "<h3>Recent Error Logs</h3>";
$log_file = ini_get('error_log');
if ($log_file && file_exists($log_file)) {
    $logs = file_get_contents($log_file);
    $lines = explode("\n", $logs);
    $recent_lines = array_slice($lines, -20);

    echo "<pre style='background: #f0f0f0; padding: 10px; max-height: 200px; overflow-y: scroll;'>";
    foreach ($recent_lines as $line) {
        if (stripos($line, 'Purchase History') !== false || stripos($line, 'SQL') !== false) {
            echo htmlspecialchars($line) . "\n";
        }
    }
    echo "</pre>";
}

?>

<p><a href="account-settings.php">Go to Account Settings</a></p>