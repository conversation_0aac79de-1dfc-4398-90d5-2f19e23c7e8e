// $(document).ready(function() {
//     let cartTotal = 0;
//     let userEmail = '';
//     let userId = 0;

//     // Get user information
//     $.post("scripts/data-scripts/auth-status.data.php", {
//         request_type: 'auth-session-check'
//     }, function(data) {
//         if (data["message"] == "success" && data["authenticated"] === true) {
//             userEmail = data["email"] || '';
//             userId = data["user_id"] || 0;

//             console.log("User authenticated:", { userEmail, userId });

//             // Enable purchase button if user is authenticated and cart has items
//             updatePurchaseButton();
//         } else {
//             // User not authenticated
//             $("#make_purchase_btn").prop("disabled", true).text("Please Login to Purchase");
//             console.log("User not authenticated");
//         }
//     }).fail(function() {
//         console.error("Failed to check authentication status");
//         $("#make_purchase_btn").prop("disabled", true).text("Authentication Error");
//     });

//     // Get cart total
//     function updateCartTotal() {
//         $.get("scripts/data-scripts/get-cart-total.data.php", function(return_data) {
//             if (return_data.status == 1) {
//                 const basePrice = return_data.image_price;
//                 const tax = return_data.image_tax;
//                 const finalPrice = basePrice + (basePrice * (tax / 100));

//                 cartTotal = finalPrice;

//                 console.log("Cart total updated:", {
//                     basePrice,
//                     tax,
//                     finalPrice: cartTotal
//                 });

//                 updatePurchaseButton();
//             } else {
//                 cartTotal = 0;
//                 $("#make_purchase_btn").prop("disabled", true).text("Cart is Empty");
//             }
//         }).fail(function() {
//             console.error("Failed to get cart total");
//             $("#make_purchase_btn").prop("disabled", true).text("Error Loading Cart");
//         });
//     }

//     // Update purchase button state
//     function updatePurchaseButton() {
//         const button = $("#make_purchase_btn");

//         if (!userEmail || userId === 0) {
//             button.prop("disabled", true).text("Please Login to Purchase");
//             return;
//         }

//         if (cartTotal <= 0) {
//             button.prop("disabled", true).text("Cart is Empty");
//             return;
//         }

//         // Enable button and set up click handler
//         button.prop("disabled", false)
//               .text(`Make Purchase (₦${cartTotal.toFixed(2)})`)
//               .removeClass("disabled")
//               .off('click')
//               .on('click', function(e) {
//                   e.preventDefault();
//                   handlePurchase();
//               });
//     }

//     // Handle purchase button click
//     function handlePurchase() {
//         console.log("Purchase initiated:", {
//             cartTotal,
//             userEmail,
//             userId
//         });

//         // Validate before proceeding
//         if (!cartTotal || cartTotal <= 0) {
//             alert("Your cart is empty. Please add items before purchasing.");
//             return;
//         }

//         if (!userEmail) {
//             alert("Please login to make a purchase.");
//             window.location.href = "signup.php?login";
//             return;
//         }

//         // Check if cart_payment function exists
//         if (typeof cart_payment !== 'function') {
//             alert("Payment system not available. Please refresh the page and try again.");
//             return;
//         }

//         // Disable button to prevent double clicks
//         $("#make_purchase_btn").prop("disabled", true).text("Processing...");

//         // Call the cart payment function
//         try {
//             cart_payment(cartTotal, userEmail, userId);
//         } catch (error) {
//             console.error("Payment error:", error);
//             alert("Payment failed to initialize. Please try again.");
//             $("#make_purchase_btn").prop("disabled", false).text(`Make Purchase (₦${cartTotal.toFixed(2)})`);
//         }
//     }

//     // Initialize cart total on page load
//     updateCartTotal();

//     // Update cart total when items are removed
//     $(document).on('cartUpdated', function() {
//         console.log("Cart updated event received");
//         updateCartTotal();
//     });

//     // Listen for promo code applications
//     $("#promo_redeem_btn").on('click', function() {
//         // Wait a bit for promo to be applied, then update total
//         setTimeout(function() {
//             updateCartTotal();
//         }, 1000);
//     });
// });

$(document).ready(function () {
  let cartTotal = 0;
  let userEmail = "";
  let userId = 0;

  // Get user information
  $.post(
    "scripts/data-scripts/auth-status.data.php",
    {
      request_type: "auth-session-check",
    },
    function (data) {
      if (data["message"] == "success" && data["authenticated"] === true) {
        userEmail = data["email"] || "";
        userId = data["user_id"] || 0;

        console.log("User authenticated:", { userEmail, userId });

        // Enable purchase button if user is authenticated and cart has items
        updatePurchaseButton();
      } else {
        // User not authenticated
        $("#make_purchase_btn")
          .prop("disabled", true)
          .text("Please Login to Purchase");
        console.log("User not authenticated");
      }
    }
  ).fail(function () {
    console.error("Failed to check authentication status");
    $("#make_purchase_btn").prop("disabled", true).text("Authentication Error");
  });

  // Get cart total
  function updateCartTotal() {
    $.get(
      "scripts/data-scripts/get-cart-total.data.php",
      function (return_data) {
        if (return_data.status == 1) {
          const basePrice = return_data.image_price;
          const tax = return_data.image_tax;
          const finalPrice = basePrice + basePrice * (tax / 100);

          cartTotal = finalPrice;

          console.log("Cart total updated:", {
            basePrice,
            tax,
            finalPrice: cartTotal,
          });

          updatePurchaseButton();
        } else {
          cartTotal = 0;
          $("#make_purchase_btn").prop("disabled", true).text("Cart is Empty");
        }
      }
    ).fail(function () {
      console.error("Failed to get cart total");
      $("#make_purchase_btn").prop("disabled", true).text("Error Loading Cart");
    });
  }

  // Update purchase button state
  function updatePurchaseButton() {
    const button = $("#make_purchase_btn");

    if (!userEmail || userId === 0) {
      button.prop("disabled", true).text("Please Login to Purchase");
      return;
    }

    if (cartTotal <= 0) {
      button.prop("disabled", true).text("Cart is Empty");
      return;
    }

    // Enable button and set up click handler
    button
      .prop("disabled", false)
      .text(`Make Purchase (₦${cartTotal.toFixed(2)})`)
      .removeClass("disabled")
      .off("click")
      .on("click", function (e) {
        e.preventDefault();
        handlePurchase();
      });
  }

  // Handle purchase button click
  function handlePurchase() {
    console.log("Purchase initiated:", {
      cartTotal,
      userEmail,
      userId,
    });

    // Validate before proceeding
    if (!cartTotal || cartTotal <= 0) {
      alert("Your cart is empty. Please add items before purchasing.");
      return;
    }

    if (!userEmail) {
      alert("Please login to make a purchase.");
      window.location.href = "signup.php?login";
      return;
    }

    // Check if cart_payment function exists
    if (typeof cart_payment !== "function") {
      alert(
        "Payment system not available. Please refresh the page and try again."
      );
      return;
    }

    // Disable button to prevent double clicks
    $("#make_purchase_btn").prop("disabled", true).text("Processing...");

    // Call the cart payment function
    try {
      cart_payment(cartTotal, userEmail, userId);
    } catch (error) {
      console.error("Payment error:", error);
      alert("Payment failed to initialize. Please try again.");
      $("#make_purchase_btn")
        .prop("disabled", false)
        .text(`Make Purchase (₦${cartTotal.toFixed(2)})`);
    }
  }

  // Initialize cart total on page load
  updateCartTotal();

  // Update cart total when items are removed
  $(document).on("cartUpdated", function () {
    console.log("Cart updated event received");
    updateCartTotal();
  });

  // Listen for promo code applications
  $("#promo_redeem_btn").on("click", function () {
    // Wait a bit for promo to be applied, then update total
    setTimeout(function () {
      updateCartTotal();
    }, 1000);
  });
});
