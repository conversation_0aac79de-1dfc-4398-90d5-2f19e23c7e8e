$(document).ready(function () {
  //Run Codes When Document Loads Completely

  let rowsOffsetTrack = [];
  let initApiCallMade = false;
  let activePageIndex = 1;
  let searchKeyword = "";
  let startDateFilter = "";
  let endDateFilter = "";
  let statusFilter = "";
  let allPlansData = []; // Store all plans for client-side filtering

  callUserPlansApi(0, "", "", "");

  function callUserPlansApi(
    offset,
    searchphrase,
    startdatefilter,
    enddatefilter
  ) {
    // ! show loader Icon
    enableLoader();

    const payload = {
      sql_offset: offset,
      search_phrase: searchphrase,
      start_date: startdatefilter,
      end_date: enddatefilter,
    };

    $.post(
      "scripts/data-scripts/user-plans.data.php",
      payload,
      function (return_data) {
        // assign Callback Function Return Data to Variable for Usability
        let return_data_value = return_data;

        if (return_data_value["message"] != "error") {
          if (return_data_value["message"] != "no result") {
            // Store all plans data for client-side filtering
            if (offset === 0) {
              allPlansData = return_data_value["plans"];
            }

            $("#plans-no-results-msg").css("display", "none");
            $("#user-plans-table").css("display", "block");
            $("#plans-table-pagination").css("display", "block");

            // Apply client-side filtering
            const filteredPlans = applyClientSideFilters(
              return_data_value["plans"]
            );
            createPlansViewByLoop(filteredPlans);

            // Update current plan summary
            updateCurrentPlanSummary(return_data_value["plans"]);

            if (initApiCallMade == false) {
              setupPaginationControls(
                return_data_value["pagination_configurations"]["row_set"],
                return_data_value["pagination_configurations"]["page_divisions"]
              );
              initApiCallMade = true;
            }
          } else {
            // ! no results available
            $("#plans-no-results-msg").css("display", "block");
            $("#user-plans-table").css("display", "none");
            $("#plans-table-pagination").css("display", "none");
          }
        } else {
          // ! server error
          console.error("Server error fetching plans");
        }

        disableLoader();
      }
    );
  }

  // function createPlansViewByLoop(data) {
  //    let planItem = '';

  //     data.forEach(element => {

  //         // <tr class="active-row plan-row"> // ! to highlight a row
  //         planItem += `<tr class='${element['status'] == 'Active' ? 'active-row plan-row' : ''}'>
  //             <td>${element['plan_name']}</td>
  //             <td>${element['amount']}</td>
  //             <td>${element['expiry_date']}</td>
  //             <td>${element['status']}</td>
  //         </tr>`;
  //     });

  //     $("#user-plans-view").html(planItem);
  // }

  function createPlansViewByLoop(data) {
    let planItem = "";

    // Sort plans to show active ones first
    data.sort((a, b) => {
      if (a.status === "Active" && b.status !== "Active") return -1;
      if (a.status !== "Active" && b.status === "Active") return 1;
      // If both have same status, sort by expiry date (newest first)
      return new Date(b.expiry_date) - new Date(a.expiry_date);
    });

    data.forEach((element) => {
      // Use the status from the server response
      const isActive = element["status"] === "Active";

      // Set status class and styling based on active status
      const statusClass = isActive ? "active-row plan-row" : "";
      const statusBadge = isActive
        ? '<span class="badge badge-success">Active</span>'
        : '<span class="badge badge-secondary">Expired</span>';

      // Format the amount with currency
      const formattedAmount = `₦${parseFloat(
        element["amount"]
      ).toLocaleString()}`;

      // Format dates
      const formattedExpiryDate = new Date(
        element["expiry_date"]
      ).toLocaleDateString();

      planItem += `<tr class='${statusClass}'>
            <td><strong>${element["plan_name"]}</strong></td>
            <td>${formattedAmount}</td>
            <td>${formattedExpiryDate}</td>
            <td>${statusBadge}</td>
        </tr>`;
    });

    $("#user-plans-view").html(planItem);
  }
  function setupPaginationControls(rowsets, pagedivisions) {
    let paginationElement = "";

    rowsOffsetTrack.push(null);
    paginationElement += `<li class="page-item"><a class="page-link" style="cursor: pointer;">Previous</a></li>`;

    for (let i = 0; i < pagedivisions; i++) {
      rowsOffsetTrack.push(rowsets * i);
      paginationElement += `<li class="page-item"><a class="page-link" style="cursor: pointer;">${
        i + 1
      }</a></li>`;
    }

    rowsOffsetTrack.push(null);
    paginationElement += `<li class="page-item"><a class="page-link" style="cursor: pointer;">Next</a></li>`;

    $("#plans-table-pagination").html(paginationElement);

    // console.log(rowsOffsetTrack);

    // ---- // ---- add event handlers for each dynamically generated elements
    const linkElements = document.querySelectorAll(
      "#plans-table-pagination li"
    );

    linkElements.forEach((li, index) => {
      li.addEventListener("click", function () {
        if (this.textContent == "Previous") {
          if (activePageIndex - 1 >= 1) {
            callUserPlansApi(
              rowsOffsetTrack[activePageIndex - 1],
              searchKeyword,
              startDateFilter,
              endDateFilter
            );
            console.log(activePageIndex - 1);
            activePageIndex = activePageIndex - 1;
          } else {
            // alert('no more previous results');
          }
        } else if (this.textContent == "Next") {
          if (activePageIndex + 1 <= pagedivisions) {
            callUserPlansApi(
              rowsOffsetTrack[activePageIndex + 1],
              searchKeyword,
              startDateFilter,
              endDateFilter
            );
            console.log(activePageIndex + 1);
            activePageIndex = activePageIndex + 1;
          } else {
            // alert('no more result');
          }
        } else {
          callUserPlansApi(
            rowsOffsetTrack[index],
            searchKeyword,
            startDateFilter,
            endDateFilter
          );
          activePageIndex = index;
        }
      });
    });
  }

  $("#plans-filter-btn").on("click", function () {
    initApiCallMade = false;
    searchKeyword = $("#plans-search-filter").val();
    startDateFilter = $("#plans-starting-date-filter").val();
    endDateFilter = $("#plans-ending-date-filter").val();

    console.log("Applying filters:", {
      search: searchKeyword,
      startDate: startDateFilter,
      endDate: endDateFilter,
    });

    callUserPlansApi(0, searchKeyword, startDateFilter, endDateFilter);
  });

  // Add real-time search functionality
  $("#plans-search-filter").on("input", function () {
    const searchValue = $(this).val();
    searchKeyword = searchValue;
    applyAllFilters();
  });

  // Add status filter handler
  $("#plans-status-filter").on("change", function () {
    statusFilter = $(this).val();
    applyAllFilters();
  });

  // Add date filter change handlers
  $("#plans-starting-date-filter, #plans-ending-date-filter").on(
    "change",
    function () {
      startDateFilter = $("#plans-starting-date-filter").val();
      endDateFilter = $("#plans-ending-date-filter").val();
      applyAllFilters();
    }
  );

  $("#plans-clear-search").on("click", function (e) {
    e.preventDefault();
    clearSearch();
    // Reset all filters
    searchKeyword = "";
    startDateFilter = "";
    endDateFilter = "";
    statusFilter = "";
    $("#plans-starting-date-filter").val("");
    $("#plans-ending-date-filter").val("");
    $("#plans-status-filter").val("");
    $("#plans-search-filter").val("");

    // Show all plans
    if (allPlansData.length > 0) {
      createPlansViewByLoop(allPlansData);
    } else {
      callUserPlansApi(0, "", "", "");
    }
  });

  // Function to apply all filters
  function applyAllFilters() {
    if (allPlansData.length > 0) {
      const filteredPlans = applyClientSideFilters(allPlansData);
      createPlansViewByLoop(filteredPlans);

      if (filteredPlans.length === 0) {
        $("#plans-no-results-msg").css("display", "block");
        $("#user-plans-table").css("display", "none");
      } else {
        $("#plans-no-results-msg").css("display", "none");
        $("#user-plans-table").css("display", "block");
      }
    } else {
      callUserPlansApi(0, searchKeyword, startDateFilter, endDateFilter);
    }
  }

  // Client-side filtering function
  function applyClientSideFilters(plans) {
    return plans.filter((plan) => {
      // Search filter
      if (
        searchKeyword &&
        !plan.plan_name.toLowerCase().includes(searchKeyword.toLowerCase())
      ) {
        return false;
      }

      // Status filter
      if (statusFilter && plan.status !== statusFilter) {
        return false;
      }

      // Date filters
      if (startDateFilter || endDateFilter) {
        const planStartDate = new Date(plan.start_date || plan.expiry_date);

        if (startDateFilter) {
          const filterStartDate = new Date(startDateFilter);
          if (planStartDate < filterStartDate) {
            return false;
          }
        }

        if (endDateFilter) {
          const filterEndDate = new Date(endDateFilter);
          if (planStartDate > filterEndDate) {
            return false;
          }
        }
      }

      return true;
    });
  }

  // Reusable view functions
  function disableLoader() {
    $("#user-plans-loader").css("display", "none");
  }
  function enableLoader() {
    $("#user-plans-loader").css("display", "block");
  }
  function clearSearch() {
    $("#plans-no-results-msg").css("display", "none");
    $("#user-plans-table").css("display", "block");
    $("#plans-table-pagination").css("display", "block");
    $("#plans-search-filter").val("");
  }

  // Function to update current plan summary
  function updateCurrentPlanSummary(plans) {
    // Find the active plan
    const activePlan = plans.find((plan) => plan.status === "Active");

    if (activePlan) {
      // Show current plan summary
      $("#current-plan-summary").show();
      $("#no-active-plan-message").hide();

      // Populate plan details
      $("#current-plan-name").text(activePlan.plan_name);
      $("#current-plan-amount").text(
        `₦${parseFloat(activePlan.amount).toLocaleString()}`
      );
      $("#current-plan-expiry").text(
        new Date(activePlan.expiry_date).toLocaleDateString()
      );
    } else {
      // Show no active plan message
      $("#current-plan-summary").hide();
      $("#no-active-plan-message").show();
    }
  }
});
