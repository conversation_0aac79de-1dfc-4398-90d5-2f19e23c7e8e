$(document).ready(function () {
  //Run Codes When Document Loads Completely

  let rowsOffsetTrack = [];
  let initApiCallMade = false;
  let activePageIndex = 1;
  let searchKeyword = "";
  let startDateFilter = "";
  let endDateFilter = "";

  callUserPlansApi(0, "", "", "");

  function callUserPlansApi(
    offset,
    searchphrase,
    startdatefilter,
    enddatefilter
  ) {
    // ! show loader Icon
    enableLoader();

    const payload = {
      sql_offset: offset,
      search_phrase: searchphrase,
      start_date: startdatefilter,
      end_date: enddatefilter,
    };

    $.post(
      "scripts/data-scripts/user-plans.data.php",
      payload,
      function (return_data) {
        // assign Callback Function Return Data to Variable for Usability
        let return_data_value = return_data;

        if (return_data_value["message"] != "error") {
          if (return_data_value["message"] != "no result") {
            $("#plans-no-results-msg").css("display", "none");
            $("#user-plans-table").css("display", "block");
            $("#plans-table-pagination").css("display", "block");

            createPlansViewByLoop(return_data_value["plans"]);

            if (initApiCallMade == false) {
              setupPaginationControls(
                return_data_value["pagination_configurations"]["row_set"],
                return_data_value["pagination_configurations"]["page_divisions"]
              );
              initApiCallMade = true;
            }
          } else {
            // ! no results available
            $("#plans-no-results-msg").css("display", "block");
            $("#user-plans-table").css("display", "none");
            $("#plans-table-pagination").css("display", "none");
          }
        } else {
          // ! server error
        }

        disableLoader();
      }
    );
  }

  // function createPlansViewByLoop(data) {
  //    let planItem = '';

  //     data.forEach(element => {

  //         // <tr class="active-row plan-row"> // ! to highlight a row
  //         planItem += `<tr class='${element['status'] == 'Active' ? 'active-row plan-row' : ''}'>
  //             <td>${element['plan_name']}</td>
  //             <td>${element['amount']}</td>
  //             <td>${element['expiry_date']}</td>
  //             <td>${element['status']}</td>
  //         </tr>`;
  //     });

  //     $("#user-plans-view").html(planItem);
  // }

  function createPlansViewByLoop(data) {
    let planItem = "";

    data.forEach((element) => {
      // Check if the plan is active by comparing expiry date with current date
      const expiryDate = new Date(element["expiry_date"]);
      const currentDate = new Date();
      const isActive = expiryDate >= currentDate;

      // Set status class based on actual active status
      const statusClass = isActive ? "active-row plan-row" : "";
      const statusText = isActive ? "Active" : "Expired";

      planItem += `<tr class='${statusClass}'>
            <td>${element["plan_name"]}</td>
            <td>${element["amount"]}</td>
            <td>${element["expiry_date"]}</td>
            <td>${statusText}</td>
        </tr>`;
    });

    $("#user-plans-view").html(planItem);
  }
  function setupPaginationControls(rowsets, pagedivisions) {
    let paginationElement = "";

    rowsOffsetTrack.push(null);
    paginationElement += `<li class="page-item"><a class="page-link" style="cursor: pointer;">Previous</a></li>`;

    for (let i = 0; i < pagedivisions; i++) {
      rowsOffsetTrack.push(rowsets * i);
      paginationElement += `<li class="page-item"><a class="page-link" style="cursor: pointer;">${
        i + 1
      }</a></li>`;
    }

    rowsOffsetTrack.push(null);
    paginationElement += `<li class="page-item"><a class="page-link" style="cursor: pointer;">Next</a></li>`;

    $("#plans-table-pagination").html(paginationElement);

    // console.log(rowsOffsetTrack);

    // ---- // ---- add event handlers for each dynamically generated elements
    const linkElements = document.querySelectorAll(
      "#plans-table-pagination li"
    );

    linkElements.forEach((li, index) => {
      li.addEventListener("click", function () {
        if (this.textContent == "Previous") {
          if (activePageIndex - 1 >= 1) {
            callUserPlansApi(
              rowsOffsetTrack[activePageIndex - 1],
              searchKeyword,
              startDateFilter,
              endDateFilter
            );
            console.log(activePageIndex - 1);
            activePageIndex = activePageIndex - 1;
          } else {
            // alert('no more previous results');
          }
        } else if (this.textContent == "Next") {
          if (activePageIndex + 1 <= pagedivisions) {
            callUserPlansApi(
              rowsOffsetTrack[activePageIndex + 1],
              searchKeyword,
              startDateFilter,
              endDateFilter
            );
            console.log(activePageIndex + 1);
            activePageIndex = activePageIndex + 1;
          } else {
            // alert('no more result');
          }
        } else {
          callUserPlansApi(
            rowsOffsetTrack[index],
            searchKeyword,
            startDateFilter,
            endDateFilter
          );
          activePageIndex = index;
        }
      });
    });
  }

  $("#plans-filter-btn").on("click", function () {
    initApiCallMade = false;
    searchKeyword = $("#plans-search-filter").val();
    startDateFilter = $("#plans-starting-date-filter").val();
    endDateFilter = $("#plans-ending-date-filter").val();
    console.log(startDateFilter, endDateFilter);
    callUserPlansApi(0, searchKeyword, startDateFilter, endDateFilter);
  });

  $("#plans-clear-search").on("click", function (e) {
    e.preventDefault();

    clearSearch();
  });

  // Reusable view functions
  function disableLoader() {
    $("#user-plans-loader").css("display", "none");
  }
  function enableLoader() {
    $("#user-plans-loader").css("display", "block");
  }
  function clearSearch() {
    $("#plans-no-results-msg").css("display", "none");
    $("#user-plans-table").css("display", "block");
    $("#plans-table-pagination").css("display", "block");
    $("#plans-search-filter").val("");
  }
});
